package com.facishare.open.outer.oa.connector.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * MD5 工具类
 * 提供常用的 MD5 加密方法
 */
@Slf4j
public class MD5Utils {

    /**
     * 私有构造函数，防止实例化
     */
    private MD5Utils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 计算字符串的 MD5 值
     *
     * @param text 需要计算 MD5 的字符串
     * @return MD5 值
     */
    public static String getMD5(String text) {
        if (text == null) {
            return null;
        }
        return DigestUtils.md5Hex(text.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 计算文件的 MD5 值
     *
     * @param file 需要计算 MD5 的文件
     * @return MD5 值
     */
    public static String getFileMD5(File file) {
        if (file == null || !file.exists()) {
            return null;
        }
        try (FileInputStream fis = new FileInputStream(file)) {
            return DigestUtils.md5Hex(fis);
        } catch (IOException e) {
            log.error("计算文件MD5值失败", e);
            return null;
        }
    }

    /**
     * 计算字节数组的 MD5 值
     *
     * @param bytes 需要计算 MD5 的字节数组
     * @return MD5 值
     */
    public static String getBytesMD5(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        return DigestUtils.md5Hex(bytes);
    }

    /**
     * 验证字符串的 MD5 值是否匹配
     *
     * @param text 原始字符串
     * @param md5  MD5 值
     * @return 是否匹配
     */
    public static boolean verifyMD5(String text, String md5) {
        if (text == null || md5 == null) {
            return false;
        }
        return getMD5(text).equalsIgnoreCase(md5);
    }

    /**
     * 验证文件的 MD5 值是否匹配
     *
     * @param file 文件
     * @param md5  MD5 值
     * @return 是否匹配
     */
    public static boolean verifyFileMD5(File file, String md5) {
        if (file == null || md5 == null) {
            return false;
        }
        return getFileMD5(file).equalsIgnoreCase(md5);
    }

    /**
     * 计算字符串的 MD5 值（32位小写）
     *
     * @param text 需要计算 MD5 的字符串
     * @return 32位小写 MD5 值
     */
    public static String getMD5LowerCase(String text) {
        if (text == null) {
            return null;
        }
        return getMD5(text).toLowerCase();
    }

    /**
     * 计算字符串的 MD5 值（32位大写）
     *
     * @param text 需要计算 MD5 的字符串
     * @return 32位大写 MD5 值
     */
    public static String getMD5UpperCase(String text) {
        if (text == null) {
            return null;
        }
        return getMD5(text).toUpperCase();
    }
} 