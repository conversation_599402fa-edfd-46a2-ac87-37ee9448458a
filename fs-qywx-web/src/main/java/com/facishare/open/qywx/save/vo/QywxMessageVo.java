package com.facishare.open.qywx.save.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/25 10:39 存储着企信的信息
 * @Version 1.0
 */
@Data

public class QywxMessageVo implements Serializable {
    private Long id;
    private String fsEa;
    private String messageId;
    private Long seq;//也是企业唯一
    private Integer keyVersion;//密钥版本。解密数据需要依赖对应的版本
    private String fromUser;//消息 发送方id
    private String fromEncryptionUser;//发送方的密文id
    private List<String> toList;//消息接收方
    private List<String> toEncryptionList;//消息接收方的密文id
    private String roomId;//群聊消息的群id
    private Long messageTime;//消息发送时间戳
    private String messageType;//消息类型
    private String content;//消息内容。存储文本
    private String md5sum;//对于图片，视频.音频的作为名字
    private String sdkFileId;//资源id，解密资源需要
    private Long fileSize;
    private String npath;
    private String fileName;//文件名
    private String fileExt;//拓展名

}
