package com.facishare.open.qywx.accountbind.model;

import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/7/16.
 */
@Data
@Table(name = "enterprise_account_bind")
public class QyweixinAccountEnterpriseMapping implements Serializable {
    private static final long serialVersionUID = -1L;

    private int id;
    private String source; //渠道： qywx
    private String fsEa;   //纷享企业账号
    private String outEa;  //企业微信corpId
    private String depId; //企微一级部门ID，企微一对多使用
    private int status;    //0-正常 1-停用 100-纷享企业未创建时的中间状态
    private Integer bindType;  //0-新建企业并绑定 1-绑定已有的纷享企业
    private String outName; //企业微信名称
    private String isvOutEa; //代开发专用，存的是代开发的
    private Integer autRetention;//
    private Integer openAuthorization;//开启或关闭企业微信客户自动绑定
    private Integer autBind;//
    private String accountSyncConfig;//企业微信客户同步配置 810需求
    private Timestamp accountSyncConfigTime;//首次同步客户数据时间
    private Timestamp leadsSyncConfigTime;//首次同步线索时间
    private Timestamp contactSyncConfigTime;//首次同步联系人时间
    private String extend;//扩展字段
    private String domain;//企业域名
    private Timestamp gmtCreate;//创建时间
    private Timestamp gmtModified;//更新时间

    public boolean checkArg() {
        if(null == source || null == fsEa || null == outEa) {
            return false;
        }
        return true;
    }
}
