package com.facishare.open.qywx.web.notification;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.accountinner.service.NotificationService;
import com.facishare.open.qywx.accountinner.arg.SendTextNoticeArg;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
// IgnoreI18nFile
public class QyweixinMsgNotification {
    @Autowired
    private NotificationService notificationService;

    public void MsgNotification(String result) {
        if (StringUtils.isNotEmpty(result)) {
            JSONObject jsonObject = JSONObject.parseObject(result);
            String errcode = String.valueOf(jsonObject.get("errcode"));
            List<String> suiteTokenList = Lists.newArrayList("40082", "41022");
            if (StringUtils.isNotEmpty(errcode) && suiteTokenList.contains(errcode)) {
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle("企微suite_ticket缓存失效告警");
                String msg = String.format("企微suite_ticket缓存失效，请及时关注\n返回结果=%s", result);
                arg.setMsg(msg);
                log.info("QyweixinMsgNotification.MsgNotification,arg={}", arg);
                notificationService.sendQYWXNotice(arg);
            }

        }
    }
}
