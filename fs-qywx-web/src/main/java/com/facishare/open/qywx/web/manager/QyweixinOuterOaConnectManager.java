package com.facishare.open.qywx.web.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaAppInfoParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.admin.FeiShuConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.params.QyweixinAppInfoParams;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.db.dao.QyweixinAccountEnterpriseBindDao;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component("qywxOAConnectManager")
public class QyweixinOuterOaConnectManager implements OuterAbstractSettingService<QywxConnectorVo> {

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private ContactsService contactsService;

    private final ChannelEnum channel = ChannelEnum.qywx;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private QYWeixinManager qyWeixinManager;
    @Resource
    private OANewBaseManager oANewBaseManager;
    @Resource
    private QyweixinAccountEnterpriseBindDao qyweixinAccountEnterpriseBindDao;
    @Resource
    private EventBindSendMqManager eventBindSendMqManager;

    @Override
    public Result<Void> doValidateConfigAndSave(OuterOAConnectSettingResult outerOAConnectSettingResult, ChannelEnum channel,
                                                OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        log.info("doValidateConfigAndSave start: outerOAConnectSettingResult={}, channel={}, outerOaAppInfoTypeEnum={}",
                outerOAConnectSettingResult, channel, outerOaAppInfoTypeEnum);

        if (outerOAConnectSettingResult == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        QywxConnectorVo qywxConnectorVo = outerOAConnectSettingResult.getConnectParams().getQywx();

        String dataCenterId = outerOAConnectSettingResult.getCurrentDcId();
        qywxConnectorVo.setDataCenterId(dataCenterId);
        if (dataCenterId == null) {
            log.error("doValidateConfigAndSave failed: dataCenterId is null");
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        if (StringUtils.isEmpty(qywxConnectorVo.getQywxCorpId())) {
            log.error("doValidateConfigAndSave failed: corpId is null");
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        //企微corpId由明文查出服务商密文
        if (qywxConnectorVo.getQywxCorpId().length() < 32) {
            // id转换
            com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.corpId2OpenCorpId(qywxConnectorVo.getQywxCorpId());
            if (result.isSuccess() && StringUtils.isNotEmpty(result.getData())) {
                qywxConnectorVo.setQywxCorpId(result.getData());
            } else {
                log.error("doValidateConfigAndSave failed: corpId convert failed, corpId={}", qywxConnectorVo.getQywxCorpId());
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
            }
        }

        //判断是否存在其他类型的绑定
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(channel).outEa(qywxConnectorVo.getQywxCorpId())
                .build()).stream().filter(entity -> !Objects.equals(entity.getId(), dataCenterId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(enterpriseBindEntities) && !outerOaAppInfoTypeEnum.equals(OuterOaAppInfoTypeEnum.isv)) {
            boolean anyMatch = enterpriseBindEntities.stream().anyMatch(entity -> !entity.getBindType().equals(BindTypeEnum.manual) && entity.getBindStatus().equals(BindStatusEnum.normal));
            if (anyMatch) {
                log.error("doValidateConfigAndSave other app failed: corp anyMatch failed, corpId={}", qywxConnectorVo.getQywxCorpId());
                return Result.newError(ResultCodeEnum.QYWX_BIND_OTHER_APP_ERROR);
            }
        }

//        // 类型检查并转换
//        QywxConnectorVo qywxConnectorVo;
//        if (connectorVo instanceof QywxConnectorVo) {
//            qywxConnectorVo = (QywxConnectorVo) connectorVo;
//        } else {
//            log.error(
//                    "doValidateConfigAndSave failed: connectorVo is not a QywxConnectorVo instance, actual type={}",
//                    connectorVo.getClass().getName());
//            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
//        }

        // 检查部门
        if (StringUtils.isEmpty(qywxConnectorVo.getQywxDepartmentId())) {
            qywxConnectorVo.setQywxDepartmentId("1");
        }
        //isNumeric(String Str)就是提供解析是否为数字类型的方法，该方式只能校验不含负号“-”的数字，即输入一个负数-199，输出结果将是false
        if(!StringUtils.isNumeric(qywxConnectorVo.getQywxDepartmentId())) {
            log.info("doValidateConfigAndSave failed: departmentId is null");
            return new Result<>(ResultCodeEnum.QYWX_DEPARTMENT_ID_ERROR);
        }
        //只运行代开发应用
//        if (outerOaAppInfoTypeEnum != OuterOaAppInfoTypeEnum.serviceRepDev) {
//            return new Result<>(ResultCodeEnum.BIND_ENTERPRISE_ERROR.getCode(),ErrorRefer.REP_APP_NOT_ENABLE.getQywxCode(), null);
//        }

        //判断当前应用是否已被当前企业绑定
        Integer agentId = qywxConnectorVo.getAgentId();
        if (ObjectUtils.isEmpty(agentId)) {
            log.info("doValidateConfigAndSave failed: agentId is null");
            return Result.newError(ResultCodeEnum.QYWX_AGENT_ID_ERROR);
        }
        //通过corpId找到所有的应用
        List<OuterOaAppInfoEntity> oaAppInfoEntities = outerOaAppInfoManager.getEntities(OuterOaAppInfoParams.builder()
                .channel(channel).outEa(qywxConnectorVo.getQywxCorpId()).appType(outerOaAppInfoTypeEnum).build());
        if (CollectionUtils.isEmpty(oaAppInfoEntities)) {
            log.info("doValidateConfigAndSave failed: oaAppInfoEntities is empty, corpId={}", qywxConnectorVo.getQywxCorpId());
            return Result.newError(ResultCodeEnum.QYWX_APP_NOT_FOUND_ERROR);
        }
        //找出agentId的应用绑定信息
        boolean isExist = Boolean.FALSE;
        String appId = null;
        for (OuterOaAppInfoEntity oaAppInfoEntity : oaAppInfoEntities) {
            QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(oaAppInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);
            if (qyweixinAppInfoParams.getAuthAppInfo().getAgentId() == agentId) {
                if (oaAppInfoEntity.getStatus() == OuterOaAppInfoStatusEnum.stop) {
                    //停用了
                    log.info("doValidateConfigAndSave failed: oaAppInfoEntity is stop, corpId={}", qywxConnectorVo.getQywxCorpId());
                    return Result.newError(ResultCodeEnum.QYWX_APP_DISABLED_ERROR);
                }
                isExist = Boolean.TRUE;
                appId = oaAppInfoEntity.getAppId();
                qywxConnectorVo.setQywxEnterpriseName(qyweixinAppInfoParams.getAuthCorpInfo().getCorpName());
                break;
            }
        }
        if (!isExist) {
            log.info("doValidateConfigAndSave failed: oaAppInfoEntity is not exist, corpId={}", qywxConnectorVo.getQywxCorpId());
            return Result.newError(ResultCodeEnum.QYWX_APP_NOT_FOUND_ERROR);
        }

        boolean isSendMq = Boolean.FALSE;
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
        if (ObjectUtils.isEmpty(entityById) || StringUtils.isEmpty(entityById.getOutEa())) {
            isSendMq = Boolean.TRUE;
        }

        //同一个纷享企业是否绑定了同一个应用
        if (CollectionUtils.isNotEmpty(enterpriseBindEntities)) {
            String finalAppId = appId;
            boolean anyMatch = enterpriseBindEntities.stream().anyMatch(entity ->
                    entity.getAppId().equals(finalAppId) && entity.getFsEa().equals(entityById.getFsEa()) && !entity.getId().equals(dataCenterId));
            if (anyMatch) {
                log.info("doValidateConfigAndSave failed: corp anyMatch failed2, corpId={}", qywxConnectorVo.getQywxCorpId());
                return Result.newError(ResultCodeEnum.QYWX_APP_ALREADY_BOUND_ERROR);
            }
        }

//        if (ObjectUtils.isEmpty(entityById)) {
//            entityById = new OuterOaEnterpriseBindEntity();
//            entityById.setId(dataCenterId);
//            entityById.setChannel(channel);
//            entityById.setCreateTime(System.currentTimeMillis());
//        }

        entityById.setOutEa(qywxConnectorVo.getQywxCorpId());
        entityById.setAppId(appId);
        entityById.setConnectInfo(JSONObject.toJSONString(qywxConnectorVo));
        entityById.setBindType(outerOaAppInfoTypeEnum == OuterOaAppInfoTypeEnum.isv ? BindTypeEnum.auto : BindTypeEnum.manual);
        entityById.setBindStatus(BindStatusEnum.normal);
        entityById.setUpdateTime(System.currentTimeMillis());

        Integer count = outerOaEnterpriseBindManager.batchUpsertById(Lists.newArrayList(entityById));
        log.info("doValidateConfigAndSave: count={}", count);

        if (isSendMq) {
            eventBindSendMqManager.enterpriseBindChangeEvent(dataCenterId);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<String> doGetAuthUrl(String dataCenterId, ChannelEnum channel,
            OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        //todo
        return new Result<>();
    }

    @Override
    public List<AlertTypeEnum> getChannelSupportedAlertTypes() {
        return Arrays.asList(AlertTypeEnum.CRM_TODO, AlertTypeEnum.CRM_NOTIFICATION);
    }

    @Override
    public Result<String> queryOuterEmpByValue(String dataCenterId, ChannelEnum channel, String queryFieldApiName,
            String queryFieldValue) {
        return new Result<>();
    }

    @Override
    public Result<Void> refreshOuterEmpData(String dataCenterId, ChannelEnum channel) {
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
        if (ObjectUtils.isEmpty(enterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        String dcId = enterpriseBindEntity.getId();

        com.facishare.open.qywx.accountsync.result.Result<Void> outerContactsInfo = contactsService.syncOuterContactsInfo(outEa, appId);
        return new Result<>();
    }

    @Override
    public Result<Boolean> routeNewPage(String tenantId, ChannelEnum channel) {
        String fsEa = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        //还需要查询老的
        List<QyweixinAccountEnterpriseMapping> accountEnterpriseMappings = qyweixinAccountEnterpriseBindDao.queryEaMappingListFromFsEa(ChannelEnum.qywx.name(), fsEa, null);
        log.info("routeNewPage: fsEa={}, accountEnterpriseMappings={}", fsEa, accountEnterpriseMappings);
        if (CollectionUtils.isNotEmpty(accountEnterpriseMappings)) {
            return new Result<>(Boolean.FALSE);
        }
//        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.qywx, fsEa);
//        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
//            return new Result<>(Boolean.FALSE);
//        }

        return new Result<>(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> unbindConnect(String dataCenterId, ChannelEnum channel) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
        if (ObjectUtils.isEmpty(entityById)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        entityById.setOutEa(null);
        entityById.setAppId(null);
        entityById.setBindStatus(BindStatusEnum.create);
        entityById.setUpdateTime(System.currentTimeMillis());
        QywxConnectorVo qywxConnectorVo = JSON.parseObject(entityById.getConnectInfo(), QywxConnectorVo.class);
        qywxConnectorVo.setQywxDepartmentId(null);
        qywxConnectorVo.setQywxEnterpriseName(null);
        qywxConnectorVo.setQywxCorpId(null);
        qywxConnectorVo.setAgentId(null);
        qywxConnectorVo.setAlertConfig(false);
        qywxConnectorVo.setAlertTypes(new ArrayList<>());
        entityById.setConnectInfo(JSONObject.toJSONString(qywxConnectorVo));
        Integer count = outerOaEnterpriseBindManager.batchUpsertById(Lists.newArrayList(entityById));
        log.info("unbindConnect: outerOaEnterpriseBind batchUpsertById count={}", count);
        return new Result<>(Boolean.TRUE);
    }

    @Override
    public Result<Void> employeeBindChangeEvent(String dataCenterId, List<String> ids, EmplyeeBindChangeTypeEnum type) {
        eventBindSendMqManager.employeeBindChangeEvent(dataCenterId, ids, type);
        return new Result<>();
    }
    @Override
    public Result<String> getLoginUrl(OuterOAConnectSettingResult arg, ChannelEnum channel,
                                      OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum){
        if(!ChannelEnum.qywx.equals(arg.getChannelEnum())||!OuterOaAppInfoTypeEnum.serviceRepDev.equals(arg.getOuterOaAppInfoTypeEnum())
                ||arg.getConnectParams()==null ||arg.getConnectParams().getQywx()==null){
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        String corpId=arg.getConnectParams().getQywx().getQywxCorpId();
        Integer agentId=arg.getConnectParams().getQywx().getAgentId();
        if(corpId==null){
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        if(agentId==null){
            return Result.newError(ResultCodeEnum.QYWX_AGENT_ID_ERROR);
        }
        //企微corpId由明文查出服务商密文
        if (corpId.length() < 32) {
            // id转换
            com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.corpId2OpenCorpId(corpId);
            if (result.isSuccess() && StringUtils.isNotEmpty(result.getData())) {
                corpId=result.getData();
            } else {
                log.error("doValidateConfigAndSave failed: corpId convert failed, corpId={}", corpId);
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
            }
        }
        OuterOaAppInfoParams outerOaAppInfoParams=OuterOaAppInfoParams.builder().outEa(corpId).channel(ChannelEnum.qywx).build();
        List<OuterOaAppInfoEntity> outerOaAppInfoEntities = outerOaAppInfoManager.getEntities(outerOaAppInfoParams);
        if(CollectionUtils.isEmpty(outerOaAppInfoEntities)){
            return Result.newError(ResultCodeEnum.QYWX_APP_NOT_FOUND_ERROR);
        }
        OuterOaAppInfoEntity oaAppInfoEntity=null;
        for (OuterOaAppInfoEntity appInfo:outerOaAppInfoEntities){
            QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfo.getAppInfo(), QyweixinAppInfoParams.class);
            if(agentId.equals(qyweixinAppInfoParams.getAuthAppInfo().getAgentId())){
                oaAppInfoEntity=appInfo;
                break;
            }
        }
        if(oaAppInfoEntity==null){
            return Result.newError(ResultCodeEnum.QYWX_AGENT_ID_ERROR);
        }
        String redirectUrl= ConfigCenter.crm_domain+"/erpdss/open/oa/noAuth/standardConnector/callback";
        String loginUrl="https://login.work.weixin.qq.com/wwlogin/sso/login/?login_type=CorpApp&appid="+corpId+"&agentid="+agentId+"&redirect_uri="+redirectUrl+"&state=WWLogin";
        return Result.newSuccess(loginUrl);
    }
}
