package com.facishare.open.qywx.web.core.enums;

import com.google.common.base.MoreObjects;

public enum QYWXBusinessTypeEnum {
    QYWX_YXT("QYWX_YXT", "企业微信营销通"),

    ;
    /**
     * 类型
     */
    private String dataType;

    /**
     * 描述
     */
    private String dataDesc;

    QYWXBusinessTypeEnum(String dataType, String dataDesc) {
        this.dataType = dataType;
        this.dataDesc = dataDesc;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataDesc() {
        return dataDesc;
    }

    public void setDataDesc(String DataDesc) {
        this.dataDesc = DataDesc;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("dataType", dataType).add("dataDesc", dataDesc)
                .toString();
    }
}
