package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;

@Data
public class QyweixinQueryTransferInfo implements Serializable {
    /**
     * 纷享ea
     */
    private String ea;
    /**
     * 企微企业ID，默认为空，一个CRM对多个企微必传
     */
    private String outEa;
    /**
     *外部联系人对象
     */
    private String externalApiName;
    /**
     *外部联系人昵称
     */
    private String externalNickname;
    /**
     *外部联系人名字
     */
    private String externalUserName;
    /**
     *原跟进成员的名称
     */
    private String handoverUserName;
    /**
     *原跟进成员的状态
     */
    private Integer handoverUserStatus;
    /**
     *原跟进人员的部门名称
     */
    private String handoverDeptName;
    /**
     *接替成员姓名
     */
    private String takeoverUserName;
    /**
     *接替成员的部门名称
     */
    private String takeoverDeptName;
    /**
     *同步状态
     */
    private Integer syncStatus;
    /**
     * 同步失败原因
     */
    private String errorMsg;
    /**
     *接替状态
     */
    private Integer transferResult;
    /**
     *接替时间
     */
    private String transferTime;
    /**
     *起始时间
     */
    private String startTime;
    /**
     *结束时间
     */
    private String endTime;
}
