package com.facishare.open.qywx.accountsync.model.qyweixin;


import lombok.Data;

import java.io.Serializable;

@Data
public class QyweixinCorpRep implements Serializable {

    private String corpId;

    private String corpName;

    private String appId;

    /**
     * 每次应用授权，会产生新的永久授权码（通过 corpId + suiteId 定位）
     */
    private String permanentCode;

    /**
     * 企业应用授权时分配的agentId (重新授权会更新)
     */
    private String agentId;

    private Integer status;
}
