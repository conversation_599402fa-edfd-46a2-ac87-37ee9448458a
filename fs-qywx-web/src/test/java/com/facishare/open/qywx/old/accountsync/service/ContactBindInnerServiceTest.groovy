package com.facishare.open.qywx.old.accountsync.service

import com.facishare.open.qywx.accountinner.model.QyweixinDepartmentBindModel
import com.facishare.open.qywx.accountinner.model.QyweixinEmployeeBindModel
import com.facishare.open.qywx.accountinner.model.qywx.QyweixinDepartmentBind
import com.facishare.open.qywx.accountinner.model.qywx.QyweixinEmployeeBind
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class ContactBindInnerServiceTest extends Specification {
    @Autowired
    private ContactBindInnerService contactBindInnerService;

    def "queryEmployeeBind"() {
        given:
        QyweixinEmployeeBind queryEmployeeBind = new QyweixinEmployeeBind();
        queryEmployeeBind.setFsEa("84883");
        List<QyweixinEmployeeBindModel> employeeBinds = new LinkedList<>();
        for (int i = 0; i < 2; i++) {
            QyweixinEmployeeBindModel model = new QyweixinEmployeeBindModel();
            model.setStatus(status);
            model.setSource(source);
            if(i == 0) {
                model.setOutAccount(outAccount);
                model.setFsAccount(fsAccount);
            } else {
                model.setOutAccount(outAccount2);
                model.setFsAccount(fsAccount2);
            }
            model.setAppId(appId);
            model.setOutEa(outEa);
            employeeBinds.add(model);
        }
        queryEmployeeBind.setEmployeeBinds(employeeBinds);
        expect:
        def result = contactBindInnerService.queryEmployeeBind(queryEmployeeBind);
        println result;
        where:
        status | source  |  outAccount  |    fsAccount  |   appId   |  outEa  |  outAccount2 |   fsAccount2
        0 |   "qywx"   | "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ"    |   "E.84883.1021" |  "wx88a141937dd6f838" | "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA" | "wowx1mDAAACfqm_NsUY9FcuZ8iBp3ooQ" | "E.84883.1037"
        1 |   "qywx"   | "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ"    |   "E.84883.1021" |  "wx88a141937dd6f838" | "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA" | "wowx1mDAAACfqm_NsUY9FcuZ8iBp3ooQ" | "E.84883.1037"
        null |   "qywx"   | "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ"    |   "E.84883.1021" |  "wx88a141937dd6f838" | "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA" | "wowx1mDAAACfqm_NsUY9FcuZ8iBp3ooQ" | "E.84883.1037"
    }

    def "saveEmployeeBind"() {
        given:
        QyweixinEmployeeBind queryEmployeeBind = new QyweixinEmployeeBind();
        queryEmployeeBind.setFsEa("84883");
        List<QyweixinEmployeeBindModel> employeeBinds = new LinkedList<>();
        for (int i = 0; i < 2; i++) {
            QyweixinEmployeeBindModel model = new QyweixinEmployeeBindModel();
            model.setStatus(status);
            model.setSource(source);
            if(i == 0) {
                model.setOutAccount(outAccount);
                model.setFsAccount(fsAccount);
            } else {
                model.setOutAccount(outAccount2);
                model.setFsAccount(fsAccount2);
            }
            model.setAppId(appId);
            model.setOutEa(outEa);
            employeeBinds.add(model);
        }
        queryEmployeeBind.setEmployeeBinds(employeeBinds);
        expect:
        def result = contactBindInnerService.saveEmployeeBind(queryEmployeeBind);
        println result;
        where:
        status | source  |  outAccount  |    fsAccount  |   appId   |  outEa  |  outAccount2 |   fsAccount2
        0 |   "qywx"   | "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ0"    |   "E.84883.10210" |  "wx88a141937dd6f838" | "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA" | "wowx1mDAAACfqm_NsUY9FcuZ8iBp3ooQ0" | "E.84883.10370"
       }

    def "updateEmployeeBind"() {
        given:
        QyweixinEmployeeBind queryEmployeeBind = new QyweixinEmployeeBind();
        queryEmployeeBind.setFsEa("84883");
        List<QyweixinEmployeeBindModel> employeeBinds = new LinkedList<>();
        for (int i = 0; i < 2; i++) {
            QyweixinEmployeeBindModel model = new QyweixinEmployeeBindModel();
            model.setStatus(status);
            model.setSource(source);
            if(i == 0) {
                model.setOutAccount(outAccount);
                model.setFsAccount(fsAccount);
            } else {
                model.setOutAccount(outAccount2);
                model.setFsAccount(fsAccount2);
            }
            model.setAppId(appId);
            model.setOutEa(outEa);
            employeeBinds.add(model);
        }
        queryEmployeeBind.setEmployeeBinds(employeeBinds);
        expect:
        def result = contactBindInnerService.updateEmployeeBind(queryEmployeeBind);
        println result;
        where:
        status | source  |  outAccount  |    fsAccount  |   appId   |  outEa  |  outAccount2 |   fsAccount2
        1 |   "qywx"   | "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ0"    |   "E.84883.10210" |  "wx88a141937dd6f838" | "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA" | "wowx1mDAAACfqm_NsUY9FcuZ8iBp3ooQ0" | "E.84883.10370"
    }

    def "queryDepartmentBind"() {
        given:
        QyweixinDepartmentBind queryDepartmentBind = new QyweixinDepartmentBind();
        queryDepartmentBind.setFsEa("djt6721");
        List<QyweixinDepartmentBindModel> departmentBinds = new LinkedList<>();
        for (int i = 0; i < 2; i++) {
            QyweixinDepartmentBindModel model = new QyweixinDepartmentBindModel();
            model.setOutEa(outEa);
            model.setAppId(appId);
            model.setSource(source);
            model.setStatus(status);
            model.setFsEa(fsEa);
            if(i == 0) {
                model.setFsDepartmentId(fsDepartmentId);
                model.setOutDepartmentId(outDepartmentId);
            } else {
                model.setFsDepartmentId(fsDepartmentId2);
                model.setOutDepartmentId(outDepartmentId2);
            }
            departmentBinds.add(model);
        }
        queryDepartmentBind.setDepartmentBinds(departmentBinds);
        expect:
        def result = contactBindInnerService.queryDepartmentBind(queryDepartmentBind);
        println result;
        where:
        fsEa | status | source  |  outDepartmentId  |    fsDepartmentId  |   appId   |  outEa  |  outDepartmentId2 |   fsDepartmentId2
        "djt6721" | 0 |   "qywx"   | "2"    |   1000 |  "wx88a141937dd6f838" | "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw" | "3" | 1001
        "djt6721" | 1 |   "qywx"   | "2"    |   1000 |  "wx88a141937dd6f838" | "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw" | "3" | 1001
        "djt6721" | null |   "qywx"   | "2"    |   1000 |  "wx88a141937dd6f838" | "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw" | "3" | 1001
    }

    def "saveDepartmentBind"() {
        given:
        QyweixinDepartmentBind queryDepartmentBind = new QyweixinDepartmentBind();
        queryDepartmentBind.setFsEa("djt6721");
        List<QyweixinDepartmentBindModel> departmentBinds = new LinkedList<>();
        for (int i = 0; i < 2; i++) {
            QyweixinDepartmentBindModel model = new QyweixinDepartmentBindModel();
            model.setOutEa(outEa);
            model.setAppId(appId);
            model.setSource(source);
            model.setStatus(status);
            model.setFsEa(fsEa);
            if(i == 0) {
                model.setFsDepartmentId(fsDepartmentId);
                model.setOutDepartmentId(outDepartmentId);
            } else {
                model.setFsDepartmentId(fsDepartmentId2);
                model.setOutDepartmentId(outDepartmentId2);
            }
            departmentBinds.add(model);
        }
        queryDepartmentBind.setDepartmentBinds(departmentBinds);
        expect:
        def result = contactBindInnerService.saveDepartmentBind(queryDepartmentBind);
        println result;
        where:
        fsEa | status | source  |  outDepartmentId  |    fsDepartmentId  |   appId   |  outEa  |  outDepartmentId2 |   fsDepartmentId2
        "djt6721" | 0 |   "qywx"   | "200"    |   10000 |  "wx88a141937dd6f838" | "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw" | "300" | 10010
    }

    def "updateDepartmentBind"() {
        given:
        QyweixinDepartmentBind queryDepartmentBind = new QyweixinDepartmentBind();
        queryDepartmentBind.setFsEa("djt6721");
        List<QyweixinDepartmentBindModel> departmentBinds = new LinkedList<>();
        for (int i = 0; i < 2; i++) {
            QyweixinDepartmentBindModel model = new QyweixinDepartmentBindModel();
            model.setOutEa(outEa);
            model.setAppId(appId);
            model.setSource(source);
            model.setStatus(status);
            model.setFsEa(fsEa);
            if(i == 0) {
                model.setFsDepartmentId(fsDepartmentId);
                model.setOutDepartmentId(outDepartmentId);
            } else {
                model.setFsDepartmentId(fsDepartmentId2);
                model.setOutDepartmentId(outDepartmentId2);
            }
            departmentBinds.add(model);
        }
        queryDepartmentBind.setDepartmentBinds(departmentBinds);
        expect:
        def result = contactBindInnerService.updateDepartmentBind(queryDepartmentBind);
        println result;
        where:
        fsEa | status | source  |  outDepartmentId  |    fsDepartmentId  |   appId   |  outEa  |  outDepartmentId2 |   fsDepartmentId2
        "djt6721" | 1 |   "qywx"   | "200"    |   10000 |  "wx88a141937dd6f838" | "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw" | "300" | 10010
    }

//    def "exportEmployeeBind"() {
//        given:
//        expect:
//        def result = contactBindInnerService.exportEmployeeBind("84883");
//        println result;
//    }
}
