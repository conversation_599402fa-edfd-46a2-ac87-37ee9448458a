package com.facishare.open.qywx.web.controller.outer;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class ControllerQYWeixinTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private ControllerQYWeixin controllerQYWeixin;

    @Test
    public void outAccountToFsAccountBatchTest() throws Exception {
        controllerQYWeixin.doFunction("aHR0cHM6Ly93d3cuY2VzaGkxMTIuY29tL2hjcm0vd2VjaGF0L2Z1bmN0aW9uL3RvZG8/YXBpbmFtZT1vYmplY3RfMHZ6WTJfX2MmaWQ9NjM3NjAyYWUwMTc5NWYwMDAxOTRlYWVlJmVhPWxnZzY3Mzc=",
                "wx88a141937dd6f838",
                null,
                "dd",
                null);
    }
}
