package com.facishare.open.erpdss.outer.oa.connector.base.inner.login;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;

/**
 * 免登录模板
 * <AUTHOR>
 * @date 2024-08-20
 */

public abstract class LoginTemplate {
    /**
     * 通过code获取外部用户信息
     * @param context
     */
    public abstract void getOutUserInfoByCode(MethodContext context);

    /**
     * 生成纷享ticket
     * @param context
     */
    public abstract void genFsTicket(MethodContext context);

    /**
     * 通过ticket换取纷享用户信息
     * @param context
     */
    public abstract void getFsUserInfoByTicket(MethodContext context);

    /**
     * SSO场景创建用户登录token
     * @param context
     */
    public void createUserToken(MethodContext context) {

    }
}
