package com.facishare.open.erpdss.outer.oa.connector.base.inner.contacts.emp;

import com.facishare.open.erpdss.outer.oa.connector.base.HandlerTemplate;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.inner.field_mapping.EmpAndDepFieldMappingTemplate;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 更新纷享员工处理器模板
 * <AUTHOR>
 * @date 2024-08-20
 */

@Slf4j
public abstract class UpdateFsEmpHandlerTemplate extends HandlerTemplate {
    @Resource
    private EmpAndDepFieldMappingTemplate empAndDepFieldMappingTemplate;

    @Override
    public TemplateResult execute(Object data) {
        MethodContext context = MethodContext.newInstance(data);

        if(empAndDepFieldMappingTemplate!=null) {
            empAndDepFieldMappingTemplate.empFieldMapping(context);
            log.info("UpdateFsEmpHandlerTemplate.execute,empFieldMapping,context={}",context);
            if(context.isError()) {
                return context.getResult();
            }
        }

        updateEmp(context);
        log.info("UpdateFsEmpHandlerTemplate.execute,updateEmp,context={}",context);

        return context.getResult();
    }

    /**
     * 更新员工
     * @param context
     */
    public abstract void updateEmp(MethodContext context);
}
