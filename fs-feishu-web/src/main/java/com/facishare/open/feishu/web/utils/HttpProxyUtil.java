package com.facishare.open.feishu.web.utils;

import com.facishare.open.feishu.web.arg.HttpArg;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class HttpProxyUtil {
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    public String httpProxyRequest(HttpArg body) {
        if(body.getMethod().equals("GET")) {
            return proxyHttpClient.getUrl(body.getUrl(), body.getHeader());
        }
        return proxyHttpClient.postUrl(body.getUrl(), body.getBody(), body.getHeader());
    }
}
