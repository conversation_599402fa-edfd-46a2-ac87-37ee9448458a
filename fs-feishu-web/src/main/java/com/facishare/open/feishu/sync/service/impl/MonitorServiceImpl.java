package com.facishare.open.feishu.sync.service.impl;

import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataManager;
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.MonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("monitorService")
public class MonitorServiceImpl implements MonitorService {
    @Autowired
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;

    @Override
    public Result<Void> uploadOaConnectorOpenData(OAConnectorOpenDataModel oaConnectorOpenDataModel) {
        oaConnectorOpenDataManager.send(oaConnectorOpenDataModel);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> alertMoreEmployeeMessage(OAConnectorOpenDataModel oaConnectorOpenDataModel) {
        oaConnectorOpenDataManager.send(oaConnectorOpenDataModel);
        return Result.newSuccess();
    }
}
