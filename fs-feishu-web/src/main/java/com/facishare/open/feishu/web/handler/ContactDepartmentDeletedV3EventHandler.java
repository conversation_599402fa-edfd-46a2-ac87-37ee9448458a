package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactDepartmentDeletedV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 员工离职处理器
 * <AUTHOR>
 * @date ********
 */
@Slf4j
@Component
public class ContactDepartmentDeletedV3EventHandler extends FeishuEventHandler {
    @Resource
    private ContactsService contactsService;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Override
    public String getSupportEventType() {
        return "contact.department.deleted_v3";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        LogUtils.info("ContactDepartmentDeletedV3EventHandler.handle,eventData={}",eventData);
        FeishuContactDepartmentDeletedV3Event event = JSON.parseObject(eventData, FeishuContactDepartmentDeletedV3Event.class);
        LogUtils.info("ContactDepartmentDeletedV3EventHandler.handle,event={}",event);
        // 查询企业绑定状态
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().outEa(header.getTenantKey())
                .appId(header.getAppId()).bindStatus(BindStatusEnum.normal).build();
        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);
        for (OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity : enterpriseBindList) {
            OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, outerOaEnterpriseBindEntity.getId());
            SettingAccountRulesModel settingAccountRulesModel= JSON.parseObject(entityByDataCenterId.getConfigInfo(), SettingAccountRulesModel.class);
            if(settingAccountRulesModel.getBindTypeEnum() == BindTypeEnum.manual||settingAccountRulesModel.getSyncTypeEnum()== EnterpriseConfigAccountSyncTypeEnum.accountBind){
                log.info("ContactDepartmentDeletedV3EventHandler.handle,outEa={},账号设置手动绑定，不支持删除部门:{}",header.getTenantKey(),outerOaEnterpriseBindEntity.getFsEa());
                contactsService.deleteContactDepartment(outerOaEnterpriseBindEntity.getChannel(),header.getAppId(),
                        header.getTenantKey(),
                        event.getObject());

            }else{
                Result<Void> result = contactsService.removeDepList(header.getAppId(),
                        header.getTenantKey(),
                        Lists.newArrayList(event.getObject()),outerOaEnterpriseBindEntity);
                LogUtils.info("ContactDepartmentDeletedV3EventHandler.handle,removeDepList,result={}",result);

            }
        }
        return SUCCESS;
    }
}
