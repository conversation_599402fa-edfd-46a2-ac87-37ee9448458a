package com.facishare.open.feishu.web.handler;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.service.*;
import com.facishare.open.feishu.web.template.outer.event.app.FeishuAppEventTemplate;
import com.facishare.open.feishu.web.template.model.FeishuEventHandleModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 飞书通讯录可见范围更新事件 事件处理器
 * <AUTHOR>
 * @date 20220803
 */
@Slf4j
@Component
public class ContactScopeUpdateV3EventHandler extends FeishuEventHandler {
    @Resource
    private FeishuAppEventTemplate feishuAppEventTemplate;

    @Override
    public String getSupportEventType() {
        return "contact.scope.updated_v3";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        log.info("ContactScopeUpdateV3EventHandler.handle,eventData={},header={}",eventData,header);

        FeishuEventHandleModel eventHandleModel = new FeishuEventHandleModel();
        eventHandleModel.setHeader(header);
        eventHandleModel.setEventType(getSupportEventType());
        eventHandleModel.setEventData(eventData);

        MethodContext context = MethodContext.newInstance(eventHandleModel);
        feishuAppEventTemplate.onAppVisibleRangeChange(context);
        return context.getResult().getDataOrMsg();
    }


}
