package com.facishare.open.feishu.syncapi.result.whatsapp;

import com.facishare.open.feishu.syncapi.result.BaseResult;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class Result<T> extends WhatsappBaseResult implements Serializable {
    private static final long serialVersionUID = -1407891263555853715L;

    protected T data;

    public Result() {
        this.code = ResultCodeEnum.SUCCESS.getCode();
        this.message = ResultCodeEnum.SUCCESS.getMsg();
    }

    public Result(T data) {
        this.code = ResultCodeEnum.SUCCESS.getCode();
        this.message = ResultCodeEnum.SUCCESS.getMsg();
        this.data = data;
    }

    public Result(ResultCodeEnum resultCode) {
        this.code = resultCode.getCode();
        this.message = resultCode.getMsg();
    }

    public Result(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <R> Result<R> newInstance(int code, String message, R data) {
        Result<R> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        result.setData(data);
        return result;
    }

    public static Result newInstance(int code, String message) {
       return newInstance(code, message,null);
    }

    private static <R> Result<R> newInstance(ResultCodeEnum resultCode, R data) {
        return newInstance(resultCode.getCode(), resultCode.getMsg(), data);
    }

    public static <R> Result<R> newError(ResultCodeEnum resultCode) {
        return newInstance(resultCode, null);
    }

    public static <R> Result<R> newError(ResultCodeEnum resultCode, String message) {
        return newError(resultCode.getCode(), message);
    }

//    public static <R> Result<R> newErrorTrace(ResultCodeEnum resultCode, String traceMsg) {
//        Result<R> result = new Result<>(resultCode);
//        result.setTraceMsg(traceMsg);
//        return result;
//    }

//    public static <R> Result<R> newErrorOpenSourceMsg(ResultCodeEnum resultCode, String openSourceMsg) {
//        Result<R> result = new Result<>(resultCode);
//        result.setOpenSourceMsg(openSourceMsg);
//        return result;
//    }

    public static <R> Result<R> newError(ResultCodeEnum resultCode, R data) {
        return newInstance(resultCode, data);
    }

    public static <R> Result<R> newError(int code, String message) {
        Result<R> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }

    public static <R> Result<R> newError(int code, String message, String traceId) {
        Result<R> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        result.setTraceId(traceId);
        return result;
    }

    public static <R> Result<R> newSuccess() {
        return newInstance(ResultCodeEnum.SUCCESS, null);
    }

    public static <R> Result<R> newSuccess(R data) {
        return newInstance(ResultCodeEnum.SUCCESS, data);
    }

    public boolean isSuccess() {
        return ResultCodeEnum.SUCCESS.getCode()==this.code;
    }


    /**
     * 复制错误结果，不复制数据
     *
     * @param result
     * @param <R>
     * @return
     */
    public static <R> Result<R> copy(BaseResult result) {
        return newInstance(result.getCode(), result.getMsg(), null);
    }

    @Override
    public String toString() {
        return "Result{" +
                "data=" + data +
                ", code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", traceId='" + traceId + '\'' +
                '}';
    }
}
