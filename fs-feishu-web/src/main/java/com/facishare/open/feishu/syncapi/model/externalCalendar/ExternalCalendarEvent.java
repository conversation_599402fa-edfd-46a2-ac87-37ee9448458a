package com.facishare.open.feishu.syncapi.model.externalCalendar;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ExternalCalendarEvent implements Serializable {
    private static final long serialVersionUID = 1L;

    private String summary;
    private String description;

    @JSONField(name = "need_notification")
    private Boolean needNotification;

    @JSONField(name = "start_time")
    private TimeInfo startTime;

    @JSONField(name = "end_time")
    private TimeInfo endTime;

    private VChat vchat;
    private String visibility;

    @JSONField(name = "attendee_ability")
    private String attendeeAbility;

    @JSONField(name = "free_busy_status")
    private String freeBusyStatus;

    private Location location;
    private Integer color;
    private List<Reminder> reminders;
    private String recurrence;
    private List<Schema> schemas;

    @Data
    public static class TimeInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private String date;
        private String timestamp;
        private String timezone;
    }

    @Data
    public static class VChat implements Serializable {
        private static final long serialVersionUID = 1L;

        @J<PERSON><PERSON><PERSON>(name = "vc_type")
        private String vcType;

        @J<PERSON><PERSON>ield(name = "icon_type")
        private String iconType;

        private String description;

        @JSONField(name = "meeting_url")
        private String meetingUrl;

        @JSONField(name = "meeting_settings")
        private MeetingSettings meetingSettings;
    }

    @Data
    public static class MeetingSettings implements Serializable {
        private static final long serialVersionUID = 1L;

        @JSONField(name = "owner_id")
        private String ownerId;

        @JSONField(name = "join_meeting_permission")
        private String joinMeetingPermission;

        @JSONField(name = "assign_hosts")
        private List<String> assignHosts;

        @JSONField(name = "auto_record")
        private Boolean autoRecord;

        @JSONField(name = "open_lobby")
        private Boolean openLobby;

        @JSONField(name = "allow_attendees_start")
        private Boolean allowAttendeesStart;
    }

    @Data
    public static class Location implements Serializable {
        private static final long serialVersionUID = 1L;

        private String name;
        private String address;
        private Double latitude;
        private Double longitude;
    }

    @Data
    public static class Reminder implements Serializable {
        private static final long serialVersionUID = 1L;

        private Integer minutes;
    }

    @Data
    public static class Schema implements Serializable {
        private static final long serialVersionUID = 1L;

        @JSONField(name = "ui_name")
        private String uiName;

        @JSONField(name = "ui_status")
        private String uiStatus;

        @JSONField(name = "app_link")
        private String appLink;
    }
}