package com.facishare.open.feishu.syncapi.data.whatsapp;

import com.facishare.open.feishu.syncapi.base.FsBase;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class WhatsappGetTemplate extends FsBase implements Serializable {
    /**
     * 牛信云WhatsApp应用的appkey
     */
    private String appkey;
    /**
     * 发送消息的通道，应用于WhatsApp消息的发送时，值必须为“whatsapp”
     */
    private String messaging_product = ChannelEnum.whatsapp.toString();
    /**
     * 商户的WhatsApp号码列表，需要带国码,如185xxx99
     */
    private String business_phone;
    /**
     * 向后分页游标值
     */
    private String after;
    /**
     * 向前分页游标值
     */
    private String before;
    /**
     * 每页模板数量 (为空 limit则默认值为20)
     */
    private Integer limit;
    /**
     * 模板名称(会返回同名的不同语言模板)
     */
    private String name;
}
