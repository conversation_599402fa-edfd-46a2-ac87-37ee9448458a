package com.facishare.open.feishu.sync.mq.sender;

import com.facishare.open.feishu.syncapi.event.WhatsappMsgPushEvent;
import com.facishare.open.feishu.syncapi.proto.OaconnectorEventDateChangeProto;
import com.facishare.open.feishu.syncapi.proto.OutEventDateChangeProto;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.github.trace.TraceContext;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class MQSyncSender {
    @Resource(name = "whatsappMsgNotifyMQSender")
    private AutoConfMQProducer whatsappMsgNotifyMQSender;
    @Resource(name = "oaconnectorEventDataChangeMQSender")
    private AutoConfMQProducer oaconnectorEventDataChangeMQSender;

    @Resource(name = "outEventDataChangeMQSender")
    private AutoConfMQProducer outEventDataChangeMQSender;

    public SendResult sendWhatsappMsgNotifyMQ(String tag, WhatsappMsgPushEvent event) {
        //投递mq
        Message msg = new Message();
        msg.setTopic(whatsappMsgNotifyMQSender.getDefaultTopic());
        msg.setTags(tag);
        msg.setBody(event.toProto());
        SendResult sendResult = whatsappMsgNotifyMQSender.send(msg);
        LogUtils.info("MQSender.sendWhatsappMsgNotifyMQ.sendResult={}", sendResult);
        return sendResult;
    }

    public SendResult sendOaconnectorEventDataChangeMQ(String tag, OaconnectorEventDateChangeProto proto, String ei) {
        Message msg = new Message();
        msg.setTopic(oaconnectorEventDataChangeMQSender.getDefaultTopic());
        msg.setTags(tag);
        msg.setBody(proto.toProto());

        TraceContext context = TraceContext.get();
        context.setEi(ei);  // 可以区分是否跨云投递
        context.setTraceId(TraceUtils.getTraceId());  // 日记记录，分布式跟踪需要

        SendResult sendResult = oaconnectorEventDataChangeMQSender.send(msg);
        LogUtils.info("MQSender.sendOaconnectorEventDataChangeMQ.ei={},tag={},sendResult={}", ei, tag, sendResult);
        return sendResult;
    }

    public SendResult sendOutEventDataChangeMQ(String tag, OutEventDateChangeProto proto, String ei) {
        Message msg = new Message();
        msg.setTopic(outEventDataChangeMQSender.getDefaultTopic());
        msg.setTags(tag);
        msg.setBody(proto.toProto());

        TraceContext context = TraceContext.get();
        context.setEi(ei);  // 可以区分是否跨云投递
        context.setTraceId(TraceUtils.getTraceId());  // 日记记录，分布式跟踪需要

        SendResult sendResult = outEventDataChangeMQSender.send(msg);
        LogUtils.info("MQSender.sendOutEventDataChangeMQ.ei={},tag={},sendResult={}", ei, tag, sendResult);
        return sendResult;
    }
}
