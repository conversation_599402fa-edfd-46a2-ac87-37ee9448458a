package com.facishare.open.feishu.sync.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.feishu.sync.mapper.CalendarMapper;
import com.facishare.open.feishu.syncapi.entity.CalendarEntity;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class CalendarManager {
    @Resource
    private CalendarMapper calendarMapper;

    public Integer insert(CalendarEntity entity) {
        int count = calendarMapper.insert(entity);
        LogUtils.info("CalendarManager.insert,count={}",count);
        return count;
    }

    public Integer update(CalendarEntity entity) {
        int count = calendarMapper.updateById(entity);
        LogUtils.info("CalendarManager.update,count={}",count);
        return count;
    }

    public CalendarEntity queryEntity(ChannelEnum channel, String outEa) {
        LambdaQueryWrapper<CalendarEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CalendarEntity::getChannel, channel);
        wrapper.eq(CalendarEntity::getOutEa, outEa);

        return calendarMapper.selectOne(wrapper);
    }
}
