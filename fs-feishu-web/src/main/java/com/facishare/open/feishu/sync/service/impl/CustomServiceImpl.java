package com.facishare.open.feishu.sync.service.impl;

import com.facishare.open.feishu.sync.manager.CustomUrlInfoManager;
import com.facishare.open.feishu.syncapi.entity.CustomUrlInfoEntity;
import com.facishare.open.feishu.syncapi.info.CustomUrlInfo;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.CustomService;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


@Service("customService")
public class CustomServiceImpl implements CustomService {
    @Resource
    private CustomUrlInfoManager customUrlInfoManager;
    @Resource
    private I18NStringManager i18NStringManager;

    @Override
    public Result<List<CustomUrlInfo>> getCustomUrls(ChannelEnum channelEnum, String tenantId) {
        String lang = TraceUtils.getLocale();
        if(ObjectUtils.isEmpty(channelEnum)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        List<CustomUrlInfoEntity> entities = customUrlInfoManager.queryEntities(channelEnum);
        if(CollectionUtils.isEmpty(entities)) {
            return Result.newSuccess();
        }
        List<CustomUrlInfo> customUrlInfos = entities.stream()
                .map(entity -> {
                    CustomUrlInfo customUrlInfo = new CustomUrlInfo();
                    BeanUtils.copyProperties(entity, customUrlInfo);
                    //设置多语
                    customUrlInfo.setUrlName(i18NStringManager.get("erpdss_outer_oa_connector.feishu.global."+entity.getUrlName(), lang, tenantId, null));
                    return customUrlInfo;
                })
                .collect(Collectors.toList());
        return Result.newSuccess(customUrlInfos);
    }
}
