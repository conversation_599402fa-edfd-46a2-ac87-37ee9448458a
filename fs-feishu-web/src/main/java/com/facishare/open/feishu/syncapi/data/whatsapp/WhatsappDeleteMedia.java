package com.facishare.open.feishu.syncapi.data.whatsapp;

import com.facishare.open.feishu.syncapi.base.FsBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class WhatsappDeleteMedia extends FsBase implements Serializable {
    private String appkey;
    /**
     * 商户手机号码，需要带国码。如86158xxxx1795
     */
    private String business_phone;
    /**
     * 发送消息的通道，应用于WhatsApp消息的发送时，值必须为“whatsapp”
     */
    private String messaging_product;

    private String name;


    private String id;
}
