package com.facishare.open.feishu.sync.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.open.feishu.sync.mapper.DepartmentBindMapper;
import com.facishare.open.feishu.syncapi.entity.DepartmentBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * tb_department_bind表管理器类
 *
 * <AUTHOR>
 * @date 20220722
 */
@Component
@Deprecated
public class DepartmentBindManager {
    @Resource
    private DepartmentBindMapper departmentBindMapper;

    public int insert(DepartmentBindEntity entity) {
        int count = departmentBindMapper.insert(entity);
        LogUtils.info("DepartmentBindManager.insert,count={}", count);
        return count;
    }

    /**
     * 查询部门信息
     *
     * @param outEa
     * @param outDepId
     * @return
     */
    public DepartmentBindEntity getEntity(String outEa, String outDepId) {
        LambdaQueryWrapper<DepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DepartmentBindEntity::getOutEa, outEa);
        wrapper.eq(DepartmentBindEntity::getOutDepId, outDepId);

        DepartmentBindEntity entity = departmentBindMapper.selectOne(wrapper);
        return entity;
    }

    /**
     * 查询部门信息
     *
     * @param outEa
     * @param outDepId
     * @return
     */
    public DepartmentBindEntity getEntity(ChannelEnum channel, String fsEa, String fsDepId, String outEa,
            String outDepId) {
        LambdaQueryWrapper<DepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DepartmentBindEntity::getChannel, channel.name());
        wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
        wrapper.eq(DepartmentBindEntity::getFsDepId, fsDepId);
        wrapper.eq(DepartmentBindEntity::getOutEa, outEa);
        wrapper.eq(DepartmentBindEntity::getOutDepId, outDepId);

        DepartmentBindEntity entity = departmentBindMapper.selectOne(wrapper);
        return entity;
    }

    // /**
    // * 查询部门信息
    // *
    // * @param fsEa
    // * @param fsDepId
    // * @return
    // */
    // public DepartmentBindEntity getEntity2(String fsEa, String fsDepId) {
    // LambdaQueryWrapper<DepartmentBindEntity> wrapper = new
    // LambdaQueryWrapper<>();
    // wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
    // wrapper.eq(DepartmentBindEntity::getFsDepId, fsDepId);
    //
    // DepartmentBindEntity entity = departmentBindMapper.selectOne(wrapper);
    // return entity;
    // }

    /**
     * 查询部门信息
     *
     * @param fsEa
     * @param depCode
     * @return
     */
    public DepartmentBindEntity getEntityByFsEa(String fsEa, String depCode) {
        LambdaQueryWrapper<DepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
        wrapper.eq(DepartmentBindEntity::getDepCode, depCode);

        DepartmentBindEntity entity = departmentBindMapper.selectOne(wrapper);
        return entity;
    }

    /**
     * 查询部门信息
     *
     * @param outEa
     * @param depCode
     * @return
     */
    public DepartmentBindEntity getEntityByOutEa(String outEa, String depCode) {
        LambdaQueryWrapper<DepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DepartmentBindEntity::getOutEa, outEa);
        wrapper.eq(DepartmentBindEntity::getDepCode, depCode);

        DepartmentBindEntity entity = departmentBindMapper.selectOne(wrapper);
        return entity;
    }

    /**
     * 批量更新部门绑定状态
     *
     * @param fsEa
     * @param fsDepIdList
     * @param bindStatus
     * @return
     */
    public int batchUpdateBindStatus(String fsEa, List<String> fsDepIdList, BindStatusEnum bindStatus, String outEa) {
        DepartmentBindEntity entity = DepartmentBindEntity.builder().bindStatus(bindStatus).build();

        LambdaUpdateWrapper<DepartmentBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
        if (CollectionUtils.isNotEmpty(fsDepIdList)) {
            wrapper.in(DepartmentBindEntity::getFsDepId, fsDepIdList);
        }
        if (StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(DepartmentBindEntity::getOutEa, outEa);
        }
        int count = departmentBindMapper.update(entity, wrapper);
        LogUtils.info("DepartmentBindManager.batchUpdateBindStatus,count={}", count);
        return count;
    }

    // /**
    // * 更新部门绑定状态
    // *
    // * @param fsEa
    // * @param bindStatus
    // * @return
    // */
    // public int updateBindStatus(String fsEa, BindStatusEnum bindStatus) {
    // DepartmentBindEntity entity = DepartmentBindEntity.builder()
    // .bindStatus(bindStatus)
    // .build();
    //
    // LambdaUpdateWrapper<DepartmentBindEntity> wrapper = new
    // LambdaUpdateWrapper<>();
    // wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
    // int count = departmentBindMapper.update(entity, wrapper);
    // LogUtils.info("DepartmentBindManager.updateBindStatus,count={}", count);
    // return count;
    // }

    /**
     * 删除已绑定的部门数据
     *
     * @param fsEa
     * @return
     */
    public int deleteByFsEa(String fsEa, String outEa) {
        LambdaUpdateWrapper<DepartmentBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
        wrapper.eq(DepartmentBindEntity::getOutEa, outEa);
        int count = departmentBindMapper.delete(wrapper);
        LogUtils.info("DepartmentBindManager.deleteByFsEa,count={}", count);
        return count;
    }

    /**
     * 分页查询部门绑定信息
     * 
     * @param fsEa     飞书企业应用ID
     * @param outEa    外部企业应用ID
     * @param pageNum  页码，从1开始
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public Page<DepartmentBindEntity> getDepartmentListPage(String fsEa, String outEa, Integer pageNum,
            Integer pageSize) {
        // 创建分页对象
        Page<DepartmentBindEntity> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<DepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
        }
        if (StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(DepartmentBindEntity::getOutEa, outEa);
        }

        // 执行分页查询
        return departmentBindMapper.selectPage(page, wrapper);
    }

    /**
     * 分页查询部门绑定信息（支持多条件查询）
     * 
     * @param channel    渠道
     * @param fsEa       飞书企业应用ID
     * @param outEa      外部企业应用ID
     * @param depCode    部门编码
     * @param bindStatus 绑定状态
     * @param pageNum    页码，从1开始
     * @param pageSize   每页大小
     * @return 分页结果
     */
    public Page<DepartmentBindEntity> getDepartmentListPage(ChannelEnum channel, String fsEa, String outEa,
            String depCode, BindStatusEnum bindStatus, Integer pageNum, Integer pageSize) {
        // 创建分页对象
        Page<DepartmentBindEntity> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<DepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        if (channel != null) {
            wrapper.eq(DepartmentBindEntity::getChannel, channel.name());
        }
        if (StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
        }
        if (StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(DepartmentBindEntity::getOutEa, outEa);
        }
        if (StringUtils.isNotEmpty(depCode)) {
            wrapper.eq(DepartmentBindEntity::getDepCode, depCode);
        }
        if (bindStatus != null) {
            wrapper.eq(DepartmentBindEntity::getBindStatus, bindStatus);
        }

        // 执行分页查询
        return departmentBindMapper.selectPage(page, wrapper);
    }
}
