package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.enums.DepartmentIdTypeEnum;
import com.facishare.open.feishu.syncapi.enums.UserIdTypeEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.UserData;

import java.util.List;

/**
 * 飞书用户服务类
 * <AUTHOR>
 * @date 20220729
 */
public interface FeishuUserService {
    /**
     * 创建用户，仅自建应用可用
     *
     * @param appId
     * @param user
     * @return
     */
    Result<UserData.User> createUser(String appId,
                                     UserData.User user);
    /**
     * 删除用户，仅自建应用可用
     *
     * @param appId
     * @param userId
     * @return
     */
    Result<Void> deleteUser(String appId, String userId);
    /**
     * 获取飞书用户信息
     *
     * @param appId
     * @param userId
     * @return
     */
    Result<UserData.User> getUserInfo(String appId,
                                 String tenantKey,
                                 String userId);

    /**
     * 获取飞书用户信息
     *
     * @param appId
     * @param userId
     * @param userIdType
     * @param departmentIdType
     * @return
     */
    Result<UserData.User> getUserInfo(String appId,
                             String tenantKey,
                             String userId,
                             UserIdTypeEnum userIdType,
                             DepartmentIdTypeEnum departmentIdType);



    /**
     * 获取部门直属用户列表
     *
     * @param appId
     * @param tenantKey
     * @param departmentId
     * @param userIdType
     * @param departmentIdType
     * @return
     */
    Result<List<UserData.User>> getUserList(String appId,
                                              String tenantKey,
                                              String departmentId,
                                              UserIdTypeEnum userIdType,
                                              DepartmentIdTypeEnum departmentIdType,
                                              Integer pageSize);


    /**
     * 获取部门直属用户列表
     * @param appId
     * @param tenantKey
     * @param departmentId
     * @return
     */
     Result<List<UserData.User>> getAllDepartmentUsers(String appId, String tenantKey, String departmentId);

    Result<List<UserData.User>> batchGetUserIds(String appId,
                                                String tenantKey,
                                                UserIdTypeEnum userIdType,
                                                List<String> emails,
                                                List<String> mobiles);

    Result<List<UserData.User>> batchGetUserIds(String appId,
                                                String tenantKey,
                                                List<String> emails,
                                                List<String> mobiles);
}
