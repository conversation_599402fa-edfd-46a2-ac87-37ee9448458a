package com.facishare.open.feishu.web.excel;

import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.facishare.open.feishu.web.enums.TemplateTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
public class BuildExcelFile {
    @Data
    public static class Result implements Serializable {
        private static final long serialVersionUID = 8785941275320897965L;
        private String fileName;
        private String tnFilePath;
    }

    @Data
    public static class DownUrlResult implements Serializable {
        private static final long serialVersionUID = 9181946463285972887L;
        private Boolean success=false;
        private String printMsg;
        private String downloadUrl;
    }

    @Data
    public static class Arg implements Serializable {
        private static final long serialVersionUID = -968360108756417347L;
        /**
         * ea
         */
        private String ea;

        /**
         * 文件名称
         */
        private String fileName;

        /**
         * sheet名称 + sheet 数据
         */
        private Map<String,List> sheetDataList;

        /**
         * 表格样式，可不传，默认使用内容居中
         */
        private HorizontalCellStyleStrategy style;

        /**
         * excel文档类型
         */
        private TemplateTypeEnum templateType;
    }

}
