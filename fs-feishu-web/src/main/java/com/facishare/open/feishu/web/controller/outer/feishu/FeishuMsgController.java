package com.facishare.open.feishu.web.controller.outer.feishu;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.config.OAMessageTag;
import com.facishare.open.feishu.syncapi.enums.MsgTypeEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.MsgService;
import com.facishare.open.feishu.web.handler.ExternalMessageHandler;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 飞书推送消息接口
 * <AUTHOR>
 * @date 20221026
 */
@Deprecated
@RestController
@RequestMapping(value="/feishu/msg")
public class FeishuMsgController {
    @Resource
    private MsgService msgService;
    @Resource
    private ExternalMessageHandler externalMessageHandler;

    ThreadPoolExecutor executor = new ThreadPoolExecutor(50,200,60, TimeUnit.SECONDS,new LinkedBlockingQueue<>());


    /**
     * 发送飞书消息接口
     * @param msgType
     * @param fsEa
     * @param fsUserIdList
     * @param content
     * @param appId
     * @return
     */
    @RequestMapping(value="/send",method = RequestMethod.GET)
    @ResponseBody
    public Result<Void> send(@RequestParam MsgTypeEnum msgType,
                               @RequestParam String fsEa,
                               @RequestParam List<String> fsUserIdList,
                               @RequestParam String content,
                               @RequestParam String appId) {
        return msgService.batchSend(msgType,fsEa,fsUserIdList,content,appId);
    }

    /**
     * 待办和提醒消息推送至飞书
     * @param msg  消息
     * @param msgType  消息类型
     * @return
     */
    @RequestMapping(value="/sendExternalMsg",method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> sendExternalMsg(@RequestBody String msg, @RequestParam("msgType") String msgType) {
        if(StringUtils.isAnyEmpty(msg, msgType)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        if(ConfigCenter.SYNC_SEND_MSG) {
            executor.submit(()->{
                sendExternalMsg2(msg, msgType);
            });

            LogUtils.info("ExternalController.send,res={},executor={}", msg, executor.toString());
            return Result.newSuccess();
        } else {
            LogUtils.info("ExternalController.send,send2=true");
            return sendExternalMsg2(msg, msgType);
        }
    }

    Result<Void> sendExternalMsg2(@RequestBody String msg, @RequestParam("msgType") String msgType) {
        try {
            Result<Void> returnMsg = Result.newSuccess();
            switch (msgType) {
                case OAMessageTag.TEXT_MSG_TAG: {
                    SendTextMessageArg sendTextMessageArg = new Gson().fromJson(msg, new TypeToken<SendTextMessageArg>() {
                    }.getType());

                    externalMessageHandler.dealSendTextMessageHandler(sendTextMessageArg);

                    //Result<Void> sendTextMessageResult = externalMsgService.sendTextMessage(sendTextMessageArg);
                    //returnMsg = sendTextMessageResult;
                }
                    break;
                case OAMessageTag.CARD_MSG_TAG: {
                    SendTextCardMessageArg sendTextCardMessageArg = new Gson().fromJson(msg, new TypeToken<SendTextCardMessageArg>() {
                    }.getType());

                    externalMessageHandler.dealSendTextCardMessageHandler(sendTextCardMessageArg);

//                    Result<Void> sendTextCardMessageResult = externalMsgService.sendTextCardMessage(sendTextCardMessageArg);
//                    returnMsg = sendTextCardMessageResult;
                }
                    break;
                case  OAMessageTag.CREATE_TO_DO_TAG: {
                    CreateTodoArg createTodoArg = new Gson().fromJson(msg, new TypeToken<CreateTodoArg>() {
                    }.getType());

                    externalMessageHandler.dealCreateTodoHandler(createTodoArg);

//                    Result<Void> createTodoResult = externalTodoMsgService.createTodo(createTodoArg);
//                    returnMsg = createTodoResult;
                }
                    break;
                case  OAMessageTag.DEAL_TO_DO_TAG: {
                    DealTodoArg dealTodoArg = new Gson().fromJson(msg, new TypeToken<DealTodoArg>() {
                    }.getType());

                    externalMessageHandler.dealDealTodoHandler(dealTodoArg);

//                    Result<Void> dealTodoResult = externalTodoMsgService.dealTodo(dealTodoArg);
//                    returnMsg = dealTodoResult;
                }
                    break;
                case  OAMessageTag.DELETE_TO_DO: {
                    DeleteTodoArg deleteTodoArg = new Gson().fromJson(msg, new TypeToken<DeleteTodoArg>() {
                    }.getType());

                    externalMessageHandler.dealDeleteTodoHandler(deleteTodoArg);

//                    Result<Void> deleteTodoResult = externalTodoMsgService.deleteTodo(deleteTodoArg);
//                    returnMsg = deleteTodoResult;
                }
                    break;
                default:
                    return Result.newError(ResultCodeEnum.MESSAGE_SEND_ERROR.getCode(),"not supported message type: " + msgType);
            }
            return returnMsg;
        }catch (Exception e) {
            LogUtils.error("trace send get exception, ", e);
            return Result.newError(ResultCodeEnum.MESSAGE_SEND_ERROR);
        }
    }
}