package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.result.Result;

/**
 * 企业服务
 * <AUTHOR>
 * @date 20220718
 */
public interface CorpService {
    /**
     * 查询并更新企业信息表
     * @param appId
     * @param tenantKey
     * @return
     */
    Result<Void> updateCorpInfo(String appId,String tenantKey);

    /**
     * 获取企业绑定信息
     * @param tenantKey
     * @return
     */
    Result<CorpInfoEntity> getCorpEntity(String tenantKey);
}
