package com.facishare.open.feishu.sync.job;

import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@JobHander(value = "AutoGetContactDataHandler")
@Component
@Slf4j
public class AutoGetContactDataHandler extends IJobHandler {

    @Resource
    private ContactsService contactsService;

    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        try {
            log.info("scan AutoGetContactDataHandler start the job!");
            contactsService.autoGetContactData();
            log.info("scan AutoGetContactDataHandler end the job!");
            return new ReturnT(ReturnT.SUCCESS_CODE, "定时任务调用成功"); //ignorei18n
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }
}