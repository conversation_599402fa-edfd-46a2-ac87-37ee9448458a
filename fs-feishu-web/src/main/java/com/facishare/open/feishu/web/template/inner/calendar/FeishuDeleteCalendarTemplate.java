package com.facishare.open.feishu.web.template.inner.calendar;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.FunctionMsgTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.function.FunctionMsgBase;
import com.facishare.open.erpdss.outer.oa.connector.base.inner.calendar.CalendarTemplate;
import com.facishare.open.feishu.sync.limiter.CrmMessagePullLimiter;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.ExternalCalendarService;
import com.facishare.open.feishu.web.template.model.CalendarObjModel;
import com.facishare.open.order.contacts.proxy.api.service.FsCalendarServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class FeishuDeleteCalendarTemplate extends CalendarTemplate {
    @Resource
    private ExternalCalendarService externalCalendarService;
    @Resource
    private CrmMessagePullLimiter crmMessagePullLimiter;
    @Resource
    private EIEAConverter eieaConverter;

    @Override
    public void filterCalendar(MethodContext context) {
        log.info("FeishuDeleteCalendarTemplate.filterCalendar,context={}",context);
        Gson gson = new Gson();
        CalendarObjModel calendarObjModel = context.getData();
        OuterOaEnterpriseBindEntity enterpriseBindEntity = calendarObjModel.getEnterpriseBindEntity();
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId : enterpriseBindEntity.getAppId();
        Integer fsEi = eieaConverter.enterpriseAccountToId(fsEa);
        String eventType = calendarObjModel.getEventType();

//        //是否推送
//        if (!ConfigCenter.CALENDAR_GRAY_EA.contains(fsEa)) {
//            context.setResult(TemplateResult.newError(null));
//            return;
//        }

        FunctionMsgBase functionMsgBase = new FunctionMsgBase();
        functionMsgBase.setChannel(enterpriseBindEntity.getChannel().name());
        functionMsgBase.setFsEa(fsEa);
        functionMsgBase.setOutEa(outEa);
        functionMsgBase.setAppId(appId);
        functionMsgBase.setType(FunctionMsgTypeEnum.crmCalendarPush.getType());
        functionMsgBase.setEventType(eventType);
        // todo:函数过滤，先放在配置中心吧
        Map<String, String> filterCalendarEaMap = new Gson().fromJson(ConfigCenter.FILTER_MESSAGES_EA, new TypeToken<Map<String, String>>() {
        });

        //根据函数判断是否继续
        if (filterCalendarEaMap.containsKey(fsEa) && StringUtils.isNotEmpty(filterCalendarEaMap.get(fsEa))) {
            Boolean isPull = crmMessagePullLimiter.messageIsPull(fsEi, filterCalendarEaMap.get(fsEa), functionMsgBase);
            if (!isPull) {
                context.setResult(TemplateResult.newError(null));
                return;
            }
        }

        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void sendCalendar(MethodContext context) {
        log.info("FeishuDeleteCalendarTemplate.sendCalendar,context={}",context);
        CalendarObjModel calendarObjModel = context.getData();
        OuterOaEnterpriseBindEntity enterpriseBindEntity = calendarObjModel.getEnterpriseBindEntity();
        String objectId = calendarObjModel.getObjectId();
        Result<Void> result = externalCalendarService.deleteExternalCalendar(enterpriseBindEntity, objectId);
        context.setResult(TemplateResult.newSuccess(result));
    }
}
