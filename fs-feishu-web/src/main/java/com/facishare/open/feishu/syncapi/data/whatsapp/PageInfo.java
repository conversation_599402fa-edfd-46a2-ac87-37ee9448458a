package com.facishare.open.feishu.syncapi.data.whatsapp;

import lombok.Data;

import java.io.Serializable;

@Data
public class PageInfo implements Serializable {
    /**
     * 游标
     */
    private Cursors cursors;
    /**
     * next 不为空可向后翻页
     */
    private String next;
    /**
     * previous不为空可向前翻页
     */
    private String previous;

    @Data
    public static class Cursors implements Serializable {
        /**
         * 游标值(上一页)
         */
        private String before;
        /**
         * 游标值(下一页)
         */
        private String after;
    }
}
