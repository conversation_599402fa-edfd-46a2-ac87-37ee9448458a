package com.facishare.open.feishu.syncapi.enums;

import java.io.Serializable;

/**
 * 消息接收者id类型 open_id/user_id/union_id/email/chat_id
 *
 * 示例值："open_id"
 *
 * 可选值有：
 *
 * open_id：以open_id来识别用户(什么是 Open ID？)
 * user_id：以user_id来识别用户。需要有获取用户 userID的权限 (什么是 User ID？)
 * union_id：以union_id来识别用户(什么是 Union ID？)
 * email：以email来识别用户。是用户的真实邮箱
 * chat_id：以chat_id来识别群聊。群ID说明请参考：群ID 说明
 */
public enum ReceiverIdTypeEnum implements Serializable {
    open_id,
    user_id,
    union_id,
    email,
    chat_id
}