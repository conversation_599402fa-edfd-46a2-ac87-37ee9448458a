package com.facishare.open.feishu.syncapi.model;

import com.facishare.open.outer.oa.connector.common.api.info.EmployeeBindModel;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class EmployeeUnBindModel implements Serializable {
    private List<EmployeeBindModel.OutEmployee> outEmployeeList = new ArrayList<>();
    private List<EmployeeBindModel.FsEmployee> fsEmployeeList = new ArrayList<>();
}
