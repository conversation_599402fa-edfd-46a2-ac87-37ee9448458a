package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.syncapi.model.event2.FeishuGroupChatAddBotV1Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.MsgService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * im.message.receive_v1 机器人进群事件处理器
 */
@Component
public class GroupChatAddBotV1EventHandler extends FeishuEventHandler {
    @Resource
    private MsgService msgService;
    @Override
    public String getSupportEventType() {
        return "im.chat.member.bot.added_v1";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        FeishuGroupChatAddBotV1Event event = JSON.parseObject(eventData, FeishuGroupChatAddBotV1Event.class);
        LogUtils.info("GroupChatAddBotV1EventHandler.handle,event={}",event);
        Result<Void> result = msgService.GroupChatAddBotToSendMessage(header.getAppId(),
                header.getTenantKey(),
                event.getChatId());
        LogUtils.info("GroupChatAddBotV1EventHandler.handle,result={}",result);
        return SUCCESS;
    }
}
