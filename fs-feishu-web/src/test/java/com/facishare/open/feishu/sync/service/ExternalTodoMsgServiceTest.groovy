package com.facishare.open.feishu.sync.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager
import com.facishare.open.feishu.sync.service.impl.ExternalTodoMsgServiceImpl
import com.facishare.open.feishu.syncapi.arg.CreateTodoPushArg
import com.facishare.open.feishu.syncapi.arg.DealTodoPushArg
import com.facishare.open.feishu.syncapi.arg.DeleteTodoPushArg
import com.facishare.open.feishu.syncapi.arg.SendTextCardMessagePushArg
import com.facishare.open.feishu.syncapi.arg.SendTextMessagePushArg
import com.facishare.open.feishu.syncapi.config.OAMessageTag
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum
import com.facishare.open.feishu.syncapi.enums.OutOaMsgEventTypeEnum
import com.facishare.open.feishu.syncapi.model.event.ExternalDealTodoEvent
import com.facishare.open.feishu.syncapi.proto.OutOaMsgChangeProto
import com.facishare.open.feishu.web.handler.ExternalMessageHandler
import com.facishare.open.feishu.web.template.inner.msg.FeishuCreateTodoTemplate
import com.facishare.open.feishu.web.template.inner.msg.FeishuDealTodoTemplate
import com.facishare.open.feishu.web.template.inner.msg.FeishuDeleteTodoTemplate
import com.facishare.open.feishu.web.template.inner.msg.FeishuSendTextCarMsgTemplate
import com.facishare.open.feishu.web.template.inner.msg.FeishuSendTextMsgTemplate
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils
import com.fxiaoke.message.extrnal.platform.model.KeyValueItem
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg
import com.google.common.collect.Lists
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import org.apache.commons.collections4.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class ExternalTodoMsgServiceTest extends Specification {
    @Autowired
    private ExternalMessageHandler externalMessageHandler
    @Autowired
    private EnterpriseBindManager enterpriseBindManager;
    @Autowired
    private ExternalTodoMsgServiceImpl externalTodoMsgService
    @Resource
    private FeishuCreateTodoTemplate feishuCreateTodoTemplate;
    @Resource
    private FeishuDealTodoTemplate feishuDealTodoTemplate;
    @Resource
    private FeishuDeleteTodoTemplate feishuDeleteTodoTemplate;
    @Resource
    private FeishuSendTextMsgTemplate feishuSendTextMsgTemplate;
    @Resource
    private FeishuSendTextCarMsgTemplate feishuSendTextCarMsgTemplate;

    def "createTodo"() {
        given:
//        String t = "{\"bizType\":\"452\",\"content\":\"客户(2024-12-26 14:03)\",\"ea\":\"90429\",\"ei\":90429,\"extraDataMap\":{\"activityId\":\"1\",\"objectApiName\":\"AccountObj\",\"activityInstanceId\":\"\",\"applicantId\":\"1000\",\"workflowInstanceId\":\"676cf1977e95db6da4c36692\",\"taskId\":\"676cf1977e95db6da4c36693\",\"objectId\":\"676cf1953201d90001c4bf79\"},\"form\":[{\"key\":\"流程主题\",\"value\":\"客户(2024-12-26 14:03)\"},{\"key\":\"客户名称\",\"value\":\"czx客户001\"},{\"key\":\"1级行业\",\"value\":\"\"},{\"key\":\"客户级别\",\"value\":\"\"},{\"key\":\"成交状态\",\"value\":\"未成交\"},{\"key\":\"负责人\",\"value\":\"陈宗鑫\"}],\"generateUrlType\":1,\"groupKeys\":[],\"receiverIds\":[1000,1002],\"senderId\":-10000,\"sourceId\":\"676cf1977e95db6da4c36693\",\"title\":\"待处理的CRM审批流程\",\"url\":\"todo?apiname=AccountObj&id=676cf1953201d90001c4bf79&ea=90429\"}";
        String t = "{\"ea\":\"85903\",\"ei\":85903,\"senderId\":0,\"receiverIds\":[1001, 1000],\"sourceId\":\"66cec4366201a634fe236b3e016\",\"bizType\":\"452\",\"url\":\"todo?apiname\\u003dAccountObj\\u0026id\\u003d66cec4347c3e710007b21d6e\\u0026ea\\u003d83998\",\"title\":\"待处理的审批流程\",\"content\":\"客户(2024-08-01 16:29)\",\"form\":[{\"key\":\"测试失败\",\"value\":\"test001\"},{\"key\":\"流程主题\",\"value\":\"客户(2024-08-01 16:29)\"},{\"key\":\"客户名称\",\"value\":\"啦啦啦-客户010\"},{\"key\":\"1级行业\",\"value\":\"\"},{\"key\":\"客户级别\",\"value\":\"\"},{\"key\":\"成交状态\",\"value\":\"未成交\"},{\"key\":\"负责人\",\"value\":\"陈宗鑫\"},{\"key\":\"多余的\",\"value\":\"飞书只允许写5行，这个看下会不会展示，还是说只展示后5行\"}],\"generateUrlType\":1,\"extraDataMap\":{\"activityId\":\"1\",\"objectApiName\":\"AccountObj\",\"applicantId\":\"1000\",\"workflowInstanceId\":\"66cec436913d0a6c46fc6dd2\",\"objectId\":\"66cec4347c3e710007b21d6e\",\"taskId\":\"66cec4366201a634fe236b3e\"},\"appId\":\"FSAID_11490d9e\"}";
        CreateTodoArg arg = new Gson().fromJson(t, CreateTodoArg.class)
        String b = new Gson().toJson(arg);
        println b
        def a = externalMessageHandler.dealCreateTodoHandler(arg)
        print(a)
    }

    def "deleteTodo"() {
        given:
        DeleteTodoArg var1 = new DeleteTodoArg()
        var1.setBizType("452")
        var1.setEa("90429")
        var1.setDeleteEmployeeIds(Lists.newArrayList(1000))
        var1.setSourceId("676cf1977e95db6da4c36693")
        expect:
        def a = externalMessageHandler.dealDeleteTodoHandler(var1)
        print(a)
    }

    def "dealTodo"() {
        given:
        DealTodoArg var1 = new DealTodoArg()
        var1.setBizType("452")
        var1.setEa("85903")
        String extDataMapJson = "{\"opinions\":[{\"reply_user\":[\"1001\"],\"feedId\":\"100000091\",\"action_type\":\"addTag\",\"reply_time\":1740481069109,\"id\":\"67bda22d606b6f201c98bbdd\",\"opinion\":\"agree\"},{\"reply_user\":[\"1001\"],\"feedId\":\"100000091\",\"action_type\":\"reject\",\"reply_time\":1750481069109,\"id\":\"67bda22d606b6f201c98bbdd\",\"opinion\":\"reject\"},{\"reply_user\":[\"1001\"],\"feedId\":\"100000091\",\"action_type\":\"agree\",\"reply_time\":1740481181471,\"id\":\"67bda29d606b6f201c98bbe0\",\"opinion\":\"pass\"}],\"state\":\"pass\"}";
        // 使用 Gson 解析 extDataMap
        Gson gson = new Gson();
        // 将 JSON 字符串转换为 Map
        Map<String, Object> extDataMap = gson.fromJson(extDataMapJson, new TypeToken<Map<String, Object>>() {}.getType());
        extDataMap.put("state", "reject")
        var1.setExtDataMap(extDataMap)
        var1.setOperators(Lists.newArrayList(1001))
        var1.setSourceId("66cec4366201a634fe236b3e016")
        expect:
        def a = externalMessageHandler.dealDealTodoHandler(var1)
        print(a)
    }

    def "testThreadDealTodo"() {
        expect:
        new Thread({ ->
            DealTodoArg var1 = new DealTodoArg()
            var1.setBizType("452")
            var1.setEa("85903")
            var1.setOperators(Lists.newArrayList(1000, 1001))
            var1.setSourceId("test012")
            externalMessageHandler.dealDealTodoHandler(var1)
        }).start()

        new Thread({ ->
            CreateTodoArg arg = new CreateTodoArg();
            arg.setGenerateUrlType(1)
            arg.setEa("85903")
            arg.setEi(85903)
            arg.setReceiverIds(Lists.newArrayList(1000, 1001))
            arg.setContent("字节天天")
            arg.setTitle("待处理的审批流程")
            arg.setBizType("452")
            List<KeyValueItem> form = new LinkedList<>()
            KeyValueItem valueItem = new KeyValueItem("公司名称", "字节和心脏")
            KeyValueItem valueItem1 = new KeyValueItem("啊行吧", "啊对吧钉钉")
            KeyValueItem valueItem2 = new KeyValueItem("电话", "132226666666")
            form.add(valueItem)
            form.add(valueItem1)
            form.add(valueItem2)
            arg.setForm(form)
            arg.setUrl("bpm?workflowInstanceId=635f6a910110be7bacbb40c5&ea=fsbcsh3382")
            arg.setSourceId("test012")
            Map<String, String> extraDataMap = new HashMap<>()
            extraDataMap.put("workflowInstanceId", "635f6a910110be7bacbb40c5")
            extraDataMap.put("activityId", "635f6a910110be7bacbb40c6")
            extraDataMap.put("objectApiName", "AccountObj")
            extraDataMap.put("objectId", "65a772295b1d590007a4a183")
            arg.setExtraDataMap(extraDataMap)
            externalMessageHandler.dealCreateTodoHandler(arg)
        }).start()
        try {
            Thread.sleep(15000L)
        } finally {
            print ""
        }
    }

    def "testThreadDeleteTodo"() {
        expect:
//        new Thread({ ->
//            DeleteTodoArg var1 = new DeleteTodoArg()
//            var1.setBizType("452")
//            var1.setEa("85903")
//            var1.setDeleteEmployeeIds(Lists.newArrayList(1000, 1001))
//            var1.setSourceId("test015")
//            externalTodoMsgService.deleteTodo(var1)
//        }).start()

        new Thread({ ->
            CreateTodoArg arg = new CreateTodoArg();
            arg.setGenerateUrlType(1)
            arg.setEa("85903")
            arg.setEi(85903)
            arg.setReceiverIds(Lists.newArrayList(1002))
            arg.setContent("字节天天")
            arg.setTitle("待处理的审批流程")
            arg.setBizType("452")
            List<KeyValueItem> form = new LinkedList<>()
            KeyValueItem valueItem = new KeyValueItem("公司名称", "字节和心脏")
            KeyValueItem valueItem1 = new KeyValueItem("啊行吧", "啊对吧钉钉")
            KeyValueItem valueItem2 = new KeyValueItem("电话", "13888888888")
            form.add(valueItem)
            form.add(valueItem1)
            form.add(valueItem2)
            arg.setForm(form)
            arg.setUrl("bpm?workflowInstanceId=635f6a910110be7bacbb40c5&ea=fsbcsh3382")
            arg.setSourceId("test015")
            Map<String, String> extraDataMap = new HashMap<>()
            extraDataMap.put("workflowInstanceId", "635f6a910110be7bacbb40c5")
            extraDataMap.put("activityId", "635f6a910110be7bacbb40c6")
            extraDataMap.put("objectApiName", "AccountObj")
            extraDataMap.put("objectId", "65a772295b1d590007a4a183")
            arg.setExtraDataMap(extraDataMap)
            externalMessageHandler.dealCreateTodoHandler(arg)
        }).start()

        new Thread({ ->
            DeleteTodoArg var1 = new DeleteTodoArg()
            var1.setBizType("452")
            var1.setEa("85903")
            var1.setDeleteEmployeeIds(Lists.newArrayList(1000, 1001))
            var1.setSourceId("test015")
            externalMessageHandler.dealDeleteTodoHandler(var1)
        }).start()
        try {
            Thread.sleep(30000L)
        } finally {
            print ""
        }
    }

    def "dealCrmTodo"() {
        given:
//        ExternalDealTodoEvent externalDealTodoEvent = new ExternalDealTodoEvent()
//        externalDealTodoEvent.setActionType("APPROVE")
//        externalDealTodoEvent.setMessageId("7398091176720039939")
//        externalDealTodoEvent.setReason("同意")
//        externalDealTodoEvent.setUserId("6effc51e")
//        externalDealTodoEvent.setToken("BiyJ6wa7yhYe6nvXNcZBOe0qix46kgWO001")

//        String plainText = "{\"action_type\":\"APPROVE\",\"action_context\":\"{\\\"MsgType\\\":\\\"commonMsg\\\",\\\"fsEa\\\":\\\"90429\\\",\\\"fsUserId\\\":\\\"1000\\\",\\\"taskId\\\":\\\"676cf1977e95db6da4c36693\\\"}\",\"user_id\":\"6effc51e\",\"message_id\":7452610202714701826,\"token\":\"BiyJ6wa7yhYe6nvXNcZBOe0qix46kgWO001\"}";
        String plainText = "{\"action_type\":\"APPROVE\",\"action_context\":\"{\\\"MsgType\\\":\\\"interconnectMsg\\\",\\\"fsEa\\\":\\\"89764\\\",\\\"upstreamEa\\\":\\\"83998\\\" ,\\\"fsUserId\\\":\\\"1003\\\",\\\"taskId\\\":\\\"66cec4366201a634fe236b3e\\\"}\",\"user_id\":\"6effc51e\",\"message_id\":7452671738200473602,\"token\":\"BiyJ6wa7yhYe6nvXNcZBOe0qix46kgWO001\"}";
        ExternalDealTodoEvent externalDealTodoEvent = JSON.parseObject(plainText, ExternalDealTodoEvent.class);
        expect:
        def a = externalTodoMsgService.dealCrmTodo(externalDealTodoEvent)
        print(a)
    }

    def "retryCrmTodo"() {
        given:
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity("89764", "100d08b69448975d");
        expect:
        def a = externalTodoMsgService.retryCrmTodo(entity)
        print(a)
    }

    def "feishuMsgChangeListener"() {
        given:
        expect:
        OutOaMsgChangeProto oaMsgChangeProto = new OutOaMsgChangeProto();
        oaMsgChangeProto.setMsgType(OutOaMsgEventTypeEnum.interconnectMsg.name());
        String t = "{\"msgType\":\"interconnectMsg\",\"upstreamEa\":\"83998\",\"createTodoArg\":{\"ea\":\"89764\",\"ei\":89764,\"senderId\":0,\"receiverIds\":[1003,1002],\"sourceId\":\"66cec4366201a634fe236b3e006\",\"bizType\":\"452\",\"url\":\"todo?apiname\\u003dAccountObj\\u0026id\\u003d66cec4347c3e710007b21d6e\\u0026ea\\u003d83998\",\"title\":\"待处理的审批流程\",\"content\":\"客户(2024-08-01 16:29)\",\"form\":[{\"key\":\"测试失败\",\"value\":\"test001\"},{\"key\":\"流程主题\",\"value\":\"客户(2024-08-01 16:29)\"},{\"key\":\"客户名称\",\"value\":\"啦啦啦-客户009\"},{\"key\":\"1级行业\",\"value\":\"\"},{\"key\":\"客户级别\",\"value\":\"\"},{\"key\":\"成交状态\",\"value\":\"未成交\"},{\"key\":\"负责人\",\"value\":\"陈宗鑫\"},{\"key\":\"多余的\",\"value\":\"飞书只允许写5行，这个看下会不会展示，还是说只展示后5行\"}],\"generateUrlType\":1,\"extraDataMap\":{\"activityId\":\"1\",\"objectApiName\":\"AccountObj\",\"applicantId\":\"1000\",\"workflowInstanceId\":\"66cec436913d0a6c46fc6dd2\",\"objectId\":\"66cec4347c3e710007b21d6e\",\"taskId\":\"66cec4366201a634fe236b3e\"},\"appId\":\"FSAID_11490d9e\"}}";
        CreateTodoPushArg arg = new Gson().fromJson(t, CreateTodoPushArg.class);
        String receiveTags = OAMessageTag.CREATE_TO_DO_TAG;
//        String t = "{\"msgType\":\"interconnectMsg\",\"upstreamEa\":\"83998\",\"sendTextCardMessageArg\":{\"ea\":\"89764\",\"ei\":89764,\"senderId\":0,\"receiverIds\":[1003,1002],\"title\":\"销售订单被修改\",\"messageContent\":\"长沙齐名国际物流有限公司，订单金额：2371.00元，下单日期：2022-10-25\",\"url\":\"https://crm.ceshi112.com/dps/preview/bypath?path\\u003dN_202402_19_a052ba0914534774af84c1baec32ec7b.xlsx\\u0026showHeader\\u003d1\",\"form\":[{\"key\":\"啊行\",\"value\":\"啊对\"},{\"key\":\"啊行吧\",\"value\":\"啊对吧\"}],\"receiverChannelType\":1,\"receiverChannelData\":\"{\\\"appId\\\":\\\"WJTZ\\\"}\",\"generateUrlType\":1,\"extraDataMap\":{\"activityId\":\"1.xlsx\",\"objectApiName\":\"AccountObj\",\"applicantId\":\"1000\",\"workflowInstanceId\":\"66cec436913d0a6c46fc6dd2\",\"objectId\":\"66cec4347c3e710007b21d6e\",\"taskId\":\"66cec4366201a634fe236b3e\"},\"appId\":\"FSAID_11490d9e\"}}";
//        SendTextCardMessagePushArg arg = new Gson().fromJson(t, SendTextCardMessagePushArg.class);
//        oaMsgChangeProto.setData(new Gson().toJson(arg));
//        String receiveTags = OAMessageTag.CARD_MSG_TAG;
        switch (receiveTags) {
            case OAMessageTag.CREATE_TO_DO_TAG:
                if (oaMsgChangeProto.getMsgType().equals(OutOaMsgEventTypeEnum.interconnectMsg.name())) {
                    //目前处理互联待办
                    CreateTodoPushArg createTodoPushArg = JSONObject.parseObject(oaMsgChangeProto.getData(), CreateTodoPushArg.class);
                    //查询所有绑定关系
                    List<EnterpriseBindEntity> enterpriseBindEntities = enterpriseBindManager.getEntityList(createTodoPushArg.getCreateTodoArg().getEa(), BindStatusEnum.normal);
                    if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
                        break;
                    }
                    for (EnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
                        try {
                            createTodoPushArg.setEnterpriseBindEntity(enterpriseBindEntity);
                            feishuCreateTodoTemplate.execute(createTodoPushArg);
                        } catch (Exception e) {
                            LogUtils.info("FeishuMsgChangeListener.consumeMessage.enterpriseBindEntity={}", enterpriseBindEntity);
                        }
                    }

                }
                break;
            case OAMessageTag.DEAL_TO_DO_TAG:
                if (oaMsgChangeProto.getMsgType().equals(OutOaMsgEventTypeEnum.interconnectMsg.name())) {
                    DealTodoPushArg dealTodoPushArg = JSONObject.parseObject(oaMsgChangeProto.getData(), DealTodoPushArg.class);
                    List<EnterpriseBindEntity> enterpriseBindEntities = enterpriseBindManager.getEntityList(dealTodoPushArg.getDealTodoArg().getEa(), BindStatusEnum.normal);
                    if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
                        break;
                    }
                    for (EnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
                        try {
                            dealTodoPushArg.setEnterpriseBindEntity(enterpriseBindEntity);
                            feishuDealTodoTemplate.execute(dealTodoPushArg);
                        } catch (Exception e) {
                            LogUtils.info("FeishuMsgChangeListener.consumeMessage.enterpriseBindEntity={}", enterpriseBindEntity);
                        }
                    }
                }

                break;
            case OAMessageTag.DELETE_TO_DO:
                if (oaMsgChangeProto.getMsgType().equals(OutOaMsgEventTypeEnum.interconnectMsg.name())) {
                    DeleteTodoPushArg deleteTodoPushArg = JSONObject.parseObject(oaMsgChangeProto.getData(), DeleteTodoPushArg.class);
                    List<EnterpriseBindEntity> enterpriseBindEntities = enterpriseBindManager.getEntityList(deleteTodoPushArg.getDeleteTodoArg().getEa(), BindStatusEnum.normal);
                    if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
                        break;
                    }
                    for (EnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
                        try {
                            deleteTodoPushArg.setEnterpriseBindEntity(enterpriseBindEntity);
                            feishuDeleteTodoTemplate.execute(deleteTodoPushArg);
                        } catch (Exception e) {
                            LogUtils.info("FeishuMsgChangeListener.consumeMessage.enterpriseBindEntity={}", enterpriseBindEntity);
                        }
                    }
                }
                break;
            case OAMessageTag.TEXT_MSG_TAG:
                if (oaMsgChangeProto.getMsgType().equals(OutOaMsgEventTypeEnum.interconnectMsg.name())) {
                    SendTextMessagePushArg sendTextMessagePushArg = JSONObject.parseObject(oaMsgChangeProto.getData(), SendTextMessagePushArg.class);
                    List<EnterpriseBindEntity> enterpriseBindEntities = enterpriseBindManager.getEntityList(sendTextMessagePushArg.getSendTextMessageArg().getEa(), BindStatusEnum.normal);
                    if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
                        break;
                    }
                    for (EnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
                        try {
                            sendTextMessagePushArg.setEnterpriseBindEntity(enterpriseBindEntity);
                            feishuSendTextMsgTemplate.execute(sendTextMessagePushArg);
                        } catch (Exception e) {
                            LogUtils.info("FeishuMsgChangeListener.consumeMessage.enterpriseBindEntity={}", enterpriseBindEntity);
                        }
                    }
                }
                break;
            case OAMessageTag.CARD_MSG_TAG:
                if (oaMsgChangeProto.getMsgType().equals(OutOaMsgEventTypeEnum.interconnectMsg.name())) {
                    SendTextCardMessagePushArg sendTextCardMessagePushArg = JSONObject.parseObject(oaMsgChangeProto.getData(), SendTextCardMessagePushArg.class);
                    List<EnterpriseBindEntity> enterpriseBindEntities = enterpriseBindManager.getEntityList(sendTextCardMessagePushArg.getSendTextCardMessageArg().getEa(), BindStatusEnum.normal);
                    if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
                        break;
                    }
                    for (EnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
                        try {
                            sendTextCardMessagePushArg.setEnterpriseBindEntity(enterpriseBindEntity);
                            feishuSendTextCarMsgTemplate.execute(sendTextCardMessagePushArg);
                        } catch (Exception e) {}
                    }
                }
                break;
            default:
                break;
        }
    }
}
