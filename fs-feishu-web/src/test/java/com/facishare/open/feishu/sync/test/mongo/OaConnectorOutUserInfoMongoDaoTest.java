package com.facishare.open.feishu.sync.test.mongo;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorOutUserInfoMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorOutUserInfoDoc;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.mongodb.client.result.DeleteResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedList;
import java.util.List;

public class OaConnectorOutUserInfoMongoDaoTest extends BaseTest {
    @Autowired
    private OaConnectorOutUserInfoMongoDao oaConnectorOutUserInfoMongoDao;

    @Test
    public void batchReplace() {

    }

    @Test
    public void deleteDepartmentInfoByUserId() {
        DeleteResult deleteResult = oaConnectorOutUserInfoMongoDao.deleteUserInfoByUserId(ChannelEnum.feishu,
                "100d7a94d39e575e",
                null,
                "test1");
        deleteResult = oaConnectorOutUserInfoMongoDao.deleteUserInfoByUserId(ChannelEnum.feishu,
                "100d7a94d39e575e",
                null,
                "test2");
        System.out.println(deleteResult);
    }

    @Test
    public void queryDepartmentInfos() {
        List<OaConnectorOutUserInfoDoc> docs = oaConnectorOutUserInfoMongoDao.queryUserInfos(ChannelEnum.feishu, "100d7a94d39e575e", null);
        System.out.println(docs);
    }

    @Test
    public void deleteNotInCollectionDocs() {
        List<OaConnectorOutUserInfoDoc> docs = new LinkedList<>();
//        OaConnectorOutUserInfoDoc doc = new OaConnectorOutUserInfoDoc();
//        doc.setChannel(ChannelEnum.feishu);
//        doc.setCreateTime(System.currentTimeMillis());
//        doc.setUpdateTime(System.currentTimeMillis());
//        doc.setOutEa("100d08b69448975d");
//        doc.setOutUserId("ou_aa822be0b3d68a3340d7bdc16ebb8b34");
//        doc.setOutUserInfo("{xxxxxx}");
//        docs.add(doc);
        DeleteResult deleteResult = oaConnectorOutUserInfoMongoDao.deleteNotInCollectionDocs(ChannelEnum.feishu, "100d08b69448975d", null, docs);
        System.out.println(deleteResult);
    }
}
