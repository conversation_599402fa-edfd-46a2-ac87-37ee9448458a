package com.facishare.open.feishu.sync.test.service;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.outer.oa.connector.common.api.info.EmployeeBindModel;
import com.facishare.open.feishu.syncapi.model.EmployeeUnBindModel;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactUserUpdatedV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.model.info.EmployeesBindSyncInfo;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.outer.oa.connector.common.api.info.PageModel;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

public class EmployeeBindServiceTest extends BaseTest {
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;

    @Test
    public void getAppInstallerInfo() {
        Result<UserData.User> result = employeeBindService.getAppInstallerInfo("cli_a20192f6afb8d00c",
                "102b932c02c4d75e");
        System.out.println(result);
    }

    @Test
    public void getAppAdminInfo() {
        Result<UserData.User> result = employeeBindService.getAppAdminInfo("cli_a20192f6afb8d00c",
                "102b932c02c4d75e");
        System.out.println(result);
    }


    @Test
    public void queryFsUnbind(){
        Result<PageModel<List<EmployeeBindModel.FsEmployee>>> result = employeeBindService.queryFsUnbind("ddqybhzyl","146a0bcf64d7175a",100);
        System.out.println(result);
    }

    @Test
    public void queryOutUnbind(){
        Result<List<EmployeeBindModel.OutEmployee>> result = employeeBindService.queryOutUnbind("ddqybhzyl",
                "146a0bcf64d7175a",
                null,
                100);
        System.out.println(result);
    }

    @Test
    public void queryUnbind(){
        Result<EmployeeUnBindModel> result = employeeBindService.queryUnbind("74860", null,100);
        System.out.println(result);
    }

    @Test
    public void queryOutEmployeeList(){
        Result<List<EmployeeBindModel.OutEmployee>> result = employeeBindService.queryOutEmployeeList("82777",null);
        System.out.println(result);
    }

    @Test
    public void getEnterpriseData() {
        int ei = eieaConverter.enterpriseAccountToId("fscsqy9648");
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseId(ei);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        System.out.println(result);
    }



    @Test
    public void refreshContactScopeDataCacheAsync(){
        //employeeBindService.refreshContactScopeDataCacheAsync("100d08b69448975d");
    }

    @Test
    public void addOrUpdateEmployee(){
        //String Str = "{\"old_object\":{\"nickname\":\"\"},\"object\":{\"country\":\"\",\"work_station\":\"\",\"gender\":0,\"city\":\"\",\"open_id\":\"ou_2aaed12be756dfcb457c5ee14d4ac641\",\"employee_no\":\"\",\"avatar\":{\"avatar_640\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_e1372170-3ddc-45ba-af2d-c5f7b8b6a39g~?image_size=640x640&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_origin\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_e1372170-3ddc-45ba-af2d-c5f7b8b6a39g~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_72\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_e1372170-3ddc-45ba-af2d-c5f7b8b6a39g~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_240\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_e1372170-3ddc-45ba-af2d-c5f7b8b6a39g~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\"},\"department_ids\":[\"od-ff26fd3ecb223b4d81d488348202a71c\"],\"enterprise_email\":\"\",\"join_time\":1684454400,\"employee_type\":1,\"name\":\"大鱼的大\",\"nickname\":\"大鱼好大\",\"union_id\":\"on_7a5da735e4120ecbc1a96cb7bf31cb03\",\"en_name\":\"\",\"orders\":[{\"user_order\":0,\"department_id\":\"od-ff26fd3ecb223b4d81d488348202a71c\",\"is_primary_dept\":true,\"department_order\":1}],\"job_title\":\"\",\"status\":{\"is_activated\":false,\"is_frozen\":false,\"is_resigned\":false,\"is_unjoin\":true,\"is_exited\":false}}}";
        String Str = "{\"old_object\":{\"status\":{\"is_frozen\":false}},\"object\":{\"country\":\"\",\"work_station\":\"\",\"gender\":0,\"city\":\"\",\"open_id\":\"ou_a715502aa11a3bdbd812a09a96e5c322\",\"employee_no\":\"\",\"avatar\":{\"avatar_640\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_ee7c9196-cfa3-43ae-98fd-520b168bcb4g~?image_size=640x640&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_origin\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_ee7c9196-cfa3-43ae-98fd-520b168bcb4g~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_72\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_ee7c9196-cfa3-43ae-98fd-520b168bcb4g~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_240\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_ee7c9196-cfa3-43ae-98fd-520b168bcb4g~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\"},\"department_ids\":[\"od-ddb1fc5db05d8b771ef9799ad8310cad\"],\"enterprise_email\":\"\",\"employee_type\":1,\"name\":\"小杨edit\",\"nickname\":\"\",\"union_id\":\"on_68e938a296c44289cb64f7c693ee437d\",\"en_name\":\"\",\"orders\":[{\"user_order\":0,\"department_id\":\"od-ddb1fc5db05d8b771ef9799ad8310cad\",\"is_primary_dept\":true,\"department_order\":1}],\"job_title\":\"\",\"status\":{\"is_activated\":true,\"is_frozen\":true,\"is_resigned\":false,\"is_unjoin\":false,\"is_exited\":false}}}";
        FeishuContactUserUpdatedV3Event event = JSON.parseObject(Str, FeishuContactUserUpdatedV3Event.class);
        FeishuEventModel2.EventModelHeader header = new FeishuEventModel2.EventModelHeader();
        header.setEventId("e0dc7a6153028dfc60b7f0a81ebeebc6");
        header.setToken("BiyJ6wa7yhYe6nvXNcZBOe0qix46kgWO");
        header.setAppId("cli_a3ddeb52763b100c");
        header.setCreateTime(1684742372000L);
        header.setEventType("contact.user.updated_v3");
        header.setTenantKey("100d7a94d39e575e");
        Result<Void> result = employeeBindService.addOrUpdateEmployee(header, event,null);
        System.out.println(result);
    }

    @Test
    public void getFsEmployeeDetailInfo(){
        employeeBindService.getFsCurEmployeeDetailInfo(74860, 1000);
    }

    @Test
    public void uploadEmployeesBindSyncFile(){
        List<EmployeesBindSyncInfo> employeesBindSyncInfos = new LinkedList<>();
        for(int i = 0; i < 130; i++) {
            int t = 1011;
            EmployeesBindSyncInfo employeesBindSyncInfo = new EmployeesBindSyncInfo();
            employeesBindSyncInfo.setFsEa("85903");
            employeesBindSyncInfo.setFsUserId(String.valueOf(t + i));
            employeesBindSyncInfo.setDisplayId("F850420834");
            employeesBindSyncInfo.setMobile(String.valueOf(t + i));
            employeesBindSyncInfos.add(employeesBindSyncInfo);
        }
        EmployeesBindSyncInfo employeesBindSyncInfo = new EmployeesBindSyncInfo();
        employeesBindSyncInfo.setFsEa("85903");
        employeesBindSyncInfo.setFsUserId("1002");
        employeesBindSyncInfo.setDisplayId("F850420834");
        employeesBindSyncInfo.setMobile("18926584793");
        employeesBindSyncInfos.add(employeesBindSyncInfo);

        employeesBindSyncInfo = new EmployeesBindSyncInfo();
        employeesBindSyncInfo.setFsEa("85903");
        employeesBindSyncInfo.setDisplayId("F850420834");
        employeesBindSyncInfo.setFsUserId("1003");
        employeesBindSyncInfo.setMobile("17328769505");
        employeesBindSyncInfos.add(employeesBindSyncInfo);

        employeesBindSyncInfo = new EmployeesBindSyncInfo();
        employeesBindSyncInfo.setFsEa("85903");
        employeesBindSyncInfo.setDisplayId("F850420834");
        employeesBindSyncInfo.setFsUserId("1001");
        employeesBindSyncInfo.setMobile("13711911803");
        employeesBindSyncInfos.add(employeesBindSyncInfo);
        Result<Void> result = employeeBindService.uploadEmployeesBindSyncFile(employeesBindSyncInfos);
        System.out.println(result);
    }

    @Test
    public void FsEmployeeInfoCache(){
        com.facishare.open.order.contacts.proxy.api.result.Result<List<EmployeeDto>> listResult = fsEmployeeServiceProxy.batchGetEmployeeDto(85903, Lists.newArrayList(1006));
        EmployeeDto employeeDto = listResult.getData().get(0);
        Result<List<EmployeeDto>> result = employeeBindService.fsEmployeeInfoCache("85903", employeeDto);
        System.out.println(result);
    }
}
