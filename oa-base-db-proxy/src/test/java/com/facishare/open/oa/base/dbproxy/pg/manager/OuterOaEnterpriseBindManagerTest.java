package com.facishare.open.oa.base.dbproxy.pg.manager;

import com.facishare.open.oa.base.dbproxy.BaseTest;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.junit.Test;

import javax.annotation.Resource;

public class OuterOaEnterpriseBindManagerTest extends BaseTest {
    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Test
    public void getEntity() {
        OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.feishu, "90429", "16bdc45070d5975f", "appId");
        System.out.println(entity);
    }

    @Test
    public void intEntity() {
        OuterOaEnterpriseBindEntity entity = new OuterOaEnterpriseBindEntity();
        entity.setChannel(ChannelEnum.feishu);
        entity.setFsEa("90429");
        entity.setOutEa("16bdc45070d5975f");
        entity.setAppId("appId");
        entity.setConnectInfo("{}");
        entity.setBindType(BindTypeEnum.auto);
        entity.setBindStatus(BindStatusEnum.normal);
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());

        Integer c = outerOaEnterpriseBindManager.insert(entity);
        System.out.println(c);
    }
}
