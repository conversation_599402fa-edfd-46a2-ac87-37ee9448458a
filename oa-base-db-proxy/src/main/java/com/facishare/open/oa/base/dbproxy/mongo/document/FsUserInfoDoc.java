package com.facishare.open.oa.base.dbproxy.mongo.document;

import lombok.*;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.io.Serializable;

/**
 * 纷享人员详情文档模板
 * <AUTHOR>
 * @date 2023/12/06
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class FsUserInfoDoc implements Serializable {
    /**
     * 唯一标识
     */
    @BsonId
    private ObjectId id;
    private String fsEa;
    private Integer fsUserId;
    private Integer status;
    private String fsUserInfo;
    /**
     * 数据创建时间
     */
    private Long createTime;
    /**
     * 数据更新时间
     */
    private Long updateTime;
}
