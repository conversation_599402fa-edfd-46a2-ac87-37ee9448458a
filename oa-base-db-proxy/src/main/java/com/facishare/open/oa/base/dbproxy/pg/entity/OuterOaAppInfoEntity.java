package com.facishare.open.oa.base.dbproxy.pg.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.outer.oa.connector.common.api.annotation.SecurityField;
import com.facishare.open.outer.oa.connector.common.api.annotation.SecurityObj;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * OuterOaAppInfoEntity
 * 应用信息实体类
 */
@Data
@TableName("outer_oa_app_info")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@SecurityObj(value = OuterOaAppInfoEntity.class)
public class OuterOaAppInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 渠道
     */
    private ChannelEnum channel;

    /**
     * 外部企业ea
     */
    private String outEa;

    /**
     * 外部应用appId
     */
    private String appId;

    /**
     * 应用类型
     */
    private OuterOaAppInfoTypeEnum appType;

    /**
     * 应用信息
     */
    @SecurityField
    private String appInfo;

    /**
     * 应用状态
     */
    private OuterOaAppInfoStatusEnum status;

    /**
     * 创建时间（long类型时间戳）
     */
    private Long createTime;

    /**
     * 修改时间（long类型时间戳）
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateTime;
}
