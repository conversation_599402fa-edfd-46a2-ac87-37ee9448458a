package com.facishare.open.oa.base.dbproxy.pg.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindEventTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindMsgTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * OuterOaMessageBindEntity
 * 消息绑定实体类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("outer_oa_message_bind")
public class OuterOaMessageBindEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 渠道标识
     */
    private ChannelEnum channel;

    /**
     * 数据中心ID
     */
    private String dcId;

    /**
     * 纷享企业标识
     */
    private String fsEa;

    /**
     * 外部企业标识
     */
    private String outEa;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 事件类型（如普通消息、互联消息）
     */
    private OuterOaMessageBindEventTypeEnum eventType;

    /**
     * 消息类型（如机器人待办、审批中心待办）
     */
    private OuterOaMessageBindMsgTypeEnum messageType;

    /**
     * 选用的模板ID
     */
    private String templateId;

    /**
     * 纷享消息ID
     */
    private String sourceId;

    /**
     * 外部消息ID
     */
    private String taskId;

    /**
     * 推送的外部人员ID
     */
    private String outUserId;

    /**
     * 消息JSON数据
     */
    private String messageInfo;

    /**
     * 状态
     */
    private OuterOaMessageBindStatusEnum status;

    /**
     * 创建时间（long类型时间戳）
     */
    private Long createTime;

    /**
     * 更新时间（long类型时间戳）
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateTime;
}
