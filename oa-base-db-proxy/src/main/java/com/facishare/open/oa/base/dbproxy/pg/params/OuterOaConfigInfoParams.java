package com.facishare.open.oa.base.dbproxy.pg.params;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaConfigInfoTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * OuterOaConfigInfoParams
 * 配置信息参数类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OuterOaConfigInfoParams implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 渠道
     */
    private ChannelEnum channel;

    /**
     * 数据中心ID
     */
    private String dcId;

    /**
     * 纷享企业ea
     */
    private String fsEa;

    /**
     * 外部企业ea
     */
    private String outEa;

    /**
     * 外部应用appId
     */
    private String appId;

    /**
     * 类型
     */
    private OuterOaConfigInfoTypeEnum type;

    /**
     * 配置信息
     */
    private String configInfo;

    /**
     * 创建时间（long类型时间戳）
     */
    private Long createTime;

    /**
     * 修改时间（long类型时间戳）
     */
    private Long updateTime;
} 