package com.facishare.open.oa.base.dbproxy.pg.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.oa.base.dbproxy.pg.entity.QyweixinBusinessInfoBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.QyweixinBusinessInfoBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.QyweixinBusinessInfoBindParams;
import com.fxiaoke.api.IdGenerator;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * QyweixinBusinessInfoBindManager
 * 企业微信业务信息绑定的管理器
 */
@Component
public class QyweixinBusinessInfoBindManager {

    @Resource
    private QyweixinBusinessInfoBindMapper qyweixinBusinessInfoBindMapper;

    public Integer insert(QyweixinBusinessInfoBindEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            entity.setId(IdGenerator.get());
        }
        return qyweixinBusinessInfoBindMapper.insert(entity);
    }

    public Integer updateById(QyweixinBusinessInfoBindEntity entity) {
        return qyweixinBusinessInfoBindMapper.updateById(entity);
    }

    public Integer batchUpsertInfos(List<QyweixinBusinessInfoBindEntity> businessInfoBindEntities) {
        if (ObjectUtils.isEmpty(businessInfoBindEntities)) {
            return 0;
        }
//        for (QyweixinBusinessInfoBindEntity businessInfoBindEntity : businessInfoBindEntities) {
//            if (StringUtils.isEmpty(businessInfoBindEntity.getId())) {
//                businessInfoBindEntity.setId(IdGenerator.get());
//            }
//        }
        return qyweixinBusinessInfoBindMapper.batchUpsertInfos(businessInfoBindEntities);
    }

    /**
     * 根据参数查询企业微信业务信息绑定列表
     * @param params 查询参数
     * @return 企业微信业务信息绑定实体列表
     */
    public List<QyweixinBusinessInfoBindEntity> getEntities(QyweixinBusinessInfoBindParams params) {
        LambdaQueryWrapper<QyweixinBusinessInfoBindEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(params.getFsEa())) {
            wrapper.eq(QyweixinBusinessInfoBindEntity::getFsEa, params.getFsEa());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(QyweixinBusinessInfoBindEntity::getOutEa, params.getOutEa());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(QyweixinBusinessInfoBindEntity::getAppId, params.getAppId());
        }
        if (StringUtils.isNotEmpty(params.getBusinessType())) {
            wrapper.eq(QyweixinBusinessInfoBindEntity::getBusinessType, params.getBusinessType());
        }
        if (params.getStatus() != null) {
            wrapper.eq(QyweixinBusinessInfoBindEntity::getStatus, params.getStatus());
        }

        return qyweixinBusinessInfoBindMapper.selectList(wrapper);
    }
} 