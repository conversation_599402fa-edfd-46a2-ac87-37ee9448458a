package com.facishare.open.aliyun.market.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/aliyun/market")
public class TestController {
    @RequestMapping(value = "/test", method = RequestMethod.GET)
    public String test(@RequestParam String name) {
        return "hello " + name;
    }
}