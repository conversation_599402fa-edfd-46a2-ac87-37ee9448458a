package com.facishare.open.aliyun.market.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.aliyun.market.entity.EnterpriseBindEntity;
import com.facishare.open.aliyun.market.manager.EnterpriseBindManager;
import com.facishare.open.aliyun.market.model.login.FsUserModel;
import com.facishare.open.aliyun.market.model.login.LoginAuthModel;
import com.facishare.open.aliyun.market.model.login.UserTicketModel;
import com.facishare.open.aliyun.market.result.Result;
import com.facishare.open.aliyun.market.result.ResultCodeEnum;
import com.facishare.open.aliyun.market.result.data.Code2UserInfoData;
import com.facishare.open.aliyun.market.service.AliyunLoginService;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.webhook.common.util.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("aliyunLoginService")
public class AliyunLoginServiceImpl implements AliyunLoginService {
    @Autowired
    private RedisDataSource redisDataSource;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;

    @Override
    public Result<Code2UserInfoData> code2UserInfo(LoginAuthModel code) {
        return Result.newSuccess();
    }

    /**
     *
     * @param corpId 阿里云实例 ID
     * @param appId 固定值：aliyunCrm
     * @param userId 固定值：1000
     * @param fsEa 阿里云免登录场景，不使用
     * @return
     */
    @Override
    public Result<String> genFsTicket(String corpId, String appId, String userId,String fsEa) {
        String ticketFormat = "corpId=%s&appId=%s&userId=%s&timestamp=%s&fsEa=%s";
        long timestamp = System.currentTimeMillis();
        String ticket = String.format(ticketFormat, corpId, appId, userId, timestamp,fsEa);
        UserTicketModel ticketModel = new UserTicketModel(corpId, appId, userId, timestamp,fsEa);
        String ticketMd5 = MD5Util.getMD5(ticket);
        String key = GlobalValue.USER_TICKET_KEY_PREFIX + ticketMd5;
        redisDataSource.getRedisClient().set(key, JSONObject.toJSONString(ticketModel));
        redisDataSource.getRedisClient().expire(key, GlobalValue.USER_TICKET_EXPIRE_TIME);  //10分钟有效

        return Result.newSuccess(ticketMd5);
    }

    @Override
    public Result<FsUserModel> getFsUser(String ticket) {
        String key = GlobalValue.USER_TICKET_KEY_PREFIX + ticket;
        String value = redisDataSource.getRedisClient().get(key);
        if(StringUtils.isEmpty(value)) {
            return Result.newError(ResultCodeEnum.TICKET_NOT_EXISTS);
        }
        UserTicketModel ticketModel = JSONObject.parseObject(value,UserTicketModel.class);
        long offset = System.currentTimeMillis() - ticketModel.getTimestamp();
        if(offset > GlobalValue.USER_TICKET_EXPIRE_TIME * 1000L) {
            LogUtils.info("AliyunLoginServiceImpl.getFsUser,ticket expired");
            return Result.newError(ResultCodeEnum.TICKET_EXPIRED);
        }
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList2(ChannelEnum.aliyun,ticketModel.getCorpId());
        LogUtils.info("AliyunLoginServiceImpl.getFsUser,enterpriseBindList={}",enterpriseBindList);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            return Result.newError(ResultCodeEnum.OUT_EA_NOT_BIND);
        }
        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindList.get(0);
        return Result.newSuccess(new FsUserModel(enterpriseBindEntity.getFsEa(),
                "1000",
                com.facishare.open.aliyun.market.config.GlobalValue.ALIYUN_CRM_APP_ID)
        );
    }
}
