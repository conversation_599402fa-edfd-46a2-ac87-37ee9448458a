package com.facishare.open.aliyun.market.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.facishare.open.aliyun.market.entity.EnterpriseBindEntity;
import com.facishare.open.aliyun.market.mapper.EnterpriseBindMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * tb_enterprise_bind表管理器类
 * <AUTHOR>
 * @date 20220722
 */
@Component
public class EnterpriseBindManager {
    @Resource
    private EnterpriseBindMapper enterpriseBindMapper;

    public int insert(EnterpriseBindEntity entity) {
        int count = enterpriseBindMapper.insert(entity);
        LogUtils.info("EnterpriseBindManager.insert,count={}",count);
        return count;
    }

    public int insert(ChannelEnum channel,
                      String fsEa,
                      String outEa,
                      BindTypeEnum bindType,
                      BindStatusEnum bindStatus) {
        EnterpriseBindEntity entity = EnterpriseBindEntity.builder()
                .channel(channel)
                .fsEa(fsEa)
                .outEa(outEa)
                .bindType(bindType)
                .bindStatus(bindStatus)
                .build();
        return insert(entity);
    }

    public int updateById(EnterpriseBindEntity entity) {
        int count = enterpriseBindMapper.updateById(entity);
        LogUtils.info("EnterpriseBindManager.updateById,count={}",count);
        return count;
    }

    public int updateBindStatus(String fsEa,
                                BindStatusEnum bindStatus) {
        EnterpriseBindEntity entity = EnterpriseBindEntity.builder()
                .bindStatus(bindStatus)
                .build();

        LambdaUpdateWrapper<EnterpriseBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getFsEa,fsEa);
        int count = enterpriseBindMapper.update(entity, wrapper);
        LogUtils.info("EnterpriseBindManager.updateBindStatus,count={}",count);
        return count;
    }

    public List<EnterpriseBindEntity> getEnterpriseBindList(ChannelEnum channel,String outEa) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getChannel,channel);
        wrapper.eq(EnterpriseBindEntity::getOutEa,outEa);

        return enterpriseBindMapper.selectList(wrapper);
    }

    public List<EnterpriseBindEntity> getEnterpriseBindList2(ChannelEnum channel,String outEa) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getChannel,channel);
        wrapper.like(EnterpriseBindEntity::getOutEa,outEa);

        return enterpriseBindMapper.selectList(wrapper);
    }

    public EnterpriseBindEntity getEntity(String fsEa) {
        return getEntity(ChannelEnum.feishu,fsEa,null);
    }

    public EnterpriseBindEntity getEntity(String fsEa,BindStatusEnum bindStatus) {
        return getEntity(ChannelEnum.feishu,fsEa,bindStatus);
    }

    public EnterpriseBindEntity getEntity(ChannelEnum channel,String fsEa,BindStatusEnum bindStatus) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getChannel,channel);
        wrapper.eq(EnterpriseBindEntity::getFsEa,fsEa);
        if(bindStatus!=null) {
            wrapper.eq(EnterpriseBindEntity::getBindStatus,bindStatus);
        }

        return enterpriseBindMapper.selectOne(wrapper);
    }

    public List<EnterpriseBindEntity> getEnterpriseBindListByUid(String uid) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(EnterpriseBindEntity::getOutEa, uid);
        wrapper.orderByDesc(EnterpriseBindEntity::getCreateTime);

        return enterpriseBindMapper.selectList(wrapper);
    }
}
