<configuration scan="false" debug="false">
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 限制第三方jar  日志输出  -->
    <logger name="druid.sql" level="DEBUG"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="com.alibaba" level="WARN"/>
    <logger name="com.github" level="WARN"/>

    <root level="debug">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
