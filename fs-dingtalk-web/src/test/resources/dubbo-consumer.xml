<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:application name="${dubbo.application.name}"/>
    <dubbo:registry address="${dubbo.registry.address}" file="${dubbo.registry.file}"/>
    <dubbo:consumer check="false" timeout="10000" filter="tracerpc"/>

    <dubbo:reference id="objectWriterService" interface="com.facishare.open.ding.api.service.CrmObjectWriterService" protocol="dubbo" timeout="3000"/>

</beans>