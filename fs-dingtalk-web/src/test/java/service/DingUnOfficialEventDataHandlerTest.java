//package service;
//
//import com.alibaba.fastjson.JSONObject;
//import com.facishare.open.ding.cloud.paas.models.EventData;
//import com.facishare.open.ding.cloud.service.impl.connector.DingUnOfficialEventDataHandler;
//
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//
//import java.util.Arrays;
//import java.util.List;
//
//public class DingUnOfficialEventDataHandlerTest extends BaseAbstractTest {
//
//    @Qualifier("dingUnOfficialEventDataHandler")
//    @Autowired
//    private DingUnOfficialEventDataHandler dingUnOfficialEventDataHandler;
//
//    @Test
//    public void testHandle(){
//
//        String jsonData="{\n" + "    \"sourceData\": {\n" + "        \"biz_reg_name\": false,\n"
//          + "        \"leads_pool_id\": \"60a3afea65530f0001484527\",\n" + "        \"source\": \"2\",\n"
//          + "        \"extend_obj_data_id\": \"62cd4222e3bd75000165e761\",\n" + "        \"tel\": \"15425424541\",\n"
//          + "        \"job_title\": \"ces22071202\",\n" + "        \"biz_status\": \"un_assigned\",\n"
//          + "        \"package\": \"CRM\",\n" + "        \"lock_status\": \"0\",\n"
//          + "        \"create_time\": 1.657618978066E12,\n" + "        \"promotion_channel\": \"qywx\",\n"
//          + "        \"created_by\": [\n" + "            \"2153\"\n" + "        ],\n" + "        \"version\": \"4\",\n"
//          + "        \"data_own_department\": [\n" + "            \"1001\"\n" + "        ],\n"
//          + "        \"last_follow_time\": 1.657618977974E12,\n" + "        \"name\": \"ces22071202\",\n"
//          + "        \"leads_stage\": \"Lead\",\n" + "        \"leads_status\": \"1\",\n"
//          + "        \"_id\": \"62cd4221e3bd75000165e730\",\n" + "        \"is_duplicated\": false,\n"
//          + "        \"tenant_id\": \"82379\",\n" + "        \"remark\": \"sdsdgadgadfdafgsdfgvsdfsss\",\n"
//          + "        \"is_deleted\": false,\n" + "        \"object_describe_api_name\": \"LeadsObj\",\n"
//          + "        \"company\": \"ces22071202\",\n" + "        \"last_follower\": [\n" + "            \"2153\"\n"
//          + "        ],\n" + "        \"department\": \"ces22071202\",\n"
//          + "        \"leads_stage_changed_time\": 1.657618977974E12,\n" + "        \"is_overtime\": false,\n"
//          + "        \"picture_path\": [],\n" + "        \"last_modified_time\": 1.657620226198E12,\n"
//          + "        \"address\": \"15425424541\",\n" + "        \"life_status\": \"normal\",\n"
//          + "        \"mobile\": \"15425424541\",\n" + "        \"is_collected\": false,\n"
//          + "        \"last_modified_by\": [\n" + "            \"2153\"\n" + "        ],\n"
//          + "        \"record_type\": \"default__c\",\n" + "        \"field_Iohiv__c\": \"asdssdsd\",\n"
//          + "        \"refresh_duplicated_version\": \"0\"\n" + "    },\n" + "    \"sourceEventType\": 2,\n"
//          + "    \"sourceTenantType\": 1\n" + "}";
//
//        EventData eventData = JSONObject.parseObject(jsonData, EventData.class);
//
//        List<EventData> eventData1 = Arrays.asList(eventData);
//
//        dingUnOfficialEventDataHandler.handle(eventData1);
//
//    }
//
//
//}
