package service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;
import okio.BufferedSource;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.UnsupportedCharsetException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.util.IOUtils.UTF8;

@Slf4j
public class ErpDssTool {

    private static List<String>alltenantIds=new ArrayList<>();
    private static List<Map>alltenantIdsMap=new ArrayList<>();
    private static String cookie="_ga=GA1.2.2041469424.1616727100; sensorsdata2015jssdkcross={\"distinct_id\":\"1786c71cb94d8-09e2375589abc3-52193e12-2073600-1786c71cb9576b\",\"props\":{\"$latest_referrer\":\"\",\"$latest_referrer_host\":\"\"},\"$device_id\":\"1787d35774719-007c05b472b86f-52193e12-2073600-1787d35774840e\"}; hy_data_2020_id=1787d3577f42be-081f7d1a00e7ec-52193e12-2073600-1787d3577f5751; hy_data_2020_js_sdk={\"distinct_id\":\"1787d3577f42be-081f7d1a00e7ec-52193e12-2073600-1787d3577f5751\",\"site_id\":478,\"user_company\":409,\"props\":{},\"device_id\":\"1787d3577f42be-081f7d1a00e7ec-52193e12-2073600-1787d3577f5751\"}; Hm_lvt_06d5233541e92feb3cc8980700b1efa6=1628162160; guid=c6e1b4aa-e0fb-c241-759b-218073491c65; sso_token=37d12c6c-1833-46d3-940c-495154f1e8c0; lang=zh-CN; mirrorId=0000; originRefer=; LoginId=LOGIN_ID_126cb0f0-aced-44b5-aa22-cb57063a1af4; fs_token=OpKrPc8sDZCjCZDZCYqqD3baBM9XP3OjE3WqCcOuP6GrE64m; FSAuthX=0G60X3Pdy840001mh4sdzQD754rg2PGORqF56L7qfkwbpc43TApyUwNIzlzjuPLNVixloqRBvkgor9CgRasvzP4XefCmmMIS9tmh7hKfAVhwhDtS9oTx9WvA2p3DCYma0DzVItYuowwVlMpVzbgcyW8ozQR7TDf5DzKu7WWQTEuPivz1FhbIdp3evySQqQ4etnYP2apXrvCQd2IdXmMCQ16kGYvJaAEaZm8MYafdcsTwtnIfYWa8t6xOoA06; FSAuthXC=0G60X3Pdy840001mh4sdzQD754rg2PGORqF56L7qfkwbpc43TApyUwNIzlzjuPLNVixloqRBvkgor9CgRasvzP4XefCmmMIS9tmh7hKfAVhwhDtS9oTx9WvA2p3DCYma0DzVItYuowwVlMpVzbgcyW8ozQR7TDf5DzKu7WWQTEuPivz1FhbIdp3evySQqQ4etnYP2apXrvCQd2IdXmMCQ16kGYvJaAEaZm8MYafdcsTwtnIfYWa8t6xOoA06; enterprise=dtddlj11078; JSESSIONID=A51B12C128C5D7433D0B3F1B45CFBB94; EPXId=0b04e419e7d642c4bc034c67a62874cb";

    public static void main(String[] args) throws Exception {
//        StopPloy.run();
//        ClearDingTalkConnect.run();
        clearOnlyCrmChannel.run();
    }

    private static void writeToFile(String content, File file) throws IOException {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(file))) {
            writer.write(content);
        } catch (Exception e) {
            throw e;
        }
    }

    public static  Map<String,Object>  queryTennat(Integer offset, Integer limit) throws IOException {
        String sql="select * from erp_connect_info t where channel='STANDARD_CHANNEL' and "
          + "  tenant_id!='0' and connect_params like '%https://ale.fxiaoke.com/dingtalk/cloud%' order by id desc offset "+offset+" limit "+limit;
        String url="https://ale.fxiaoke.com/erp/syncdata/superadmin/superQuery";
        return post(url,cookie,sql);
    }

    public static  Map<String,Object>  queryOnlyChannel(Integer offset, Integer limit) throws IOException {
        String sql="select * from (select  tenant_id,count(tenant_id)n from erp_connect_info t  where tenant_id!='0'  group by tenant_id )t where n=1 offset "+offset+" limit "+limit;
        String url="https://ale.fxiaoke.com/erp/syncdata/superadmin/superQuery";
        return post(url,cookie,sql);
    }





    public static Map<String, Object> post(String url, String cookies, String content) throws IOException {

        OkHttpClient client = new OkHttpClient().newBuilder()
                                                .build();
        MediaType mediaType = MediaType.parse("text/plain");
        RequestBody body = RequestBody.create(mediaType, content);
        Request request = new Request.Builder()
          .url(url)
          .method("POST", body)
          .addHeader("Cookie", cookies)
          .addHeader("tenantId", "62000900")
          .addHeader("Content-Type", "text/plain")
          .build();
        Response response = client.newCall(request).execute();
        String res = getContentFromRbody(response.body());
        if (StringUtils.isEmpty(res)){
            return Collections.emptyMap();
        }
        Map map = JSONObject.parseObject(res, Map.class);
        return map;
    }
    private  static String getContentFromRbody(ResponseBody responseBody) throws IOException {
        BufferedSource source = responseBody.source();
        source.request(Long.MAX_VALUE);
        Buffer buffer = source.buffer();
        Charset charset = UTF8;
        MediaType contentType = responseBody.contentType();
        if (contentType != null) {
            try {
                charset = contentType.charset(UTF8);
            } catch (UnsupportedCharsetException e) {
                e.printStackTrace();
            }
        }
        return buffer.clone().readString(charset);
    }

    /*停用钉钉策略*/
    static class StopPloy{

        public static void run(){
            try {
                excutorStopPloyDetail();
            } catch (Exception e) {
                log.error("处理错误",e);
            }
            log.info("结束:{},{}",alltenantIds.size(),alltenantIds);
        }

        private static void excutorStopPloyDetail() throws Exception {
            int size=500;
            int limit=500;
            int offset =0;
            while (size==limit){
                size = stopPloy(offset, limit);
                offset+=limit;
                Thread.sleep(3000);
            }
        }

        private  static int stopPloy(Integer offset , Integer limit) throws Exception {
            //1.查询企业
            Map<String, Object> objectMap = queryTennat(offset, limit);
            log.info("data:{}",objectMap);

            //2.停用策略快照
            Object data = objectMap.get("data");
            if (data!=null){
                List<Map<String,Object>> dataList= (List<Map<String, Object>>) data;
                StringBuffer tenantIds=new StringBuffer();
                for (Map<String, Object> dataMap : dataList) {
                    Object tenant_id = dataMap.get("tenant_id");
                    alltenantIds.add(String.valueOf(tenant_id));
                    if (tenantIds.length()==0){
                        tenantIds.append("'").append(tenant_id).append("'");
                    }else {
                        tenantIds.append(",").append("'").append(tenant_id).append("'");
                    }
                }
                stopPloyDetail(tenantIds.toString());

                return dataList.size();
            }
            return 0;
        }

        private static  Map<String,Object>  stopPloyDetail(String tenantId) throws IOException {
            String sql="update sync_ploy_detail_snapshot set status='2' where source_tenant_id in ("+tenantId+")  and( source_object_api_name ='LeadsObj' or (dest_object_api_name in ('AccountObj','ContactObj'))) and status=1";
            log.info(sql);
            String url="https://ale.fxiaoke.com/erp/syncdata/superadmin/superInsert";
            return post(url,cookie,sql);
        }

    }

    static class ClearDingTalkConnect{

        public static void run(){
            File file = new File("C:\\Users\\<USER>\\Desktop\\connets.json");
            if (!file.exists()){
                log.warn("文件不存在");
                return;
            }
            try {
                excutorClearDingTalkConnect();
            } catch (Exception e) {
                log.error("处理错误",e);
            }
            log.info("结束:{},{}",alltenantIds.size(),alltenantIds);
            try {
                writeToFile(JSONObject.toJSONString(alltenantIdsMap),file);
                log.info("写入成功");
            } catch (IOException e) {
                log.error("写入错误：",e);
            }
            log.info("delete:{}",JSONObject.toJSONString(alltenantIdsMap));
        }

        private static void excutorClearDingTalkConnect() throws Exception {
            int size=500;
            int limit=500;
            while (size==limit){
                size = clearDingTalkConnect(0, limit);
                Thread.sleep(3000);
            }
        }


        private  static int clearDingTalkConnect(Integer offset , Integer limit) throws Exception {
            //1.查询企业
            Map<String, Object> objectMap = queryTennat(offset, limit);
            log.info("data:{}",objectMap);

            //2.停用策略快照
            Object data = objectMap.get("data");
            if (data!=null){
                List<Map<String,Object>> dataList= (List<Map<String, Object>>) data;
                StringBuffer tenantIds=new StringBuffer();
                alltenantIdsMap.addAll(dataList);
                for (Map<String, Object> dataMap : dataList) {
                    Object id = dataMap.get("id");
                    alltenantIds.add(String.valueOf(id));
                    if (tenantIds.length()==0){
                        tenantIds.append("'").append(id).append("'");
                    }else {
                        tenantIds.append(",").append("'").append(id).append("'");
                    }
                }
                clearConnect(tenantIds.toString());
                return dataList.size();
            }
            return 0;
        }

        private static Map<String, Object> clearConnect(String ids) throws IOException {

            String sql="delete from erp_connect_info where id in ("+ids+") ";
            log.info(sql);
            String url="https://ale.fxiaoke.com/erp/syncdata/superadmin/superInsert";
            return post(url,cookie,sql);

        }

    }

    static class clearOnlyCrmChannel{

        public static void run() throws Exception {
            File file = new File("C:\\Users\\<USER>\\Desktop\\clearOnlyCrmChannel.json");
            if (!file.exists()){
                log.warn("文件不存在");
                return;
            }
            try {
                excutorClearOnlyCrmConnect();
            } catch (Exception e) {
                log.error("处理错误",e);
            }
            log.info("结束:{},{}",alltenantIds.size(),alltenantIds);
            try {
                writeToFile(JSONObject.toJSONString(alltenantIdsMap),file);
                log.info("写入成功");
            } catch (IOException e) {
                log.error("写入错误：",e);
            }
            log.info("delete:{}",JSONObject.toJSONString(alltenantIdsMap));
        }


        private static void excutorClearOnlyCrmConnect() throws Exception {
            int size=500;
            int limit=500;
            int offset=0;
            while (size==limit){
                size = clearOnlyCrmTennat(0, limit);
                Thread.sleep(3000);
                offset+=limit;
            }
        }

        private static String queryOnlyCrmTennatIds(int offset, int limit) throws IOException {
            //查询只有一个渠道的企业
            Map<String, Object> objectMap = queryOnlyChannel(offset, limit);
            log.info("data:{}",objectMap);

            //获取企业id
            Object data = objectMap.get("data");
            StringBuffer tenantIds=new StringBuffer();
            if (data!=null){
                List<Map<String,Object>> dataList= (List<Map<String, Object>>) data;

                for (Map<String, Object> dataMap : dataList) {
                    Object tenant_id = dataMap.get("tenant_id");
                    if (tenantIds.length()==0){
                        tenantIds.append("'").append(tenant_id).append("'");
                    }else {
                        tenantIds.append(",").append("'").append(tenant_id).append("'");
                    }
                }
                if (StringUtils.isEmpty(tenantIds)){
                    return null;
                }
            }
            if (tenantIds.length()==0){
                return null;
            }
            return tenantIds.toString();
        }

        public static List<Map<String,Object>>  queryConnectByTenantId(String tenantIds) throws IOException {
            String sql="select * from erp_connect_info t where tenant_id in ("+tenantIds+") limit 2000";
            String url="https://ale.fxiaoke.com/erp/syncdata/superadmin/superQuery";
            Map<String, Object> connObjectMap = post(url, cookie, sql);
            log.info("connObjectMap:{}",connObjectMap);

            Object data = connObjectMap.get("data");

            if (data!=null){
                List<Map<String,Object>> dataList= (List<Map<String, Object>>) data;

                Map<String, List<Map<String, Object>>> tenantMap =
                  dataList.stream().collect(Collectors.groupingBy(t -> t.get("tenant_id").toString()));

                List<Map<String,Object>> clearDataList= new ArrayList<>();

                for (Map.Entry<String, List<Map<String, Object>>> entry : tenantMap.entrySet()) {

                    if (entry.getValue().size()>1){
                        log.warn("当前企业正常,不清除:{}",JSONObject.toJSONString(entry.getValue()));
                        continue;
                    }

                    Object channel = entry.getValue().get(0).get("channel");
                    if (!"CRM".equals(channel)){
                        continue;
                    }
                    clearDataList.addAll(entry.getValue());
                }
                return clearDataList;
            }
            return null;
        }

        private static int clearOnlyCrmTennat(int offset, int limit) throws IOException {
            String tenantIds = queryOnlyCrmTennatIds(offset, limit);
            if (StringUtils.isEmpty(tenantIds)){
                return 0;
            }

            List<Map<String, Object>> clearDataList = queryConnectByTenantId(tenantIds);

            if (clearDataList!=null){

                StringBuffer ids=new StringBuffer();
                for (Map<String, Object> dataMap : clearDataList) {
                    Object id = dataMap.get("id");
                    alltenantIds.add(id.toString());
                    if (ids.length()==0){
                        ids.append("'").append(id).append("'");
                    }else {
                        ids.append(",").append("'").append(id).append("'");
                    }
                }
                alltenantIdsMap.addAll(clearDataList);
                if (StringUtils.isEmpty(ids)){
                    return 0;
                }

                //清除连接
                clearConnect(ids.toString());
                return clearDataList.size();
            }

            return 0;
        }


        private static Map<String, Object> clearConnect(String ids) throws IOException {

            String sql="delete from erp_connect_info where id in ("+ids+") ";
            log.info(sql);
            String url="https://ale.fxiaoke.com/erp/syncdata/superadmin/superInsert";
            return post(url,cookie,sql);

        }

    }

};
