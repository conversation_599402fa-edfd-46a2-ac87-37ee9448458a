package interfaces;

import base.BaseAbstractTest;
import com.facishare.open.ding.api.result.CrmResponseResult;
import com.facishare.open.ding.provider.task.NCrmManager;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * @<NAME_EMAIL>
 * @ClassName: SfaManagerTest
 * @Description:
 * @datetime 2019/2/25 21:04
 * @Version 1.0
 */
public class SfaManagerTest extends BaseAbstractTest {

    @Autowired
    private NCrmManager nCrmManager;

    @Test
    public void invalidStock() {
        List<String> stockIds = Lists.newArrayList();
        stockIds.add("5c6a1eeea5083d0a11abd2e5");
        Map<String, Object> dataMap = new HashMap<>();
//        dataMap.put("objectDataIdList", stockIds);


        dataMap.put("object_data_id", "5c6a1eeea5083d0a11abd2e5");
        dataMap.put("dataObjectApiName", "StockObj");
        //作废纷享ERP库存/纷享库存
        CrmResponseResult<Void> invalidResult = nCrmManager.invalidV2Object(71646, "StockObj", 1005, dataMap);

    }

    @Test
    public void deleteStock() {
        String[] stockIds = new String[1];
        stockIds[0] = "5c6a1eeea5083d0a11abd2e5";
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("idList", stockIds);

//        dataMap.put("data", "5c6a1eeea5083d0a11abd2e5");
        dataMap.put("describe_api_name", "StockObj");
        //作废纷享ERP库存/纷享库存
        CrmResponseResult<Void> invalidResult = nCrmManager.deleteV2Object(71646, "StockObj", 1005, dataMap);

    }
}
