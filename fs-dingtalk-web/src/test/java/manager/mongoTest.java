package manager;

import base.BaseAbstractTest;
import com.fxiaoke.common.Guard;
import com.fxiaoke.common.PasswordUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.ast.Test;

/**
 * <AUTHOR>
 * @Date 2020/10/27 10:18
 * @Version 1.0
 */
@Slf4j
public class mongoTest extends BaseAbstractTest {

        static String key = "FS4e2%Y#X@~g.+F<";
        static Guard encrypt = new Guard(key);
        public void testfunc() {
            try {
                //解密mongo的密文密码
                System.out.println(PasswordUtil.decode("0505712E5836A4D8CF66F379C7DC0F3DE7E1968728A58A18C98435D5A87A0191"));
            }catch (Exception e) {

            }

        }
        public static void main( String args[] ) {
            mongoTest t = new mongoTest();
            t.testfunc();
        }


}
