package com.facishare.open.ding.provider.dao;

import com.facishare.open.ding.provider.arg.DingObjSyncEntity;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/17 14:12
 * @Version 1.0
 */
public interface DingObjSyncDao extends ICrudMapper<DingObjSyncEntity> {
    @Insert("<script>" +
            "insert ignore into ding_obj_sync" +
            "(crm_api_name,ding_api_name,crm_obj_id,ding_obj_id,is_sync,ding_corp_id,tenant_id) values " +
            "(#{dingObjSync.crmApiName},#{dingObjSync.dingApiName},#{dingObjSync.crmObjId},#{dingObjSync.dingObjId},#{dingObjSync.isSync},#{dingObjSync.dingCorpId},#{dingObjSync.tenantId})" +
            "</script>")
    Integer insertObjSync(@Param("dingObjSync") DingObjSyncEntity dingObjSyncEntity);

    @Select("<script>" +
            "select count(*) from ding_obj_sync where ding_corp_id=#{corpId} and ding_obj_id=#{dingObjId} and ding_api_name=#{dingApiName}" +
            "</script>")
    Integer getObjById(@Param("dingObjId") String dingObjId,@Param("corpId") String corpId,@Param("dingApiName") String dingApiName);

    @Select("<script>" +
            "select * from ding_obj_sync where tenant_id=#{enterpriseId} " +
            "<if test='maxId != null'> and id > #{maxId} </if> " +
            "order by id limit #{pageSize}" +
            "</script>")
    List<DingObjSyncEntity> pageById(@Param("enterpriseId") Integer enterpriseId, @Param("maxId") Long maxId, @Param("pageSize") Integer pageSize);
}
