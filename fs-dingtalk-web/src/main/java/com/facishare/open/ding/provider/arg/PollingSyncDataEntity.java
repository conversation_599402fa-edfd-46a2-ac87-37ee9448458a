package com.facishare.open.ding.provider.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @Date 2021/5/11 17:06
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "polling_sync_time")
public class PollingSyncDataEntity implements Serializable {
    private Long id;
    private Long lastSyncTime;
    private Integer eventLevel;
    private String suiteTicket;
    private Date updateTime;

}
