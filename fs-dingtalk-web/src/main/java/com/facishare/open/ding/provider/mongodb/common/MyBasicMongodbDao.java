package com.facishare.open.ding.provider.mongodb.common;

import com.facishare.open.common.storage.mongodb.BasicMongodbDaoImpl;
import com.facishare.open.common.storage.mongodb.MongoDBTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/11 11:52
 * @<NAME_EMAIL>
 * @version 1.0
 */
public abstract class MyBasicMongodbDao<T> extends BasicMongodbDaoImpl<T> {

	/**
	 * 日志打印器
	 */
	protected final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	protected MongoDBTemplate mongoDBTemplate;
	
	/* 方便多数据源
	 * @see com.facishare.open.common.storage.mongodb.BasicMongodbDaoImpl#getMongoDBTemplate()
	 */
	public MongoDBTemplate getMongoDBTemplate(){
		return mongoDBTemplate ;
	}

}