package com.facishare.open.ding.web.template.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class Code2UserInfoData implements Serializable {
    private String access_token;
    private String token_type;
    private int expires_in;
    private String name;
    private String en_name;
    private String avatar_url;
    private String avatar_thumb;
    private String avatar_middle;
    private String avatar_big;
    private String open_id;
    private String union_id;
    private String email;
    private String enterprise_email;
    private String user_id;
    private String mobile;
    private String tenant_key;
    private int refresh_expires_in;
    private String refresh_token;
}