//package com.facishare.open.ding.cloud.service.impl.connector;
//
//import com.alibaba.fastjson.JSONObject;
//import com.facishare.open.ding.api.model.connector.ConnectorPushDataModel;
//import com.facishare.open.ding.api.service.DingCorpMappingService;
//import com.facishare.open.ding.api.service.cloud.connector.ConnectorHistoryDataService;
//import com.facishare.open.ding.api.vo.DingCorpMappingVo;
//import com.facishare.open.ding.api.constants.DataModelId;
//import com.facishare.open.ding.cloud.entity.HighBizDataDo;
//import com.facishare.open.ding.cloud.service.api.DingEventService;
//import com.facishare.open.ding.common.result.Result;
//import com.facishare.restful.common.StopWatch;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import static com.facishare.open.ding.cloud.constants.Constant.APP_ID;
//
///**
// * 连接器连接事件处理类
// * <AUTHOR>
// * @Date 2021/7/28
// */
//@Service("connectorEvent70ServiceImpl")
//@Slf4j
//public class ConnectorEvent70ServiceImpl implements DingEventService {
//    @Autowired
//    private ConnectorHistoryDataService connectorHistoryDataService;
//
//    @Autowired
//    private DingCorpMappingService dingCorpMappingService;
//
//    @Override
//    public void executeEvent(HighBizDataDo eventData) {
//        log.info("connectorEvent70ServiceImpl.executeEvent,eventData={}",eventData);
//        StopWatch stopWatch = StopWatch.create("connector_event_70_begin");
//        ConnectorPushDataModel dataModel = JSONObject.parseObject(eventData.getBizData(),ConnectorPushDataModel.class);
//        log.info("connectorEvent70ServiceImpl.executeEvent,dataModel={}",dataModel);
//
//        Result<DingCorpMappingVo> dingCorpMappingVoResult = dingCorpMappingService.queryMappingByAppId(eventData.getCorpId(),APP_ID);
//        if(!dingCorpMappingVoResult.isSuccess() || dingCorpMappingVoResult.getData()==null) {
//            log.info("connectorEvent70ServiceImpl.executeEvent,dingCorpMappingVoResult={}",dingCorpMappingVoResult);
//            stopWatch.lap("connector_event_70_end");
//            stopWatch.log();
//            return;
//        }
//
//        DingCorpMappingVo dingCorpMappingVo = dingCorpMappingVoResult.getData();
//        dingCorpMappingVo.setConnector(1);
//        dingCorpMappingService.update(dingCorpMappingVo);
//
//        switch (dataModel.getMainEventModelId()) {
//            case DataModelId.CUSTOMER_DATA_MODEL_ID:
//            {
//                new Thread(()->{
//                    connectorHistoryDataService.processHistoryCustomerData(dingCorpMappingVo);
//                }).start();
//            }
//            break;
//            case DataModelId.SALES_ORDER_DATA_MODEL_ID:
//            {
//                //历史订单数据不能在初始化的时候同步到连接器，因为订单要关联产品，关联客户，
//                // 而产品又是从ERP同步过来的，客户数据也需要同步到ERP，这样发送方和接收方才能正常处理，
//                // 否则，直接同步订单，肯定会全量出错
////                new Thread(()->{
////                    connectorHistoryDataService.processHistorySalesOrderData(dingCorpMappingVo);
////                }).start();
//            }
//            break;
//            default:
//            {
//                log.info("connectorEvent70ServiceImpl.executeEvent,不支持的连接器事件模型");
//                stopWatch.lap("connector_event_70_end");
//                stopWatch.log();
//                return;
//            }
//        }
//        stopWatch.lap("connector_event_70_end");
//        stopWatch.log();
//    }
//}
