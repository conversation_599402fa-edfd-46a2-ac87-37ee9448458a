package com.facishare.open.ding.web.template.inner.jsapi;

import com.facishare.open.ding.api.result.AppAuthResult;
import com.facishare.open.ding.api.result.SignResult;
import com.facishare.open.ding.api.service.AppAuthService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.web.constants.ConfigCenter;
import com.facishare.open.ding.web.template.model.JsapiModel;
import com.facishare.open.ding.web.utils.JSApiUtils;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.inner.jsapi.JsApiTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class DingtalkJsApiTemplate extends JsApiTemplate {
    @Autowired
    private AppAuthService appAuthService;

    @Override
    public void getJsApiSignature(MethodContext context) {
        log.info("DingtalkJsApiTemplate.getJsApiSignature,context={}",context);

        JsapiModel jsapiModel = context.getData();

        //获取签名参数
        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(jsapiModel.getOutEa(), ConfigCenter.APP_CRM_ID, null);
        Long agentId = appAuthResult.getData().get(0).getAgentId();
        String signValue = "";
        try {
            signValue = JSApiUtils.sign(jsapiModel.getTicket(), ConfigCenter.NONCE_STR, jsapiModel.getTimeStamp(), ConfigCenter.h5Url);
        } catch (Exception e) {
            log.info("DingtalkJsApiTemplate.getJsApiSignature,exception={}",e.getMessage());
            e.printStackTrace();
        }
        SignResult signResult = new SignResult(signValue, agentId);

        log.info("DingtalkJsApiTemplate.getJsApiSignature,signResult={}",signResult);
        context.setResult(TemplateResult.newSuccess(signResult));
    }
}
