package com.facishare.open.ding.api.service.cloud;

import com.facishare.open.ding.common.result.Result;

/**
 * <AUTHOR>
 * @Date 2022/2/9 20:14 钉钉的单独对象数据
 * @Version 1.0
 */
public interface DingObjectService {

     Result<String> queryCustomerData(String dingCorpId,String suiteId,String dingDataId);

     Result<String> queryConcatData(String dingCorpId,String suiteId,String dingDataId);

     Result<String> syncCustomerDataInTime(Integer tenantId,String suiteId,String dingDataId);

     Result<String> syncConcatDataInTime(Integer tenantId,String suiteId,String dingDataId);
}
