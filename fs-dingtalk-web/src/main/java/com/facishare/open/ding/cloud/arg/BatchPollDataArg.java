package com.facishare.open.ding.cloud.arg;

import lombok.Data;

import java.io.Serializable;

@Data
public class BatchPollDataArg implements Serializable {
    private String dataModelId; //要拉取的主数据模型id   必填
    private String datetimeFilterField; //用于过滤时间范围的字段，包含数据创建时间(dataGmtCreate)和数据修改时间(dataGmtModified)，如不传则不过滤
    private Long minDatetime; //当配置了datetimeFilterField字段后，数据的时间起点，如果不传则将最早一条数据作为起点
    private Long maxDatetime; //当配置了datetimeFilterField字段后，数据的时间终点，如果不传则按最新一条数据作为终点
    private String nextToken;//用于翻页的游标，如果为空则从第一条数据开始查询
    private Long maxResults;//单次获取的最大记录条数，最大限制100条
    private String appId;//同步数据的应用ID，第三方企业应用传应用的appId，企业自建应用传agentId
}
