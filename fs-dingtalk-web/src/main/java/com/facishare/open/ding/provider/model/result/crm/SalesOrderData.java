package com.facishare.open.ding.provider.model.result.crm;

import java.util.Map;
import lombok.Data;

/**
 * Created by system on 2018/6/5.
 */
@Data
public class SalesOrderData {

    private Map<String, Object> data;

    /** ID **//*
    @SerializedName(value = "_id")
    private String id;

    *//** 销售订单编号 **//*
    private String name;

    *//** 客户ID **//*
    @SerializedName(value = "account_id")
    private String accountId;

    *//** 负责人ID **//*
    @SerializedName(value = "owner")
    private List<Integer> ownerIds;

    *//** 销售订单金额 **//*
    @SerializedName(value = "order_amount")
    private String orderAmount;

    *//** 下单日期 **//*
    @SerializedName(value = "order_time")
    private Long orderTime;

    *//** 交货日期 **//*
    @SerializedName(value = "delivery_date")
    private Long deliveryDate;

    *//** 收货人ID, 关联 联系人ID **//*
    @SerializedName(value = "ship_to_id")
    private String receiverId;

    *//** 收货人电话 **//*
    @SerializedName(value = "ship_to_tel")
    private String recieverPhone;

    *//** 收货地址 **//*
    @SerializedName(value = "ship_to_add")
    private String receivingAddress;

    *//** 订单关联的产品列表 **//*
    @SerializedName(value = "SalesOrderProductObj")
    private List<SalesOrderProduct> products;

    @Data
    public static class SalesOrderProduct {

        *//** 产品ID **//*
        @SerializedName(value = "product_id")
        private String productId;

        *//** 价格(元) **//*
        @SerializedName(value = "product_price")
        private String productPrice;

        *//** 折扣 **//*
        private String discount;

        *//** 销售单价(元) **//*
        @SerializedName(value = "sales_price")
        private String salesPrice;

        *//** 数量 **//*
        private String quantity;

        *//** 金额小计 **//*
        @SerializedName(value = "subtotal")
        private String subTotal;

    }*/

}
