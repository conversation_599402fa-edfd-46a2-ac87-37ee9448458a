package com.facishare.open.ding.api.result;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by system on 2018/4/9.
 */
@Data
public class ConfigurationGetResult implements Serializable {

    /** 配置id, 当未开启时 为null **/
    private Integer id;

    /** 是否开启数据对接: 0、未开启  1、开启 **/
    private Integer status;

    /** 数据同步类型，参考SyncTypeEnum **/
    private Integer syncType;

    /** 定时同步频率类型，参考 RateTypeEnum **/
    private Integer rateType;

    /** 定时同步的起始时间，默认00:00 **/
    private String startTime;

    /** 定时同步的截至时间，默认23:59 **/
    private String endTime;

}
