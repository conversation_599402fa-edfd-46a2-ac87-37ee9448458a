package com.facishare.open.ding.provider.service;

import com.facishare.open.ding.api.enums.CrmApiNameEnum;
import com.facishare.open.ding.api.enums.OperationStatusNameEnum;
import com.facishare.open.ding.api.enums.OperationTypeNameEnum;
import com.facishare.open.ding.api.enums.SyncTypeEnum;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.result.KcSyncLogResult;
import com.facishare.open.ding.api.service.SyncLogService;
import com.facishare.open.ding.api.vo.SyncSettingVo;
import com.facishare.open.ding.common.result.PageObject;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.manager.DingSyncLogManager;
import com.facishare.open.ding.provider.model.arg.ValueToApiName;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>日志查询</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-08-28 16:56
 */
@Slf4j
@Service("syncLogService")
public class SyncLogServiceImpl implements SyncLogService{

    @Autowired
    private DingSyncLogManager dingSyncLogManager;

    @Autowired
    private EmployeeProviderService employeeProviderService;

    @Override
    public Result<Map<String, Object>> querySyncLog(SyncSettingVo vo) {
        if (Objects.isNull(vo)) {
            log.warn("querySyncLog param is null, vo=[{}].", vo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        String apiName= ValueToApiName.getApiNameByValue(vo.getApiNameValue());

        PageObject<KcSyncLogResult> resultPageObject = dingSyncLogManager.queryDingSyncLogPage(vo.getEi(), vo.getOperationType(), vo.getOperationStatus(),
                vo.getPageSize(), vo.getPageNumber(),apiName,vo.getContentKey(),vo.getStartTime(),vo.getEndTime());
        List<KcSyncLogResult> list = new ArrayList<>();
        if (Objects.nonNull(resultPageObject.getResult())) {
            list = resultPageObject.getResult();
            for (KcSyncLogResult log : list) {
                if (log.getSyncType() == SyncTypeEnum.MANUAL_SYNC.getType()) {
                    //根据updateBy查询操作人名称
                    DingMappingEmployeeResult employeeResult = getFsEmployee(vo.getEi(), log.getUpdateBy());
                    if (Objects.nonNull(employeeResult)) {
                        log.setOperation(employeeResult.getEmployeeName());
                    }
                } else {
                    log.setOperation("系统");
                }
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                log.setOperationTime(sdf.format(log.getUpdateTime()));
                log.setObjName(Objects.nonNull(log.getApiName()) ? CrmApiNameEnum.getExceptionByName(log.getApiName()) : "");

                log.setOperationName(Objects.nonNull(log.getOperationType()) ? OperationTypeNameEnum.getOperationByType(log.getOperationType()) : "");
                log.setStatusName(Objects.nonNull(log.getOperationStatus()) ? OperationStatusNameEnum.getDescByStatus(log.getOperationStatus()) : "");
            }
        }
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("totalNum", resultPageObject.getTotalCount());
        dataMap.put("logList", list);
        return Result.newSuccess(dataMap);
    }

    public DingMappingEmployeeResult getFsEmployee(Integer ei, Integer employeeId) {
        if (Objects.isNull(ei) && Objects.isNull(employeeId)) {
            log.warn("querySyncLog param is null, ei=[{}],employeeId.", ei, employeeId);
            return null;
        }
        GetEmployeeDtoArg arg = new GetEmployeeDtoArg();
        arg.setEnterpriseId(ei);
        arg.setEmployeeId(employeeId);
        GetEmployeeDtoResult result = employeeProviderService.getEmployeeDto(arg);
        DingMappingEmployeeResult employeeResult = new DingMappingEmployeeResult();
        if (Objects.nonNull(result) && Objects.nonNull(result.getEmployeeDto())) {
            EmployeeDto employeeDto = result.getEmployeeDto();
            employeeResult.setEmployeeName(employeeDto.getName());
        }
        return employeeResult;
    }
}
