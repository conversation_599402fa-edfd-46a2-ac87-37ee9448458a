package com.facishare.open.ding.api.service.cloud;

import com.facishare.open.ding.api.arg.DingSendMessageArg;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.common.result.Result;

/**
 * <AUTHOR>
 * @Date 2021/9/22 14:33
 * @Version 1.0
 */
public interface DingSupportSendMessageService {
    Result<String> sendMessage(DingSendMessageArg dingSendMessageArg);

    Result<DingCorpMappingVo> queryEnterpriseByEi(Integer ei);
}
