package com.facishare.open.ding.cloud.template.outer.msg.todo;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.open.ding.api.result.AppAuthResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.AppAuthService;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.arg.SendCardMessageArg;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 钉钉发送createTodo消息模板
 *
 * <AUTHOR>
 * @date 20240927
 */
@Slf4j
@Component
// IgnoreI18nFile
public class DingTalkSendCreateTodoMsgHandlerTemplate extends DingTalkSendTodoMsgHandlerTemplate {
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private DingCorpMappingService corpMappingService;
    @Autowired
    private AppAuthService appAuthService;
    @Autowired
    private DingManager dingManager;

    @Override
    protected void filterMsg(MethodContext context) {
        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void buildMsg(MethodContext context) {
        log.info("DingTalkSendCreateTodoMsgHandlerTemplate.buildMsg,context={}", context);
        CreateTodoArg createTodoArg = context.getData();

        log.info("DingTalkSendCreateTodoMsgHandlerTemplate.buildMsg,createTodoArg={}", createTodoArg);
        CreateTodoResult result = new CreateTodoResult();
        result.setCode(200);
        result.setMessage("发送成功");
        Result<List<DingCorpMappingVo>> mappingResult = corpMappingService.queryByEi(createTodoArg.getEi());
        if (!mappingResult.isSuccess() || CollectionUtils.isEmpty(mappingResult.getData())) {
            // log.info("crm no bind, ea:{}.",createTodoArg.getEa());
            // return result;
            log.info("DingTalkSendCreateTodoMsgHandlerTemplate.buildMsg,queryByEi,crm no bind, ea:{}",
                    createTodoArg.getEa());
            context.setResult(TemplateResult.newInstance(result.getCode(), result.getMessage(), result));
            return;
        }
        // 钉钉待办
        if (!isCrmBizType(createTodoArg.getBizType())) {
            // return result;
            log.info("DingTalkSendCreateTodoMsgHandlerTemplate.buildMsg,isCrmBizType()=false,bizType={}",
                    createTodoArg.getBizType());
            context.setResult(TemplateResult.newInstance(result.getCode(), result.getMessage(), result));
            return;
        }
        // 灰度的企业发待办
        if (ConfigCenter.OA_GRAY_TENANTS.contains(createTodoArg.getEa())) {
            if (ConfigCenter.WORK_ORDER_BIZ_TYPES.contains(createTodoArg.getBizType())) {
                if (!ConfigCenter.OA_WORK_ORDER_TENANTS.contains(createTodoArg.getEa())) {
                    // return result;
                    context.setResult(TemplateResult.newInstance(result.getCode(), result.getMessage(), result));
                    return;
                }
            }
            // return dingManager.createTodo(createTodoArg);
            CreateTodoResult createTodoResult = dingManager.createTodo(createTodoArg);
            context.setResult(TemplateResult.newInstance(createTodoResult.getCode(), createTodoResult.getMessage(),
                    createTodoResult));
            return;
        }
        // StopWatch stopWatch = StopWatch.create("trace createTodo:" +
        // createTodoArg.getEa());//处理markdown语法
        // 发工作通知
        StringBuilder markdown = new StringBuilder();
        if (CollectionUtils.isNotEmpty(createTodoArg.getForm())) {
            for (int i = 0; i < createTodoArg.getForm().size(); i++) {
                if (i == 0) {
                    markdown.append("<font color=\"#181C25\" style=\"line-height:22px;font-size:16px;\">"
                            + createTodoArg.getForm().get(i).getKey() + ":" + createTodoArg.getForm().get(i).getValue()
                            + "</font>\n" + " <br>\n");
                } else {
                    markdown.append("<font color=\"#A2A3A5\" style=\"line-height:20px;font-size:14px;\">"
                            + createTodoArg.getForm().get(i).getKey()
                            + "：</font><font color=\"#181C25\" style=\"line-height:20px;font-size:14px;\">"
                            + createTodoArg.getForm().get(i).getValue() + "</font>" + " <br>\n");
                }
            }
        }
        String objectApiName = createTodoArg.getExtraDataMap().get("objectApiName");
        String objectId = createTodoArg.getExtraDataMap().get("objectId");
        String instanceId = createTodoArg.getExtraDataMap().get("workflowInstanceId");
        Integer taskId = createTodoArg.getGenerateUrlType();
        List<Integer> userIds = createTodoArg.getReceiverIds();

        Result<List<DingMappingEmployeeResult>> dataResult = objectMappingService.batchQueryMapping(createTodoArg.getEi(), userIds, String.valueOf(ConfigCenter.APP_CRM_ID));
        if(CollectionUtils.isEmpty(dataResult.getData())){
//            log.info("crm no user bind ea:{},userIds:{}",createTodoArg.getEa(),createTodoArg.getReceiverIds());
//            return result;
            log.info("DingTalkSendCreateTodoMsgHandlerTemplate.buildMsg,batchQueryMapping,crm no user bind ea:{},userIds:{}",
                    createTodoArg.getEa(),createTodoArg.getReceiverIds());
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }
        List<String> dingEmpIds = dataResult.getData().stream().map(DingMappingEmployeeResult::getDingEmployeeId)
                .collect(Collectors.toList());
        String dingCorpId = mappingResult.getData().get(0).getDingCorpId();
        // 获取对应的agentid
        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingCorpId, ConfigCenter.APP_CRM_ID,
                null);
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("apiname", objectApiName);
        argMap.put("objectId", objectId);
        argMap.put("instanceId", ObjectUtils.isEmpty(instanceId) ? "100" : instanceId);
        argMap.put("taskId", taskId);
        argMap.put("markdown", markdown);
        argMap.put("ei", createTodoArg.getEi());
        argMap.put("bizType", createTodoArg.getBizType());
        String empIds = Joiner.on(",").join(dingEmpIds);
        argMap.put("userIdList", empIds);
        // 需要判断是否购买了纷享钉钉CRM。其他应用的不发送
        if (ObjectUtils.isEmpty(appAuthResult.getData())) {
            // log.info("enterprise not support send todo :{}",dingCorpId);
            // return result;
            log.info("DingTalkSendCreateTodoMsgHandlerTemplate.buildMsg,enterprise not support send todo :{}",
                    dingCorpId);
            context.setResult(TemplateResult.newInstance(result.getCode(), result.getMessage(), result));
            return;
        }
        Long agentId = appAuthResult.getData().get(0).getAgentId();

        // Result<String> messageResult = dingManager.sendCardMessage(agentId,
        // dingCorpId, argMap,
        // ConfigCenter.MESSAGE_CARD_ID,true,ConfigCenter.CRM_SUITE_ID);
        // log.info("create to do arg :{}，result:{}",argMap,messageResult);

        SendCardMessageArg arg = new SendCardMessageArg();
        arg.setAgentId(agentId);
        arg.setDingCorpId(dingCorpId);
        arg.setDingMessageArg(argMap);
        arg.setTemplateId(ConfigCenter.MESSAGE_CARD_ID);
        arg.setIsCardMessage(true);
        arg.setSuiteId(ConfigCenter.CRM_SUITE_ID);
        // dingManager.sendCardMessage(arg);

        context.setResult(TemplateResult.newSuccess());
        context.setData(arg);
    }

    @Override
    public void sendMsg(MethodContext context) {
        log.info("DingTalkSendCreateTodoMsgHandlerTemplate.sendMsg,context={}", context);
        SendCardMessageArg arg = context.getData();
        Result<String> sendResult = dingManager.sendCardMessage(arg);
        log.info("DingTalkSendCreateTodoMsgHandlerTemplate.sendMsg,sendResult={}", sendResult);

        CreateTodoResult result = new CreateTodoResult();
        result.setCode(200);
        result.setMessage("发送成功");

        context.setResult(TemplateResult.newSuccess(result));
    }
}
