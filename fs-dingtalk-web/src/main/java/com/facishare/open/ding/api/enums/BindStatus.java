package com.facishare.open.ding.api.enums;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/13 15:09
 * <AUTHOR> yin<PERSON>@fxiaoke.com
 * @version 1.0 
 */
public enum BindStatus {

    /** 已经授权 **/
    ORDER_BIND(0),

    /** 取消授权 **/
    MANUAL_BIND(1),

    ;

    private int type;

    BindStatus(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static boolean isInvalid(Integer type) {
        if (type != null) {
            for (BindStatus syncTypeEnum : BindStatus.values()) {
                if (syncTypeEnum.getType() == type) {
                    return false;
                }
            }
        }
        return true;
    }

}
