package com.facishare.open.ding.cloud.template.outer.event.order;

import com.facishare.open.ding.api.model.OrderModel;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.order.OrderPaidEventHandlerTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class DingTalkOrderPaidEventHandlerTemplate extends OrderPaidEventHandlerTemplate {
    @Resource
    private DingTalkOpenEnterpriseHandlerTemplate dingTalkOpenEnterpriseHandlerTemplate;
    @Resource
    private DingTalkSaveOrderHandlerTemplate dingTalkSaveOrderHandlerTemplate;
    //只能Autowired装载，不能用Resource
    @Resource
    private DingCorpMappingService dingCorpMappingService;


    @Override
    public void openEnterpriseOrSaveOrder(MethodContext context) {
        log.info("DingTalkOrderPaidEventHandlerTemplate.openEnterpriseOrSaveOrder,context={}",context);
        OrderModel orderModel = context.getData();
        Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryCorpMappingByCorpId(orderModel.getCorpId(), null);
        log.info("DingTalkOrderPaidEventHandlerTemplate.openEnterpriseOrSaveOrder,corpResult={}",corpResult);
        if(CollectionUtils.isEmpty(corpResult.getData())) {
            //如果没有绑定关系，触发开通企业的逻辑
            context.setResult(TemplateResult.newSuccess(OPEN_ENTERPRISE));
        } else {
            //如果有企业绑定关系，触发保存订单的逻辑
            context.setResult(TemplateResult.newSuccess(SAVE_ORDER));
        }
        log.info("DingTalkOrderPaidEventHandlerTemplate.openEnterpriseOrSaveOrder,context.2={}",context);
    }

    @Override
    public void openEnterprise(MethodContext context) {
        log.info("DingTalkOrderPaidEventHandlerTemplate.openEnterprise,context={}",context);
        TemplateResult result = dingTalkOpenEnterpriseHandlerTemplate.execute(context.getData());
        context.setResult(result);
        log.info("DingTalkOrderPaidEventHandlerTemplate.openEnterprise,result={}",result);
    }

    @Override
    public void saveOrder(MethodContext context) {
        log.info("DingTalkOrderPaidEventHandlerTemplate.saveOrder,context={}",context);
        TemplateResult result = dingTalkSaveOrderHandlerTemplate.execute(context.getData());
        context.setResult(result);
        log.info("DingTalkOrderPaidEventHandlerTemplate.saveOrder,result={}",result);
    }
}
