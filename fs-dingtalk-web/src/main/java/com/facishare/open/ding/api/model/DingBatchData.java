package com.facishare.open.ding.api.model;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/24 19:38
 * @Version 1.0
 */
@Data
@Slf4j
public class DingBatchData implements Serializable {
    private String creator_userid;
    private String data;
    private String extend_data;
    private String gmt_create;
    private String object_type;
    private String proc_inst_status;
    private String proc_out_result;
    private Permission permission;
    private String instance_id;
    private String gmt_modified;
    @Data
    public static class Permission implements Serializable {
        public List<String> ownerStaffIds;
        public List<String> participantStaffIds;
    }

    public  Long convertCreateTime(){
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date parse = df.parse(this.getGmt_create());
            return parse.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

}
