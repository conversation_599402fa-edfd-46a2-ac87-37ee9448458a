package com.facishare.open.ding.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/11/1 17:51 附属产品赠送的数量
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NeighborProductQuantity implements Serializable {

    private String crmProduct;//产品ID
    private Integer schemaAccount;//应用本身自己的账号 如：订货通下游的端口 营销通的虚拟账号。不是属于crm账号的，可以通过该变量控制订单的quantity
    private Boolean hasRealAccountSync;//是否同步真实的订单数据
    private Integer crmAccount;//crm版本的赠送对应的人数
    private Boolean hasCrmProduct;//是否是crm产品
    private Integer chargeChannel;//计费模式 1 全额  0 固定  2 计算模式
    private Long  payFee;//默认金额
    private String CRMDingItemCode;//crm版本所对应的钉钉的itemcode（非必填,适用于非CRM应用）
}
