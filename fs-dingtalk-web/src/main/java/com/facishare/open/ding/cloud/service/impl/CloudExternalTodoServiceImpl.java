package com.facishare.open.ding.cloud.service.impl;

import com.facishare.open.ding.cloud.template.outer.msg.todo.DingTalkSendCreateTodoMsgHandlerTemplate;
import com.facishare.open.ding.cloud.template.outer.msg.todo.DingTalkSendDealTodoMsgHandlerTemplate;
import com.facishare.open.ding.cloud.template.outer.msg.todo.DingTalkSendDeleteTodoMsgHandlerTemplate;
import com.facishare.open.ding.cloud.template.outer.msg.todo.DingTalkSendUpdateTodoMsgHandlerTemplate;
import com.fxiaoke.message.extrnal.platform.api.ExternalTodoService;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.UpdateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DealTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DeleteTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.UpdateTodoResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2021/5/10 19:16
 * @Version 1.0
 */
@Service("cloudExternalTodoServiceImpl")
@Slf4j
public class CloudExternalTodoServiceImpl implements ExternalTodoService {

//    @Autowired
//    private DingManager dingManager;
//    @Autowired
//    private AppAuthService appAuthService;
//    @Autowired
//    private DingCorpMappingService corpMappingService;
//    @Autowired
//    private ObjectMappingService objectMappingService;
    @Resource
    private DingTalkSendCreateTodoMsgHandlerTemplate dingTalkSendCreateTodoMsgHandlerTemplate;
    @Resource
    private DingTalkSendUpdateTodoMsgHandlerTemplate dingTalkSendUpdateTodoMsgHandlerTemplate;
    @Resource
    private DingTalkSendDealTodoMsgHandlerTemplate dingTalkSendDealTodoMsgHandlerTemplate;
    @Resource
    private DingTalkSendDeleteTodoMsgHandlerTemplate dingTalkSendDeleteTodoMsgHandlerTemplate;


    @Override
    public synchronized CreateTodoResult createTodo(CreateTodoArg createTodoArg) {

        CreateTodoResult result = (CreateTodoResult) dingTalkSendCreateTodoMsgHandlerTemplate.execute(createTodoArg).getData();
        return result;
    }



    @Override
    public UpdateTodoResult updateTodo(UpdateTodoArg updateTodoArg) {

        UpdateTodoResult result = (UpdateTodoResult)dingTalkSendUpdateTodoMsgHandlerTemplate.execute(updateTodoArg).getData();
        return result;
    }

    @Override
    public DealTodoResult dealTodo(DealTodoArg dealTodoArg) {

        DealTodoResult result = (DealTodoResult) dingTalkSendDealTodoMsgHandlerTemplate.execute(dealTodoArg).getData();
        return result;
    }


    @Override
    public synchronized DeleteTodoResult deleteTodo(DeleteTodoArg deleteTodoArg) {


        DeleteTodoResult result = (DeleteTodoResult) dingTalkSendDeleteTodoMsgHandlerTemplate.execute(deleteTodoArg).getData();
        return result;
    }

}
