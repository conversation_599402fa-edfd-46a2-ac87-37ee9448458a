package com.facishare.open.ding.web.ajax;

/**
 * <p>用于ajax返回</p>
 * @dateTime 2017/9/12 16:23
 * <AUTHOR> <EMAIL>
 * @version 1.0
 */
public class AjaxResult {
    private Integer errorCode = AjaxCode.OK;
    private String errMsg = "OK";
    private Object value;

    public AjaxResult(Integer errCode, String errMsg) {
        this.errorCode = errCode;
        this.errMsg = errMsg;
    }

    public AjaxResult(Object value) {
        this(AjaxCode.OK, "OK");
        this.value = value;
    }

    public Object getData() {
        return value;
    }

    public void setData(Object value) {
        this.value = value;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    @Override
    public String toString() {
        return "AjaxResult [errorCode=" + errorCode + ", errMsg=" + errMsg + ", value=" + value + "]";
    }
}
