package com.facishare.open.ding.cloud.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.open.ding.api.arg.DingSendMessageArg;
import com.facishare.open.ding.api.model.AppParams;
import com.facishare.open.ding.api.result.AppAuthResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.AppAuthService;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.cloud.DingSupportSendMessageService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/22 15:16
 * @Version 1.0
 */
@Service("dingSupportSendMessageServiceImpl")
@Slf4j
public class DingSupportSendMessageServiceImpl implements DingSupportSendMessageService {

    @Autowired
    private DingCorpMappingService corpMappingService;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private AppAuthService appAuthService;
    @Autowired
    private ObjectMappingService objectMappingService;

    @Override
    public Result<String> sendMessage(DingSendMessageArg dingSendMessageArg) {
        //TODO 判断是否绑定
        Result<List<DingCorpMappingVo>> enterpriseResult = corpMappingService.queryByEi(dingSendMessageArg.getTenantId());
        if(ObjectUtils.isEmpty(enterpriseResult.getData())){
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        DingCorpMappingVo mappingVo=enterpriseResult.getData().get(0);
        String dingCorpId=mappingVo.getDingCorpId();
        final String suiteId = dingSendMessageArg.getSuiteId();
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(suiteId);
        Result<List<DingMappingEmployeeResult>> dataResult = objectMappingService.batchQueryMapping(mappingVo.getEi(), dingSendMessageArg.getReceiverUserIds(), appParams.getAppId());
        if (CollectionUtils.isEmpty(dataResult.getData())) {
            log.info("crm no user bind ea:{},userIds:{}", mappingVo.getEa(), dingSendMessageArg);
            return Result.newError(ResultCode.NOT_BIND_EMP);
        }
        List<String> dingEmpIds = dataResult.getData().stream().map(DingMappingEmployeeResult::getDingEmployeeId).collect(Collectors.toList());
        String empIds = Joiner.on(",").join(dingEmpIds);
        dingSendMessageArg.getDataMap().put("userIdList", empIds);
        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingCorpId, Long.parseLong(appParams.getAppId()), null);
        if(ObjectUtils.isEmpty(appAuthResult)){
            return Result.newError(ResultCode.ENTERPRISE_NOT_SUPPORT_APP);
        }
        Long agentId = appAuthResult.getData().get(0).getAgentId();
        Result<String> supportResult = dingManager.supportSendMessage(agentId, dingCorpId, dingSendMessageArg.getDataMap(), dingSendMessageArg.getTemplateId(), suiteId);
        return supportResult;
    }

    @Override
    public Result<DingCorpMappingVo> queryEnterpriseByEi(Integer ei) {
        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryByEi(ei);
        if(ObjectUtils.isEmpty(corpResult.getData())){
            return  Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        return Result.newSuccess(corpResult.getData().get(0));
    }


}
