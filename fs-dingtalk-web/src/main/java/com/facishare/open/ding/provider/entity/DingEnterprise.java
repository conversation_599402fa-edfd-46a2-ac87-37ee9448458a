package com.facishare.open.ding.provider.entity;

import java.util.Date;
import javax.persistence.Id;
import lombok.Data;

/**
 * <p>企业绑定</p>
 *
 * @version 1.0
 * @dateTime 2018-07-10 16:20
 * @anthor liqb
 */
@Data
public class DingEnterprise {

    @Id
    private Long id;

    /** 纷享ea  **/
    private String ea;

    /** 纷享EI **/
    private Integer ei;

    /** 纷享企业名称 **/
    private String enterpriseName;

    /** 钉钉企业标识 **/
    private String dingCorpId;

    private String agentId;

    /** 应用ID **/
    private String appKey;

    /** 应用secret **/
    private String appSecret;

    /** 免等待应用ID **/
    private String redirectAppId;

    /** 免等待应用secret **/
    private String redirectAppSecret;

    private String clientIp;

    private String token;

    /** 创建时间 **/
    private Date createTime;

    /** 更新时间 **/
    private Date updateTime;

    /** 创建人 **/
    private Integer createBy;

    /** 修改人 **/
    private Integer updateBy;

    /**是否使用回调**/
    private Integer isCallback;

    /**
     * 集成开发模式1/2
     */
    private Integer devModel;

    /** crm提醒开启的状态 **/
    private Integer alertStatus;

    /**
     * 全局index
     */
    private Integer allIndex;
}
