package com.facishare.open.ding.transfer.handler;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.provider.dao.DingCorpMappingDao;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.transfer.handler.SourceKeyRateLimitPageTransferHandler;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.fscore.RunStatus;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/11 17:46:32
 */
public abstract class DingTalkIsvHandler<RV, CV> extends SourceKeyRateLimitPageTransferHandler<RV, CV> {

    @Autowired
    private DingCorpMappingDao dingCorpMappingDao;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    protected static LoadingCache<Integer, List<OuterOaEnterpriseBindEntity>> appAuthEntityCache;

    @Override
    public void afterPropertiesSet() {
        super.afterPropertiesSet();
        if (Objects.isNull(appAuthEntityCache)) {
            appAuthEntityCache = Caffeine.newBuilder()
                    .maximumSize(100)
                    .expireAfterWrite(30, TimeUnit.MINUTES)
                    .build(k -> {
                        final String fsEa = eieaConverter.enterpriseIdToAccount(k);
                        return outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.dingding, fsEa);
                    });
        }
    }

    @Override
    public List<String> getAllTenantIds() {
        final List<Integer> allTenantIds = dingCorpMappingDao.getAllTenantIds();
        return getNormalEis(allTenantIds);
    }

    @NotNull
    private List<String> getNormalEis(List<Integer> allTenantIds) {
        return batchGetSimpleEnterpriseData(allTenantIds).stream()
                .filter(data -> Objects.equals(data.getRunStatus(), RunStatus.RUN_STATUS_NORMAL))
                .map(SimpleEnterpriseData::getEnterpriseId)
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getTenantIdsByTime(Long startTime) {
        final List<Integer> tenantIdsByTime = dingCorpMappingDao.getTenantIdsByTime(new Timestamp(startTime));
        return getNormalEis(tenantIdsByTime);
    }

    public List<SimpleEnterpriseData> batchGetSimpleEnterpriseData(List<Integer> tenantIds) {
        final BatchGetSimpleEnterpriseDataArg arg = new BatchGetSimpleEnterpriseDataArg();
        arg.setEnterpriseIds(tenantIds);
        return enterpriseEditionService.batchGetSimpleEnterpriseData(arg).getSimpleEnterpriseList();
    }

    @Override
    public void cleanCache() {
        appAuthEntityCache.invalidateAll();
    }
}
