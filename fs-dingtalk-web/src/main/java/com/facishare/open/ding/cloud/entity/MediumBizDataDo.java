package com.facishare.open.ding.cloud.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/4/28 14:49
 * @Version 1.0
 */
@Data
@Alias("mediumBizDataDo")
@TableName("open_sync_biz_data_medium")
public class MediumBizDataDo implements Serializable {
    private Long id;
    private Date gmtCreate;
    private Date gmtModified;
    //订阅方ID
    private String subscribeId;
    //企业ID
    private String corpId;
    //业务ID
    private String bizId;
    //业务类型
    private Integer bizType;
    //业务数据
    private String bizData;
    //对账游标
    private Long openCursor;
   //处理状态
    private Integer status;

    public  void  convertGmtCrete(Long timeStamp){
        Date dateTime=new Date(timeStamp);
        this.setGmtCreate(dateTime);
    }

    public  void  convertGmtModify(Long timeStamp){
        Date dateTime=new Date(timeStamp);
        this.setGmtModified(dateTime);
    }

}
