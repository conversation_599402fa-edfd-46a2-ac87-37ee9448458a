//package com.facishare.open.ding.cloud.aop;
//import com.facishare.open.ding.api.exception.SyncDataException;
//import com.facishare.open.ding.common.utils.ErasePasswordUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.ObjectUtils;
//import org.apache.commons.lang3.time.StopWatch;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.springframework.stereotype.Component;
//
//import java.util.Arrays;
//
//@Aspect
//@Slf4j
//@Component
//public class LogAop {
//
//    @Around("execution(* com.facishare.open.ding.cloud.service.*.*(..))"
//            + "|| execution(* com.facishare.open.ding.cloud.service.*.*.*(..))"
//            + "|| execution(* com.facishare.open.ding.cloud.manager.*.*(..))"
//            + "|| execution(* com.facishare.open.ding.cloud.service.*.*.*.*(..))")
//    public Object interceptor(ProceedingJoinPoint point) throws Throwable {
//        Object result = null;
//        // 获取类的完整路径和方法名称
//        String fullClassName = point.getTarget().getClass().getName();
//        String methodName = point.getSignature().getName();
//        //计时器
//        StopWatch stopWatch = new StopWatch();
//        String args = ErasePasswordUtils.erase(ObjectUtils.isNotEmpty(point.getArgs()) ? Arrays.toString(point.getArgs()) : null);
//        stopWatch.start();
//        //处理结果直接返回
//        try {
//            result = point.proceed();
//            stopWatch.stop();
//            String result2 = ErasePasswordUtils.erase(ObjectUtils.isNotEmpty(result) ? String.valueOf(result) : null);
//            log.info(" AOP trace fullClassName:{} ,totalTime:{}ms,methodName:{} arg{}, result:{}",  fullClassName, stopWatch.getTime(), methodName,args,result2);
//        }catch (Exception e){
//
//            stopWatch.stop();
//            if (e instanceof SyncDataException){
//                log.warn(" classname:{},totalTime:{}ms, methedName:{}, args:{}, result:{},exception:{}",
//                  fullClassName, stopWatch.getTime(),methodName, args,result,e.getMessage());
//            }else {
//                log.error(" classname:{},totalTime:{}ms, methedName:{}, args:{}, result:{},exception:{}",
//                  fullClassName, stopWatch.getTime(),methodName, args,result,e.getMessage());
//            }
//            throw e;
//        }
//        return result;
//    }
//}
