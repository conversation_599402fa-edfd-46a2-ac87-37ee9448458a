package com.facishare.open.ding.api.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 钉钉任务VO
 */
@Data
public class DingTaskVo implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业ID
     */
    private Integer ei;

    /**
     * 数据中心ID
     */
    private String dataCenterId;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 钉钉员工ID
     */
    private String dingEmployeeId;

    /**
     * 员工ID
     */
    private Integer employeeId;

    /**
     * 源ID
     */
    private String sourceId;

    /**
     * 员工源ID
     */
    private String employeeSourceId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 创建者员工ID
     */
    private Integer creatorEmployeeId;

    /**
     * 状态 1:pending 0:approved
     */
    private Integer status;
}
