<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
			http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

    <!--Add Mongo-spring-support support ,需要在配置中心中添加 此 MongoDB连接的配置属性 -->
    <bean id="mongoSupport" class="com.github.mongo.support.MongoDataStoreFactoryBean" p:configName="fs-open-dingtalk-all" />
    <bean id="mongoDBTemplate" class="com.facishare.open.common.storage.mongodb.MongoDBTemplateImpl">
        <property name="datastore" ref="mongoSupport"/>
    </bean>

</beans>
