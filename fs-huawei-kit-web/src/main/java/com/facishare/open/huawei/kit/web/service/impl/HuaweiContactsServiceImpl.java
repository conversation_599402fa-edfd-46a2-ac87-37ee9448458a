package com.facishare.open.huawei.kit.web.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.huawei.kit.web.config.ConfigCenter;
import com.facishare.open.huawei.kit.web.result.result.ResultCodeEnum;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDepartmentBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaDepartmentBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.huawei.kit.web.info.AllOrgSyncInfo;
import com.facishare.open.huawei.kit.web.info.AuthSyncInfo;
import com.facishare.open.huawei.kit.web.info.SingleOrgSyncInfo;
import com.facishare.open.huawei.kit.web.result.result.Result;
import com.facishare.open.huawei.kit.web.service.HuaweiContactsService;
import com.facishare.open.order.contacts.proxy.api.arg.FsDeptArg;
import com.facishare.open.order.contacts.proxy.api.arg.FsEmpArg;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.enums.FsEmployeeRoleCodeEnum;
import com.facishare.open.order.contacts.proxy.api.service.FsContactsServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("huaweiContactsService")
public class HuaweiContactsServiceImpl implements HuaweiContactsService {
//    @Resource
//    private EnterpriseBindManager enterpriseBindManager;
//    @Resource
//    private EmployeeBindManager employeeBindManager;
    @Resource
    private EIEAConverter eieaConverter;
//    @Resource
//    private DepartmentBindManager departmentBindManager;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Resource
    private FsDepartmentServiceProxy fsDepartmentServiceProxy;
    @Resource
    private FsContactsServiceProxy fsContactsServiceProxy;
    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Resource
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;

    @Override
    public Result<Void> syncEmployee(AuthSyncInfo authSyncInfo) {
        //根据类型处理
        if(authSyncInfo.getFlag() == 1 || authSyncInfo.getFlag() == 2) {
            createOrUpdateEmployee(authSyncInfo);
        } else if(authSyncInfo.getFlag() == 0 || authSyncInfo.getFlag() == 3) {
            stopEmployee(authSyncInfo);
        }
        return Result.newSuccess();
    }

    private Result<Void> createOrUpdateEmployee(AuthSyncInfo authSyncInfo) {
        String outEa = authSyncInfo.getTenantId();
        //查询企业绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.huawei, outEa, ConfigCenter.HUAWEI_APP_ID);
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        String fsEa = enterpriseBindEntities.get(0).getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        String dcId = enterpriseBindEntities.get(0).getId();

        for(AuthSyncInfo.User user : authSyncInfo.getUserList()) {
            String outUserId = user.getUserName();
            //查询人员绑定关系
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntities(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.huawei).outEa(outEa).fsEa(fsEa).outEmpId(outUserId).appId(ConfigCenter.HUAWEI_APP_ID).build());
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                //新增
                //员工不存在，新增员工并更新员工绑定表
                String sex = "M";

                List<String> fsDepIdList = getFsDepIdList(fsEa, outEa, Lists.newArrayList(user.getOrgCode()));
                List<String> mainDepIdList = CollectionUtils.isEmpty(fsDepIdList) ?
                        Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"") : fsDepIdList;
                String phone = user.getMobile();
                if(StringUtils.isNotEmpty(phone) && StringUtils.startsWith(phone, "+86-")) {
                    phone = phone.substring(4);
                }

                FsEmpArg arg = FsEmpArg.builder()
                        .ei(ei + "")
                        .name(user.getName())
                        .fullName(user.getName())
                        .sex(sex)
                        .phone(phone)
                        .mainDepartment(mainDepIdList)
                        .status("0")
                        .isActive(true)
                        .build();
                com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
                        Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                        FsEmployeeRoleCodeEnum.SALES.getRoleCode());
                if (result.isSuccess()) {
                    OuterOaEmployeeBindEntity employeeBindEntity = new OuterOaEmployeeBindEntity();
                    employeeBindEntity.setChannel(ChannelEnum.huawei);
                    employeeBindEntity.setFsEa(fsEa);
                    employeeBindEntity.setFsEmpId(result.getData().getId());
                    employeeBindEntity.setOutEa(outEa);
                    employeeBindEntity.setDcId(dcId);
                    employeeBindEntity.setOutEmpId(outUserId);
                    employeeBindEntity.setAppId(ConfigCenter.HUAWEI_APP_ID);
                    employeeBindEntity.setBindStatus(BindStatusEnum.normal);
                    employeeBindEntity.setCreateTime(System.currentTimeMillis());
                    employeeBindEntity.setUpdateTime(System.currentTimeMillis());
                    Integer insertEmp = outerOaEmployeeBindManager.insert(employeeBindEntity);
                    LogUtils.info("HuaweiContactsServiceImpl.addUser,insert employee mapping,count={}", insertEmp);
                    return Result.newSuccess();
                } else {
                    //如果手机同号则自动匹配。
                if (result.getCode() == 46) {
                    //通过手机号搜索员工
                    com.facishare.open.order.contacts.proxy.api.result.Result<EmployeeDto> mobileResult = fsEmployeeServiceProxy.getEmployeesDtoByEnterpriseAndMobile(ei, user.getMobile());
                    if (mobileResult.isSuccess()) {
                        //修改员工
                        FsEmpArg fsEmpArg = FsEmpArg.builder()
                        .ei(ei + "")
                        .id(mobileResult.getData().getDataId())
                        .status("0")
                        .build();
                        com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateResult = fsEmployeeServiceProxy.update(fsEmpArg, null, null);
                        LogUtils.info("HuaweiContactsServiceImpl.addUser,updateResult={}", updateResult);
                        if(updateResult.isSuccess()) {
                            OuterOaEmployeeBindEntity employeeBindEntity = new OuterOaEmployeeBindEntity();
                            employeeBindEntity.setChannel(ChannelEnum.huawei);
                            employeeBindEntity.setFsEa(fsEa);
                            employeeBindEntity.setFsEmpId(mobileResult.getData().getDataId());
                            employeeBindEntity.setOutEa(outEa);
                            employeeBindEntity.setDcId(dcId);
                            employeeBindEntity.setOutEmpId(outUserId);
                            employeeBindEntity.setAppId(ConfigCenter.HUAWEI_APP_ID);
                            employeeBindEntity.setBindStatus(BindStatusEnum.normal);
                            employeeBindEntity.setCreateTime(System.currentTimeMillis());
                            employeeBindEntity.setUpdateTime(System.currentTimeMillis());
                            Integer insertEmp = outerOaEmployeeBindManager.insert(employeeBindEntity);
                            LogUtils.info("HuaweiContactsServiceImpl.addUser,insert employee mapping2,count={}", insertEmp);
                           return Result.newSuccess();
                        }
                    }
                }
                    return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
                }
            } else {
                //更新
                OuterOaEmployeeBindEntity employeeBindEntity = employeeBindEntities.get(0);
                com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.toggle(ei + "",
                        employeeBindEntity.getFsEmpId(),
                        true,
                        Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                        FsEmployeeRoleCodeEnum.SALES.getRoleCode());
                LogUtils.info("HuaweiContactsServiceImpl.addUserList,result={}", result);
                employeeBindEntity.setBindStatus(BindStatusEnum.normal);
                employeeBindEntity.setUpdateTime(System.currentTimeMillis());
                Integer count = outerOaEmployeeBindManager.updateById(employeeBindEntity);
                LogUtils.info("HuaweiContactsServiceImpl.addUserList,update employee bind,count={}", count);
            }
        }
        return Result.newSuccess();
    }

    private List<String> getFsDepIdList(String fsEa, String outEa, List<String> outDepIdList) {
        List<String> fsDepList = new ArrayList<>();
        for (String depId : outDepIdList) {
            //飞书根部门对应纷享根部门
            if (StringUtils.equalsIgnoreCase(depId, "0")) {
                fsDepList.add(GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "");
                continue;
            }
            List<OuterOaDepartmentBindEntity> departmentBindManagerEntities = outerOaDepartmentBindManager.getEntities(OuterOaDepartmentBindParams.builder().channel(ChannelEnum.huawei).fsEa(fsEa).outEa(outEa).appId(ConfigCenter.HUAWEI_APP_ID).outDepId(depId).build());

            if (CollectionUtils.isEmpty(departmentBindManagerEntities)) {
                continue;
            }
            fsDepList.add(departmentBindManagerEntities.get(0).getFsDepId());
        }
        LogUtils.info("HuaweiContactsServiceImpl.getFsDepIdList,fsDepList={}", fsDepList);

        return fsDepList;
    }

    private Result<Void> stopEmployee(AuthSyncInfo authSyncInfo) {
        String outEa = authSyncInfo.getTenantId();
        //查询企业绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.huawei, outEa, ConfigCenter.HUAWEI_APP_ID);
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        String fsEa = enterpriseBindEntities.get(0).getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        for(AuthSyncInfo.User user : authSyncInfo.getUserList()) {
            String outUserId = user.getUserName();
            //查询人员绑定关系
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntities(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.huawei).outEa(outEa).fsEa(fsEa).outEmpId(outUserId).appId(ConfigCenter.HUAWEI_APP_ID).build());
            if (CollectionUtils.isNotEmpty(employeeBindEntities)) {
                OuterOaEmployeeBindEntity employeeBindEntity = employeeBindEntities.get(0);
                com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.toggle(ei + "",
                        employeeBindEntity.getFsEmpId(),
                        false,
                        Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                        FsEmployeeRoleCodeEnum.SALES.getRoleCode());
                LogUtils.info("HuaweiContactsServiceImpl.addUserList,result={}", result);

                employeeBindEntity.setBindStatus(BindStatusEnum.stop);
                employeeBindEntity.setUpdateTime(System.currentTimeMillis());
                Integer count = outerOaEmployeeBindManager.updateById(employeeBindEntity);
                LogUtils.info("HuaweiContactsServiceImpl.addUserList,stop employee bind,count={}", count);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> syncDepartment(SingleOrgSyncInfo singleOrgSyncInfo) {
        if(singleOrgSyncInfo.getFlag() == 1 || singleOrgSyncInfo.getFlag() == 2) {
            AllOrgSyncInfo.OrgInfo orgInfo = new AllOrgSyncInfo.OrgInfo();
            orgInfo.setOrgCode(singleOrgSyncInfo.getOrgCode());
            orgInfo.setOrgName(singleOrgSyncInfo.getOrgName());
            orgInfo.setParentCode(singleOrgSyncInfo.getParentCode());
            syncDepartment2Crm(singleOrgSyncInfo.getTenantId(), Lists.newArrayList(orgInfo));
        } else if(singleOrgSyncInfo.getFlag() == 0) {
            //查询企业绑定关系
            List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.huawei, singleOrgSyncInfo.getTenantId(), ConfigCenter.HUAWEI_APP_ID);
            if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
                return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
            }
            String fsEa = enterpriseBindEntities.get(0).getFsEa();
            int ei = eieaConverter.enterpriseAccountToId(fsEa);

            List<OuterOaDepartmentBindEntity> departmentBindManagerEntities = outerOaDepartmentBindManager.getEntities(OuterOaDepartmentBindParams.builder().channel(ChannelEnum.huawei).fsEa(fsEa).outEa(singleOrgSyncInfo.getTenantId()).appId(ConfigCenter.HUAWEI_APP_ID).outDepId(singleOrgSyncInfo.getOrgCode()).build());

            if(CollectionUtils.isNotEmpty(departmentBindManagerEntities)) {
                OuterOaDepartmentBindEntity departmentBindEntity = departmentBindManagerEntities.get(0);
                com.facishare.open.order.contacts.proxy.api.result.Result<List<String>> batchStopFsDep = fsContactsServiceProxy.batchStopFsDep(ei, fsEa, departmentBindEntity.getFsDepId());
                //2.批量更新部门绑定状态为停用
                if(batchStopFsDep.isSuccess()) {
                    departmentBindEntity.setBindStatus(BindStatusEnum.stop);
                    departmentBindEntity.setUpdateTime(System.currentTimeMillis());
                    Integer count = outerOaDepartmentBindManager.updateById(departmentBindEntity);
                    LogUtils.info("HuaweiContactsServiceImpl.syncDepartment,stop department bind,count={}", count);
                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> syncAllDepartment(AllOrgSyncInfo allOrgSyncInfo) {
        return syncDepartment2Crm(allOrgSyncInfo.getTenantId(), allOrgSyncInfo.getOrgInfoList());
    }

    private Result<Void> syncDepartment2Crm(String outEa, List<AllOrgSyncInfo.OrgInfo> orgInfoList) {
        //查询企业绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.huawei, outEa, ConfigCenter.HUAWEI_APP_ID);
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        String fsEa = enterpriseBindEntities.get(0).getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        String dcId = enterpriseBindEntities.get(0).getId();

        for(AllOrgSyncInfo.OrgInfo orgInfo : orgInfoList) {
            //查询部门绑定关系
            List<OuterOaDepartmentBindEntity> departmentBindManagerEntities = outerOaDepartmentBindManager.getEntities(OuterOaDepartmentBindParams.builder().channel(ChannelEnum.huawei).fsEa(fsEa).outEa(outEa).appId(ConfigCenter.HUAWEI_APP_ID).outDepId(orgInfo.getOrgCode()).build());
            String fsDepartmentId = GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "";
            if(StringUtils.isNotEmpty(orgInfo.getParentCode())) {
                List<OuterOaDepartmentBindEntity> parentDepartmentBindManagerEntities = outerOaDepartmentBindManager.getEntities(OuterOaDepartmentBindParams.builder().channel(ChannelEnum.huawei).fsEa(fsEa).outEa(outEa).appId(ConfigCenter.HUAWEI_APP_ID).outDepId(orgInfo.getParentCode()).build());

                if(CollectionUtils.isNotEmpty(parentDepartmentBindManagerEntities)) {
                    fsDepartmentId = parentDepartmentBindManagerEntities.get(0).getFsDepId();
                }
            }
            if(CollectionUtils.isEmpty(departmentBindManagerEntities)) {
                //新增
                FsDeptArg arg = FsDeptArg.builder()
                        .ei(ei + "")
                        .name(orgInfo.getOrgName())
                        .code(orgInfo.getOrgCode())
                        .status("0")
                        .parentId(Lists.newArrayList(fsDepartmentId))
                        .build();
                LogUtils.info("HuaweiContactsServiceImpl.initDepList,createArg={}", arg);
                //3.创建纷享部门
                com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> createDepResult = fsDepartmentServiceProxy.create(arg);
                LogUtils.info("HuaweiContactsServiceImpl.initDepList,createDepResult={}", createDepResult);
                if (createDepResult.isSuccess() == false) {
                    LogUtils.info("HuaweiContactsServiceImpl.initDepList,create dep failed,break,createArg={}", arg);
                    return Result.newSuccess();
                }
                OuterOaDepartmentBindEntity departmentBindEntity = new OuterOaDepartmentBindEntity();
                departmentBindEntity.setChannel(ChannelEnum.huawei);
                departmentBindEntity.setFsDepId(createDepResult.getData().getId());
                departmentBindEntity.setOutDepId(orgInfo.getOrgCode());
                departmentBindEntity.setOutEa(outEa);
                departmentBindEntity.setFsEa(fsEa);
                departmentBindEntity.setAppId(ConfigCenter.HUAWEI_APP_ID);
                departmentBindEntity.setDcId(dcId);
                departmentBindEntity.setBindStatus(BindStatusEnum.normal);
                departmentBindEntity.setUpdateTime(System.currentTimeMillis());
                departmentBindEntity.setCreateTime(System.currentTimeMillis());
                Integer count = outerOaDepartmentBindManager.insert(departmentBindEntity);
                LogUtils.info("HuaweiContactsServiceImpl.initDepList,insert department bind,count={}", count);
            } else {
                //更新
                OuterOaDepartmentBindEntity departmentBindEntity = departmentBindManagerEntities.get(0);
                com.facishare.open.order.contacts.proxy.api.result.Result<List<String>> batchResumeFsDep = fsContactsServiceProxy.batchResumeFsDep(ei, fsEa, Lists.newArrayList(departmentBindEntity.getFsDepId()));

                //2.批量更新部门绑定状态为正常
                if(batchResumeFsDep.isSuccess()) {
                    departmentBindEntity.setBindStatus(BindStatusEnum.stop);
                    departmentBindEntity.setUpdateTime(System.currentTimeMillis());
                    Integer count = outerOaDepartmentBindManager.updateById(departmentBindEntity);
                    LogUtils.info("HuaweiContactsServiceImpl.syncDepartment,stop department bind,count={}", count);
                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> createEmployee(String tenantId, AuthSyncInfo.User user) {
        String outEa = tenantId;
        //查询企业绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.huawei, outEa, ConfigCenter.HUAWEI_APP_ID);
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        String fsEa = enterpriseBindEntities.get(0).getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        String dcId = enterpriseBindEntities.get(0).getId();

        String outUserId = user.getUserName();
        //查询人员绑定关系
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntities(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.huawei).outEa(outEa).fsEa(fsEa).outEmpId(outUserId).appId(ConfigCenter.HUAWEI_APP_ID).build());

        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            //新增
            //员工不存在，新增员工并更新员工绑定表
            String sex = "M";

            List<String> fsDepIdList = getFsDepIdList(fsEa, outEa, Lists.newArrayList(user.getOrgCode()));
            List<String> mainDepIdList = CollectionUtils.isEmpty(fsDepIdList) ?
                    Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "") : fsDepIdList;

            FsEmpArg arg = FsEmpArg.builder()
                    .ei(ei + "")
                    .name(user.getName())
                    .fullName(user.getName())
                    .sex(sex)
                    .phone(user.getMobile())
                    .mainDepartment(mainDepIdList)
                    .status("0")
                    .isActive(true)
                    .build();
            com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
                    Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                    FsEmployeeRoleCodeEnum.SALES.getRoleCode());
            LogUtils.info("HuaweiContactsServiceImpl.addUser,result={}", result);
            if (result.isSuccess()) {
                OuterOaEmployeeBindEntity employeeBindEntity = new OuterOaEmployeeBindEntity();
                employeeBindEntity.setChannel(ChannelEnum.huawei);
                employeeBindEntity.setFsEa(fsEa);
                employeeBindEntity.setFsEmpId(result.getData().getId());
                employeeBindEntity.setOutEa(outEa);
                employeeBindEntity.setDcId(dcId);
                employeeBindEntity.setOutEmpId(outUserId);
                employeeBindEntity.setAppId(ConfigCenter.HUAWEI_APP_ID);
                employeeBindEntity.setBindStatus(BindStatusEnum.normal);
                employeeBindEntity.setCreateTime(System.currentTimeMillis());
                employeeBindEntity.setUpdateTime(System.currentTimeMillis());
                Integer insertEmp = outerOaEmployeeBindManager.insert(employeeBindEntity);
                LogUtils.info("HuaweiContactsServiceImpl.addUser,insert employee mapping,count={}", insertEmp);
                return Result.newSuccess();
            } else {
                //如果手机同号则自动匹配。
                if (result.getCode() == 46) {
                    //通过手机号搜索员工
                    com.facishare.open.order.contacts.proxy.api.result.Result<EmployeeDto> mobileResult = fsEmployeeServiceProxy.getEmployeesDtoByEnterpriseAndMobile(ei, user.getMobile());
                    if (mobileResult.isSuccess()) {
                        //修改员工
                        FsEmpArg fsEmpArg = FsEmpArg.builder()
                        .ei(ei + "")
                        .id(mobileResult.getData().getDataId())
                        .status("0")
                        .build();
                        com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateResult = fsEmployeeServiceProxy.update(fsEmpArg, null, null);
                        LogUtils.info("HuaweiContactsServiceImpl.addUser,updateResult={}", updateResult);
                        if(updateResult.isSuccess()) {
                            OuterOaEmployeeBindEntity employeeBindEntity = new OuterOaEmployeeBindEntity();
                            employeeBindEntity.setChannel(ChannelEnum.huawei);
                            employeeBindEntity.setFsEa(fsEa);
                            employeeBindEntity.setFsEmpId(mobileResult.getData().getDataId());
                            employeeBindEntity.setOutEa(outEa);
                            employeeBindEntity.setDcId(dcId);
                            employeeBindEntity.setOutEmpId(outUserId);
                            employeeBindEntity.setAppId(ConfigCenter.HUAWEI_APP_ID);
                            employeeBindEntity.setBindStatus(BindStatusEnum.normal);
                            employeeBindEntity.setCreateTime(System.currentTimeMillis());
                            employeeBindEntity.setUpdateTime(System.currentTimeMillis());
                            Integer insertEmp = outerOaEmployeeBindManager.insert(employeeBindEntity);
                            LogUtils.info("HuaweiContactsServiceImpl.addUser,insert employee mapping2,count={}", insertEmp);
                           return Result.newSuccess();
                        }
                    }
                }
                return Result.newError(ResultCodeEnum.getCodeByOutCode(String.valueOf(result.getCode()), ResultCodeEnum.CRM_USER_ACCOUNT_CREATE_ERROR.getCode()), result.getMsg());
            }
        }
        return Result.newSuccess();
    }
}
