package com.facishare.open.huawei.kit.web.template.outer.event;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.OuterEventHandlerTemplate;
import com.facishare.open.huawei.kit.web.config.ConfigCenter;
import com.facishare.open.huawei.kit.web.result.Result;
import com.facishare.open.huawei.kit.web.result.ResultCodeEnum;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import com.facishare.open.huawei.kit.web.handler.HuaweiEventHandlerManager;
import com.facishare.open.huawei.kit.web.utils.VerifyMessageUtil;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * 华为云外部实例事件处理器模板实现类
 * <AUTHOR>
 * @date 2024-09-19
 */

@Slf4j
@Component
public class HuaweiOuterInstanceTemplate extends OuterEventHandlerTemplate {
    @Resource
    private HuaweiEventHandlerManager huaweiEventHandlerManager;

    @Override
    public void onEventDecode(MethodContext context) {
        String traceId = UUID.randomUUID().toString();
        TraceUtils.initTraceId(traceId);
        log.info("HuaweiOuterInstanceTemplate.onEventDecode,context={}",context);

        KitVerifyTemplateData kitVerifyTemplateData = context.getData();
        try {
            // 验证签名
            Result result = VerifyMessageUtil.verifyRequestParams(kitVerifyTemplateData.getIsvProduceReq(), kitVerifyTemplateData.getRequest().getParameterMap(),
                    ConfigCenter.SIGN_ACCESS_KEY);
            // 如果鉴权返回成功则进行业务处理 验证入参格式 及异常场景校验
            if (result.isSuccess()) {
                // 接口测试
//                if (DEBUG_TEST.equals(instanceVerifyTemplateData.getIsvProduceReq().get("testFlag"))) {
//                    CreateInstanceResult res = CreateInstanceResult.newSuccess(instanceVerifyTemplateData.getIsvProduceReq().get("businessId"));
//                    context.getResult().setData(res);
//                    context.setResult(TemplateResult.newSuccess(null));
//                    return;
//                }

                if (StringUtils.isBlank(kitVerifyTemplateData.getType())) {
                    context.getResult().setData(Result.newError(ResultCodeEnum.INVALID_PARAM));
                    context.setResult(TemplateResult.newError(ResultCodeEnum.INVALID_PARAM.getResultMsg()));
                    return;
                }

                if (result.isSuccess()) {
                    //准备下一步需要的context
                    context.setData(kitVerifyTemplateData);
                    context.setResult(TemplateResult.newSuccess());
                } else {
                    context.getResult().setData(result);
                    context.setResult(TemplateResult.newError(result.resultMsg));
                }
            } else {
                context.getResult().setData(Result.newError(ResultCodeEnum.OTHER_INNER_ERROR));
                context.setResult(TemplateResult.newError(ResultCodeEnum.OTHER_INNER_ERROR.getResultMsg()));
            }
        } catch (Exception e) {
            LogUtils.info("HuaweiOuterInstanceTemplate.push,exception={}",e.getMessage());
            context.getResult().setData(Result.newError(ResultCodeEnum.OTHER_INNER_ERROR));
            context.setResult(TemplateResult.newError(ResultCodeEnum.OTHER_INNER_ERROR.getResultMsg()));
        }
    }

    @Override
    public void onEventFilter(MethodContext context) {
        log.info("HuaweiOuterInstanceTemplate.onEventFilter,context={}",context);
        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void onEventHandle(MethodContext context) {
        log.info("HuaweiOuterInstanceTemplate.onEventHandle,context={}",context);
        KitVerifyTemplateData kitVerifyTemplateData = context.getData();


        TemplateResult templateResult = huaweiEventHandlerManager.handle(kitVerifyTemplateData.getType(), kitVerifyTemplateData);

        context.setResult(templateResult);


//        String activity = instanceVerifyTemplateData.getIsvProduceReq().get("activity");
//
//        // 根据行为不同，分发事件
//        switch (activity) {
//            case HuaweiProduceConstant.NEW_INSTANCE:
//                //创建实例
//                //纷享无需创建实例，直接返回成功
//                String businessId = instanceVerifyTemplateData.getIsvProduceReq().get("businessId");
//                context.setResult(TemplateResult.newSuccess(CreateInstanceResult.newSuccess(businessId)));
//                return;
//            case HuaweiProduceConstant.REFRESH_INSTANCE:
//                //更新实例
//                //没有企业绑定关系时不做处理，通过instanceId查询corpInfo
//                String refreshInstanceInstanceId = instanceVerifyTemplateData.getIsvProduceReq().get("instanceId");
//                com.facishare.open.feishu.syncapi.result.Result<CorpInfoEntity> corpInfoResult = huaweiOrderService.getCorpInfo(refreshInstanceInstanceId);
//                if(ObjectUtils.isEmpty(corpInfoResult.getData())) {
//                    context.setResult(TemplateResult.newSuccess(Result.newSuccess()));
//                    return;
//                }
//                com.facishare.open.feishu.syncapi.result.Result<List<EnterpriseBindEntity>> enterpriseBindList = enterpriseBindService.getEnterpriseBindList(corpInfoResult.getData().getTenantKey());
//                if(CollectionUtils.isEmpty(enterpriseBindList.getData())) {
//                    context.setResult(TemplateResult.newSuccess(Result2.newSuccess()));
//                    return;
//                }
//                //更新订单逻辑
//                String refreshOrderId = instanceVerifyTemplateData.getIsvProduceReq().get("orderId");
//                com.facishare.open.feishu.syncapi.result.Result<CorpInfoEntity> refreshInfoEntityResult = huaweiOrderService.getCorpInfo(refreshInstanceInstanceId);
//                HuaweiOrderDataModel refreshHuaweiOrderDataModel = new HuaweiOrderDataModel();
//                refreshHuaweiOrderDataModel.setInstanceId(refreshInstanceInstanceId);
//                refreshHuaweiOrderDataModel.setTenantId(refreshInfoEntityResult.getData().getTenantKey());
//                refreshHuaweiOrderDataModel.setOrderId(refreshOrderId);
//
//                ThreadPoolHelper.huaweiEventTypeThreadPool.submit(() -> {
//                    TemplateResult refreshTemplateResult = huaweiSaveOrderHandlerTemplate.execute(refreshHuaweiOrderDataModel);
//                    log.info("HuaweiOuterInstanceTemplate.onEventHandle,refreshTemplateResult={}",refreshTemplateResult);
//                });
//
//                context.setResult(TemplateResult.newSuccess(Result.newSuccess()));
//                return;
//            case HuaweiProduceConstant.UPDATE_INSTANCE_STATUS:
//                //todo
//                context.setResult(TemplateResult.newSuccess(Result.newSuccess()));
//                return;
//            case HuaweiProduceConstant.RELEASE_INSTANCE:
//                //todo
//                context.setResult(TemplateResult.newSuccess(Result.newSuccess()));
//                return;
//            case HuaweiProduceConstant.QUERY_INSTANCE:
//                //查询实例
//                //只返回基础信息
//                String queryInstanceInstanceId = instanceVerifyTemplateData.getIsvProduceReq().get("instanceId");
//                QueryInstanceResult queryInstanceResult = huaweiOrderService.queryInstance(queryInstanceInstanceId);
//                context.setResult(TemplateResult.newSuccess(queryInstanceResult));
//                return;
//            case HuaweiProduceConstant.UPGRADE_INSTANCE:
//                //没有企业绑定关系时不做处理，通过instanceId查询corpInfo
//                String upgradeInstanceInstanceId = instanceVerifyTemplateData.getIsvProduceReq().get("instanceId");
//                com.facishare.open.feishu.syncapi.result.Result<CorpInfoEntity> upgradeInstanceCorpInfoResult = huaweiOrderService.getCorpInfo(upgradeInstanceInstanceId);
//                if(ObjectUtils.isEmpty(upgradeInstanceCorpInfoResult.getData())) {
//                    context.setResult(TemplateResult.newSuccess(Result.newSuccess()));
//                    return;
//                }
//                com.facishare.open.feishu.syncapi.result.Result<List<EnterpriseBindEntity>> upgradeInstanceEnterpriseBindList = enterpriseBindService.getEnterpriseBindList(upgradeInstanceCorpInfoResult.getData().getTenantKey());
//                if(CollectionUtils.isEmpty(upgradeInstanceEnterpriseBindList.getData())) {
//                    context.setResult(TemplateResult.newSuccess(Result2.newSuccess()));
//                    return;
//                }
//                //更新订单逻辑
//                String upgradeOrderId = instanceVerifyTemplateData.getIsvProduceReq().get("orderId");
//                com.facishare.open.feishu.syncapi.result.Result<CorpInfoEntity> upgradeInfoEntityResult = huaweiOrderService.getCorpInfo(upgradeInstanceInstanceId);
//                HuaweiOrderDataModel upgradeHuaweiOrderDataModel = new HuaweiOrderDataModel();
//                upgradeHuaweiOrderDataModel.setInstanceId(upgradeInstanceInstanceId);
//                upgradeHuaweiOrderDataModel.setTenantId(upgradeInfoEntityResult.getData().getTenantKey());
//                upgradeHuaweiOrderDataModel.setOrderId(upgradeOrderId);
//
//                ThreadPoolHelper.huaweiEventTypeThreadPool.submit(() -> {
//                    TemplateResult upgradeTemplateResult = huaweiSaveOrderHandlerTemplate.execute(upgradeHuaweiOrderDataModel);
//                    log.info("HuaweiOuterInstanceTemplate.onEventHandle,upgradeTemplateResult={}",upgradeTemplateResult);
//                });
//
//                context.setResult(TemplateResult.newSuccess(Result.newSuccess()));
//                return;
//            default:
//                log.info("unsupported activity: {}", activity);
//                context.setResult(TemplateResult.newSuccess(Result.newSuccess()));
//        }
    }
}
