<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss} [%thread] %-5level %logger{12} %X{traceId} %X{userId} [%class:%line] %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.apache" level="WARN"/>

    <root level="DEBUG">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>