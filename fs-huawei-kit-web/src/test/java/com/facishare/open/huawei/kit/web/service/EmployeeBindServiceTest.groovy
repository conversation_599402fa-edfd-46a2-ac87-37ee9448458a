package com.facishare.open.huawei.kit.web.service

import com.facishare.open.huawei.kit.web.arg.QueryEmployeeBindArg
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/fsHuaweiKitWebAppContext-test.xml")
class EmployeeBindServiceTest extends Specification {
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;

    def "queryEmployeeBind"() {
        given:
        QueryEmployeeBindArg arg = new QueryEmployeeBindArg()
        arg.setFsEa("85903")
        arg.setOutEa("100d08b69448975d")
        arg.setOutUserId(outUserIdCase)
        arg.setFsUserId(fsUserIdCase)
        expect:
        def a = employeeBindService.queryEmployeeBind("feishu", arg)
        print(a)
        where:
        fsUserIdCase  |  outUserIdCase  || result
           "1001"     |      null        || null
           null       |  "ou_0fd979c697c5dd375d12ffb999492a91"  || null
    }

    def "queryEmployeeBindListByOutData"() {
        expect:
        def a = employeeBindService.queryEmployeeBindListByOutData(ChannelEnum.feishu, "16bdc45070d5975f", "ou_ee510be38a602ebc7a60f70a12308a3f")
        print(a)
    }
}
