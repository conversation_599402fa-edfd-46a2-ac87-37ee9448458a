package com.facishare.open.huawei.kit.web;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring/fsHuaweiKitWebAppContext.xml"})
public class BaseTest {

    @BeforeClass
    public static void SetUp(){
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name","fs-huawei-kit-web");
    }

    @Test
    public void emptyTest() {
        System.out.println("test passed");
    }
}