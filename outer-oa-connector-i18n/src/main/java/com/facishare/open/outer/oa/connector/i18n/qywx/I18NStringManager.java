package com.facishare.open.outer.oa.connector.i18n.qywx;

import com.facishare.converter.EIEAConverter;
import com.facishare.organization.adapter.api.config.model.GetConfigDto;
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
import com.fxiaoke.i18n.client.I18nClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 国际化字符串管理器
 * <AUTHOR>
 * @date 2023-10-13
 */
@Slf4j
@Component(value = "qywxI18NStringManager")
public class I18NStringManager {
    public static final String X_FS_LOCALE = "x-fs-locale";
    public static final String DEFAULT_LANG = "zh-CN";
    @Resource
    private EnterpriseConfigService enterpriseConfigService;
    @Resource
    private EIEAConverter eieaConverter;

    @PostConstruct
    private void init() {
        try {
            I18nClient.getInstance().initWithTags("erpdss_outer_oa_connector","server");//erpdss是集成平台专用国际化标签
        } catch (Throwable e) {
            log.warn("I18NStringManager.init,I18N init error", e);
        }
    }

    public void setDefaultRequestScope(HttpServletRequest request,String lang) {
        request.setAttribute("errorPageTitle",get(I18NStringEnum.s166,lang,null));

        request.setAttribute("errorMsgTip",get(I18NStringEnum.s167,lang,null));
        request.setAttribute("proposeTip",get(I18NStringEnum.s168,lang,null));

        request.setAttribute("errorPageTip",get(I18NStringEnum.s160,lang,null));
        request.setAttribute("errorPageTip2",get(I18NStringEnum.s161,lang,null));

        request.setAttribute("resyncEmp",get(I18NStringEnum.s162,lang,null));
        request.setAttribute("createEmpSuccess",get(I18NStringEnum.s163,lang,null));
        request.setAttribute("createEmpFailed",get(I18NStringEnum.s164,lang,null));
    }

    public String getDefaultLang(String tenantId) {
        try {
            GetConfigDto.Argument argument = new GetConfigDto.Argument();
            argument.setEnterpriseId(Integer.valueOf(tenantId));
            argument.setEmployeeId(1000);
            argument.setKey("enterprise_default_language");
            GetConfigDto.Result config = enterpriseConfigService.getConfig(argument);
            log.info("getDefaultLang,ei={},lang={}",tenantId,config.getValue());
            return config.getValue();
        } catch (Exception e) {
            return DEFAULT_LANG;
        }
    }

    public String getDefaultLangByFsEa(String fsEa) {
        if(StringUtils.isEmpty(fsEa))
            return DEFAULT_LANG;
        String tenantId = eieaConverter.enterpriseAccountToId(fsEa)+"";
        return getDefaultLang(tenantId);
    }

    public String get(String i18NKey, String lang, String tenantId, String defaultVal) {
        try {
            if(StringUtils.isEmpty(i18NKey)) return defaultVal;
            if(StringUtils.equalsIgnoreCase(i18NKey,I18NStringEnum.s1.getI18nKey())) {
                if(StringUtils.isNotEmpty(defaultVal) && !StringUtils.equalsIgnoreCase(I18NStringEnum.s1.getI18nValue(),defaultVal))
                    return defaultVal;
            }
            if(StringUtils.isEmpty(lang) && StringUtils.isNotEmpty(tenantId)) {
                lang = getDefaultLang(tenantId);
            }
            lang = StringUtils.isEmpty(lang) ? DEFAULT_LANG : lang;
            return I18nClient.getInstance().get(i18NKey,0, lang, defaultVal);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    public String get2(I18NStringEnum i18nEnum, String lang, String tenantId, String... extra) {
        return get2(i18nEnum.getI18nKey(), lang, tenantId, i18nEnum.getI18nValue(), Arrays.stream(extra).collect(Collectors.toList()));
    }

    public String get2(String i18NKey, String lang, String tenantId, String defaultVal, List<String> extra) {
        try {
            if(StringUtils.isEmpty(i18NKey)) return defaultVal;
            if(StringUtils.equalsIgnoreCase(i18NKey,I18NStringEnum.s1.getI18nKey())) {
                if(StringUtils.isNotEmpty(defaultVal) && !StringUtils.equalsIgnoreCase(I18NStringEnum.s1.getI18nValue(),defaultVal))
                    return defaultVal;
            }
            String msg = get(i18NKey, lang, tenantId, defaultVal);
            if(!StringUtils.containsIgnoreCase(msg,"%s")) {
                return msg;
            }
            Object[] args = extra.size() > 0 ? extra.toArray() : null;
            return String.format(msg, args);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    public String getByEi(String i18NKey, String ei, String defaultVal) {
        try {
            if(StringUtils.isEmpty(i18NKey) || StringUtils.isEmpty(ei)) {
                return defaultVal;
            }
            if(StringUtils.equalsIgnoreCase(i18NKey,I18NStringEnum.s1.getI18nKey())) {
                if(StringUtils.isNotEmpty(defaultVal) && !StringUtils.equalsIgnoreCase(I18NStringEnum.s1.getI18nValue(),defaultVal))
                    return defaultVal;
            }
            String lang = DEFAULT_LANG;
            if (StringUtils.isNotEmpty(ei)) {
                lang = getDefaultLang(ei);
            }
            return I18nClient.getInstance().get(i18NKey,0, lang, defaultVal);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    public String getByEi2(I18NStringEnum i18nEnum, String tenantId, String... extra) {
        return getByEi2(i18nEnum.getI18nKey(), tenantId, i18nEnum.getI18nValue(), Arrays.stream(extra).collect(Collectors.toList()));
    }

    public String getByEi2(String i18NKey, String ei, String defaultVal, List<String> extra) {
        try {
            if(StringUtils.isEmpty(i18NKey) || StringUtils.isEmpty(ei)) return defaultVal;
            if(StringUtils.equalsIgnoreCase(i18NKey,I18NStringEnum.s1.getI18nKey())) {
                if(StringUtils.isNotEmpty(defaultVal) && !StringUtils.equalsIgnoreCase(I18NStringEnum.s1.getI18nValue(),defaultVal))
                    return defaultVal;
            }
            String msg = getByEi(i18NKey, ei, defaultVal);
            if(!StringUtils.containsIgnoreCase(msg,"%s")) {
                return msg;
            }
            Object[] args = extra.size() > 0 ? extra.toArray() : null;
            return String.format(msg, args);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    public String get(I18NStringEnum i18NStringEnum, String lang, String tenantId) {
        return get(i18NStringEnum.getI18nKey(), lang, tenantId, i18NStringEnum.getI18nValue());
    }

    public String getByEi(I18NStringEnum i18NStringEnum, String ei) {
        return getByEi(i18NStringEnum.getI18nKey(), ei, i18NStringEnum.getI18nValue());
    }
}
