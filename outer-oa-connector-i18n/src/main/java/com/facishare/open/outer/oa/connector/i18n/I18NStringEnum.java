package com.facishare.open.outer.oa.connector.i18n;

import com.fxiaoke.i18n.client.I18nClient;
import com.github.trace.TraceContext;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 国际化词条枚举类
 * <AUTHOR>
 * @date 2023-10-16
 */
@SuppressWarnings("SpellCheckingInspection")
@Getter
@ToString
@AllArgsConstructor
public enum I18NStringEnum {
    s1("成功"),
    s2("参数不合法"),
    s3("系统错误"),
    s4("外部服务限速，请稍后重试"),
    s5("企业绑定失败，请检查参数是否正确"),
    s6("客户创建失败"),
    s7("订单创建失败"),
    s8("不存在订单信息"),
    s9("ticket不存在"),
    s10("ticket已过期"),
    s11("CRM应用未安装"),
    s12("飞书企业已经和当前纷享企业绑定，请勿重复绑定"),
    s13("保存纷享和飞书企业绑定信息失败"),
    s14("查询不到已绑定的应用管理员"),
    s15("企业未绑定"),
    s16("解绑失败"),
    s17("获取CRM安装人员失败"),
    s18("连接器连接信息不存在"),
    s19("连接器连接信息已存在"),
    s20("没有未绑定的纷享员工"),
    s21("员工没有绑定关系"),
    s22("员工已绑定，无需重新绑定"),
    s23("第三方企业未绑定"),
    s24("身份校验失败，cookie不存在"),
    s25("消息发送失败"),
    s26("自定义函数调用失败"),
    s27("拉取模板失败"),
    s28("创建模板失败"),
    s29("更新模板失败"),
    s30("删除模板失败"),
    s31("获取企业下的手机号码失败"),
    s32("发送消息失败"),
    s33("获取文件元数据失败"),
    s34("图片大小超过5M"),
    s35("音频大小超过16M"),
    s36("视频大小超过16M"),
    s37("文档大小超过100M"),
    s38("贴纸大小超过100KB"),
    s39("whatsapp账号已被绑定"),
    s40("查询牛信云余额失败"),
    s41("上传文件失败"),
    s42("上传模板文件失败"),
    s43("同一个纷享企业，不同的飞书连接器，不能绑定同一个飞书企业"),
    s44("当前绑定的飞书企业已经和别的纷享企业存在自动绑定关系，请先解绑原自动绑定关系，再执行反绑定操作;fsEa=%s"),
    s45("当前绑定的飞书企业已经和别的纷享企业存在自动绑定关系，请先解绑原自动绑定关系，再执行反绑定操作"),
    s46("指定的成员无权限"),
    s47("贵企业总账号已达上限"),
    s48("该员工创建失败"),
    s49("调用外部系统接口失败"),
    s50("查询纷享日程失败"),
    s51("创建日历失败"),
    s52("创建日程失败"),
    s53("添加日程参与人失败"),
    s54("更新日程失败"),
    s55("删除日程参与人失败"),
    s56("更新日程失败"),
    s57("没有自动绑定的飞书企业，不支持解绑。请到原手动绑定的企业进行解绑，路径：集成平台->连接器->飞书连接器"),
    s58("数据列表不可为空"),
    s59("NPATH文件下载失败"),
    s60("创建审批模板失败"),
    s61("创建审批失败"),
    s62("处理审批失败"),
    s63("删除审批失败"),
    s64("处理失败，请到纷享销客crm处理"),
    s65("请关闭页面，重新登陆进去纷享crm"),
    s66("登陆失败"),
    s67("通过飞书数据(corpId=%s,userId=%s)，创建纷享员工账号失败"),
    s68("1、该员工是否在飞书纷享应用可见范围\n2、该企业无飞书通讯录调用权限，请贵企业管理员在飞书纷享应用权限上勾选【获取通讯录权限范围】"),
    s69("该企业纷享crm员工账号已达上限，请联系纷享客服处理"),
    s70("通过飞书数据(corpId=%s,userId=%s)，找不到绑定的员工账号"),
    s71("该员工账号未创建，如果超过一个小时还未创建成功，请点击下面按钮重新创建员工账号"),
    s72("请检查该员工飞书账号是否已绑定纷享crm员工账号"),
    s73("飞书CRM应用未安装，请先安装飞书CRM应用"),
    s74("调用获取飞书管理员接口失败"),
    s75("调用获取飞书管理员接口失败，请把这个问题，反馈给纷享研发人员"),
    s76("当前扫码人员，不是飞书CRM应用的管理员"),
    s77("请检查，当前扫码人员，是否有飞书CRM应用管理员权限，如果没有，请找有飞书CRM应用管理员权限的人员来扫码"),
    s78("请检查该飞书企业是否已绑定纷享企业"),
    s79("通过飞书数据(corpId=%s,userId=%s)，找不到绑定的企业账号"),
    s80("登陆的纷享crm账号没有权限"),
    s81("请登录有权限的纷享crm，再刷新页面，或者向管理员申请工具使用的权限"),

    /**
     * s82~s92
     */

    s93("同意"),
    s94("拒绝"),

    s95("员工"),

    s96("飞书人员绑定表"),

    s97("飞书连接器人员导入结果"),

    s98("导入中，导入结果留意企信通知"),

    s99("部门"),

    s100("飞书连接器"),

    s101("纷享销客"),

    s102("页面出错了"),

    s103("请联系贵公司CRM管理员或IT管理员根据以下错误信息进行排查"),

    s104("错误信息"),

    s105("排查建议"),

    s106("重新同步此账号"),

    s107("同步中..."),

    s108("创建成功，请重新打开页面"),

    s109("创建失败，请稍后重新点击同步"),

    s110("示例：请删除该行"),

    s111("纷享部门ID"),

    s112("纷享部门名称"),

    s113("飞书部门ID"),

    s114("飞书部门名称"),

    s115("纷享员工ID"),

    s116("纷享员工名称"),

    s117("飞书员工手机号"),

    s118("飞书员工名称"),

    s119("纷享员工ID & 飞书员工手机号 字段不能为空"),

    s120("纷享员工不存在，纷享员工ID错误,纷享员工ID="),

    s121("根据飞书手机号查询飞书用户失败"),

    s122("根据飞书手机号找不到飞书用户"),

    s123("纷享用户已存在绑定关系，纷享用户ID="),

    s124("飞书手机号对应的飞书用户已存在绑定关系，飞书用户手机号="),

    s125("员工导入结果：\n"),

    s126("解析成功【%s】条，新增数据【%s】条，更新数据【%s】条"),

    s127("处理失败【%s】条："),

    s128("  行号【%s】：%s"),

    s129("解析异常【%s】条："),

    s130("纷享员工查询失败"),
    
    s131("此节点无法直接审批，请到CRM处理"),

    s132("部门导入结果：\n"),

    s133("飞书"),

    s134("用户不在可见范围"),

    s135("用户部门不在可见范围"),
    s136("飞书自建应用没有绑定"),
    s137("飞书应用信息不对，请检查配置app_id&app_sectet是否正确"),
    s138("飞书应用信息不对，请检查配置app_id&app_sectet是否正确"),
    s139("企业微信接口许可证号订单未付款"),

    s140("查询外部员工信息失败"),

    s141("通过华为数据(corpId=%s,userId=%s)，创建纷享员工账号失败"),

    s142("人员开通失败"),

    s143("请联系纷享客服"),

    s144("转换纷享员工信息失败"),


    s145("通过飞书数据(corpId=%s,userId=%s)，找不到对应的互联企业"),

    s146("请检查该企业是否有互联企业身份"),

    s147("通过飞书数据(corpId=%s,userId=%s)，找不到对应的互联用户"),

    s148("请检查该人员是否有互联用户身份"),
    s170("外部数据不存在"),
    s171("OA连接器已过期"),
    s172("部门无绑定信息"),
    s173("外部OA部门数据不存在"),
    s174("现在只支持isv跳转"),
    S175("账号绑定数据"),
    s176("账号绑定列表"),
    s177("未绑定账号列表"),
    s178("避免外部数据字段都是空，需要必须选择名字"),
    s179("由于更新了人员字段映射，后台还在重新拉取全部人员数据，请稍后重试~"),
    s180("执行超时，请稍后重试"),
    s181("账号绑定模板"),
    s182("批量同步员工成功%s条，失败%s.失败原因：%s"),
    s183("存在未保存设置的连接器"),
    s184("系统名(昵称)"),
    s185("邮箱"),
    s186("手机号码"),
    s187("员工编号"),
    s188("主属部门"),
    s189("归属部门"),
    s190("员工状态"),
    s191("用户ID"),
    s192("汇报对象"),
    s193("openId"),
    s194("用户名"),
    s195("工号"),
    s196("钉钉职员ID"),
    s197("直属上级"),
    s198("成员UserID"),
    s199("成员名称"),
    s200("配置字段"),
    s201("企业微信"),
    s202("钉钉"),
    s203("飞书"),
    s204("lark"),
    s205("阿里云市场"),
    s206("华为云"),
    s207("WhatsApp"),
    s208("飞书"),

    cxb001("渠道不存在:{0}"),
    cxb002("该企业绑定了多个应用"),
    cxb003("dcId不存在"),
    cxb004("钉钉自建连接器"),
    cxb005("钉钉isv连接器"),
    cxb006("任务处理中,请稍候"),
    cxb007("前面有任务在处理中,完成后请刷新页面"),
    cxb008("该企业已绑定钉钉isv应用"),
    cxb009("该钉钉应用已绑定其他企业"),

    //czx用 start
    c001("企微部门id格式填写错误"),
    c002("企微应用agentId为填写或者填写错误"),
    c003("通过企微id找不到安装的应用信息"),
    c004("企微应用已停用，请重新启用"),
    c005("当前纷享企业已经绑定了该企微企业同一个应用"),
    c006("当前企业已经绑定了其他类型的企微应用，请先在企微连接器解绑其他类型应用的绑定关系"),
    c007("暂不支持此功能"),
    c008("成功"),
    c009("找不到指定的汇报对象，可能已离职。"),
    c010("没有对应的部门"),
    c011("系统繁忙"),
    c012("系统错误"),
    c013("用户不是应用管理员"),
    c014("Cloud用户名或密码不正确"),
    c015("企业未绑定或不存在"),
    c016("中转客户端post请求钉钉openapi失败"),
    c017("参数错误"),
    c018("超出批量查询最大数量1000"),
    c019("对接配置不存在"),
    c020("对接配置已存在"),
    c021("同步任务正在运行，请等待结果通知"),
    c022("不支持手动同步"),
    c023("已存在绑定账号"),
    c024("员工未绑定"),
    c025("已经有企业与该cloud已经建立连接"),
    c026("钉钉接口异常"),
    c027("注册回调业务接口失败,请检查参数"),
    c028("钉钉appKey或appSecret或clientIp错误"),
    c029("企业保存成功"),
    c030("查询钉钉部门列表失败"),
    c031("正在初始化钉钉用户，请稍后"),
    c032("查询钉钉员工失败"),
    c033("消息接收人员为空"),
    c034("钉钉corpId或clientIp错误"),
    c035("token不存在"),
    c036("该纷享职员已被绑定"),
    c037("字段初始化失败"),
    c038("获取纷享职员信息失败"),
    c039("创建纷享职员失败"),
    c040("修改纷享职员失败"),
    c041("停用纷享职员失败"),
    c042("查询钉钉部门失败"),
    c043("根据负责人查询其一级部门失败"),
    c044("企业回调地址已经存在,不影响初始化组织架构，但请按照对接文档配置应用回调"),
    c045("企业员工已超额"),
    c046("部门名称已存在"),
    c047("人员名称已存在"),
    c048("人员手机号已存在"),
    c049("人员账号已存在"),
    c050("人员账号已存在"),
    c051("CRM人员与钉钉人员信息不一致"),
    c052("正在全量同步数据，请稍后"),
    c053("员工已停用"),
    c054("员工配额已满"),
    c055("纷享没有该数据"),
    c056("纷享生成JS失败"),
    c057("服务错误"),
    c058("ACCESS TOKEN过期"),
    c059("此待办不支持跳转，请到纷享内处理"),
    c060("函数执行报错"),
    c061("消息过滤"),

    //czx用 end

    gyd001("oa开放连接器"),
    kniip("不允许使用内网IP"),
    knfuser("无法找到该员工，请确认员工Id和应用权限范围"),
    ;
    final String i18nKey;
    final String i18nValue;

    I18NStringEnum(String i18nValue) {
        i18nKey = "erpdss_outer_oa_connector.feishu.global." + name();
        this.i18nValue = i18nValue;
    }

    //前端请求没走异步，和是统一的异步Controller的异步一定可以用，其他的自己斟酌
    public String get() {
        String locale = TraceContext.get().getLocale();
        if (locale == null) {
            locale = "zh-CN";
        }
        try {
            return get(locale);
        } catch (Exception ignore) {
            return i18nValue;
        }
    }

    public String get(String locale) {
        return I18nClient.getInstance().get(this.i18nKey, 0, locale, this.i18nValue);
    }

    public static void main(String[] args) {
        String filePath = "/Users/<USER>/Downloads/template_auto200.xlsx";
        
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Sheet1");
            
            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {"key", "zh_CN", "tags"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 添加数据
            int rowNum = 1;
            for (I18NStringEnum codeEnum : I18NStringEnum.values()) {
                String name = codeEnum.name().toLowerCase();
                if (name.startsWith("s") && name.length() == 4) {
                    try {
                        int num = Integer.parseInt(name.substring(1));
                        if (num >= 1 && num <= 208 && !StringUtils.isEmpty(codeEnum.getI18nValue())) {
                            Row row = sheet.createRow(rowNum++);
                            row.createCell(0).setCellValue(codeEnum.getI18nKey());
                            row.createCell(1).setCellValue(codeEnum.getI18nValue());
                            row.createCell(2).setCellValue("server");
                        }
                    } catch (NumberFormatException ignored) {
                        // 忽略非数字的情况
                    }
                }
            }
            
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // 写入文件
            try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
                workbook.write(fileOut);
            }
            System.out.println("Template file generated successfully at: " + filePath);
            
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
