package com.facishare.open.outer.oa.connector.common.api.params;

import com.facishare.open.outer.oa.connector.common.api.annotation.SecurityField;
import com.facishare.open.outer.oa.connector.common.api.info.BaseOuterOaAppInfoParams;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class QyweixinAppInfoParams extends BaseOuterOaAppInfoParams {
    private static final long serialVersionUID = -1L;
    //永久授权码
    @SecurityField
    private String permanentCode;
    //授权企业信息
    private AuthCorpInfo authCorpInfo;

    @Data
    public static class AuthCorpInfo implements Serializable {
        private static final long serialVersionUID = -1L;
        private String corpName;
        private String corpType;
        private String corpSquareLogoUrl;
        private String corpFullName;
        private int subjectType;
    }

    //授权应用信息
    private AuthAppInfo authAppInfo;

    @Data
    public static class AuthAppInfo implements Serializable {
        private static final long serialVersionUID = -1L;
        private int agentId;
        private String name;
        private String roundLogoUrl;
        private String squareLogoUrl;
        private int authMode;//授权模式，0为管理员授权；1为成员授权
        private AgentSharedFrom sharedFrom;

        @Data
        public static class AgentSharedFrom implements Serializable {
            private static final long serialVersionUID = -1L;
            private String corpId;
            private String shareType;
        }
    }

    //授权应用的用户信息
    private AuthAppUserInfo authAppUserInfo;

    @Data
    public static class AuthAppUserInfo implements Serializable {
        private static final long serialVersionUID = -1L;
        private String userId;
        private String name;
        private String avatar;
    }
}
