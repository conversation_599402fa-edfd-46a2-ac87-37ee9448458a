package com.facishare.open.outer.oa.connector.common.api.enums;

import java.io.Serializable;

public enum LicenseEnum implements Serializable {
    DING_TALK_DOCK_APP(ChannelEnum.dingding, "ding_talk_dock_app"),
    FEISHU_CONNECTOR_APP(ChannelEnum.feishu, "feishu_data_sync_app"),
    LARK_CONNECTOR_APP(ChannelEnum.lark, "lark_data_sync_app"),;

    private final ChannelEnum channelEnum;
    private final String moduleCode;

    LicenseEnum(ChannelEnum channelEnum, String moduleCode) {
        this.channelEnum = channelEnum;
        this.moduleCode = moduleCode;
    }

    /**
     * 获取渠道枚举
     * 
     * @return 渠道枚举
     */
    public ChannelEnum getChannelEnum() {
        return channelEnum;
    }

    /**
     * 获取模块代码
     * 
     * @return 模块代码
     */
    public String getModuleCode() {
        return moduleCode;
    }
}
