package com.facishare.open.outer.oa.connector.common.api.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class HuaweiEnterpriseConnectVo extends BaseConnectorVo {
    private static final long serialVersionUID = -1L;
    private String domain;
    /**
     * 外部域名
     */
    private String outDomain;

    /**
     * 外部企业名称
     */
    private String outEnterpriseName;
}
