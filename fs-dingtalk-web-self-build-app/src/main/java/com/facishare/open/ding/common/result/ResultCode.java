package com.facishare.open.ding.common.result;

import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;

/**
 * Created by system on 2018/3/29.
 */
public enum ResultCode {

	/** 成功 **/
	SUCCESS(0, "success", "成功", I18NStringEnum.c008.getI18nKey()),
	LEADER_NOT_EXISTS_CODE(29, "leader not exists", "找不到指定的汇报对象，可能已离职。", I18NStringEnum.c009.getI18nKey()),
	DEPT_NOT_EXISTS_CODE(27, "dept not exists", "没有对应的部门", I18NStringEnum.c010.getI18nKey()),

	/** 系统繁忙 */
	SERVER_BUSY(-1, "server is busy", "系统繁忙", I18NStringEnum.c011.getI18nKey()),

	/** 系统错误 */
	SYSTEM_ERROR(-2, "system error", "系统错误", I18NStringEnum.c012.getI18nKey()),

	NOT_APP_MANAGER(-3, "not app manager", "用户不是应用管理员", I18NStringEnum.c013.getI18nKey()),

	NOT_CLOUD_MANAGER(-4, "not cloud manager", "Cloud用户名或密码不正确", I18NStringEnum.c014.getI18nKey()),

	ENTERPRISE_NOT_BIND(-5, "the enterprise not bind", "企业未绑定或不存在", I18NStringEnum.c015.getI18nKey()),

	CLIENT_POST_FAILED(-8,"client post error","中转客户端post请求钉钉openapi失败", I18NStringEnum.c016.getI18nKey()),


	PARAMS_ERROR(-101, "params error", "参数错误", I18NStringEnum.c017.getI18nKey()),

	OUT_OF_LIMIT(-102, "query count is out of limit, maximum is 1000", "超出批量查询最大数量1000", I18NStringEnum.c018.getI18nKey()),

	CONFIGURATION_NOT_EXIST(-201, "configuration not exist", "对接配置不存在", I18NStringEnum.c019.getI18nKey()),

	CONFIGURATION_ALREADY_EXIST(-202, "configuration already exist", "对接配置已存在", I18NStringEnum.c020.getI18nKey()),

	SYNC_TASK_ALREADY_RUNNING(-203, "sync task is already running", "同步任务正在运行，请等待结果通知", I18NStringEnum.c021.getI18nKey()),

	NOT_SUPPORT_MANUAL_SYNC(-204, "not support manual sync", "不支持手动同步", I18NStringEnum.c022.getI18nKey()),

	ALREADY_BIND_ACCOUNT(-206, "already bind account", "已存在绑定账号", I18NStringEnum.c023.getI18nKey()),

	NOT_BIND_EMP(-207, "emp not bind", "员工未绑定", I18NStringEnum.c024.getI18nKey()),

	CLOUD_HAS_CONNECTED(-300,"cloud has connected","已经有企业与该cloud已经建立连接", I18NStringEnum.c025.getI18nKey()),

	DING_SERVER_EXCEPTION(-301, "dingding server exception", "钉钉接口异常", I18NStringEnum.c026.getI18nKey()),

	DING_REGIST_FAILED(-304, "regist call back failed", "注册回调业务接口失败,请检查参数", I18NStringEnum.c027.getI18nKey()),

	DING_CONNECT_PARAM_ERROR(-305,"dingding params error","钉钉appKey或appSecret或clientIp错误", I18NStringEnum.c028.getI18nKey()),

	SAVE_ENTERPRISE_ERROR(-306,"save enterprise failed","企业保存成功", I18NStringEnum.c029.getI18nKey()),

	DEPT_LIST_ERROR(-308,"query dingding dept list failed","查询钉钉部门列表失败", I18NStringEnum.c030.getI18nKey()),

	INIT_DING_EMP(-310, "dingding employee is initing", "正在初始化钉钉用户，请稍后", I18NStringEnum.c031.getI18nKey()),

	GET_DING_EMP_FAILED(-312, "get dingding user failed", "查询钉钉员工失败", I18NStringEnum.c032.getI18nKey()),

	TO_USER_EMPTY(-314,"message receiver is empty","消息接收人员为空", I18NStringEnum.c033.getI18nKey()),

	DING_CORPID_ERROR(-316,"dingding params error","钉钉corpId或clientIp错误", I18NStringEnum.c034.getI18nKey()),

	TOKEN_NOT_EXIST(-401, "token not exist", "token不存在", I18NStringEnum.c035.getI18nKey()),

	EMP_HAS_BIND(-601,"employee has been binded","该纷享职员已被绑定", I18NStringEnum.c036.getI18nKey()),

	FIELD_INIT_ERROR(-602,"bind field failed","字段初始化失败", I18NStringEnum.c037.getI18nKey()),

	GET_FXEMP_FAILED(-603,"get fx employee failed","获取纷享职员信息失败", I18NStringEnum.c038.getI18nKey()),

	CREATE_FXEMP_FAILED(-604,"create fx employee failed","创建纷享职员失败", I18NStringEnum.c039.getI18nKey()),

	MODIFY_FXEMP_FAILED(-605,"modify fx employee failed","修改纷享职员失败", I18NStringEnum.c040.getI18nKey()),

	STOP_FXEMP_FAILED(-606,"stop fx employee failed","停用纷享职员失败", I18NStringEnum.c041.getI18nKey()),
	QUERY_DEPT_DETAIL(-607,"query ding dept failed","查询钉钉部门失败", I18NStringEnum.c042.getI18nKey()),


	GET_ORG_BY_OWNER_FAILED(-701,"get org by owner failed","根据负责人查询其一级部门失败", I18NStringEnum.c043.getI18nKey()),
	DING_CALL_BACK_URL_EXIST(-705, "regist call back failed", "企业回调地址已经存在,不影响初始化组织架构，但请按照对接文档配置应用回调", I18NStringEnum.c044.getI18nKey()),
	AVAILABLE_EMPLOYEE_NOT_ENOUGH(-705, "regist call back failed", "企业员工已超额", I18NStringEnum.c045.getI18nKey()),
	DEPT_NAME_IS_EXIST(*********, "circle name is exist", "部门名称已存在", I18NStringEnum.c046.getI18nKey()),
	EMPLOYEE_NAME_IS_EXIST(*********, "employee name is exist", "人员名称已存在", I18NStringEnum.c047.getI18nKey()),

	EMPLOYEE_MOBILE_EXIST(46, "employee mobile is exist", "人员手机号已存在", I18NStringEnum.c048.getI18nKey()),
	EMPLOYEE_ACCOUNT_EXIST(********, "employee account is exist", "人员账号已存在", I18NStringEnum.c049.getI18nKey()),
	EMPLOYEE_PHONE_BIND_EXIST(********, "employee phone account is exist", "人员账号已存在", I18NStringEnum.c050.getI18nKey()),
	EMPLOYEE_IS_EXIST(********, "employee mobile is exist", "CRM人员与钉钉人员信息不一致", I18NStringEnum.c051.getI18nKey()),
	ALL_PULL_ORGANIZATION_ING(********, "all pull Organization is initing", "正在全量同步数据，请稍后", I18NStringEnum.c052.getI18nKey()),
	EMPLOYEE_IS_STOP(********, "EMPLOYEE HAVEN STOPED", "员工已停用", I18NStringEnum.c053.getI18nKey()),
	ENTERPRISE_COUNT_FULL(44, "ENTPRISE_EMP_COUNT_FULL", "员工配额已满", I18NStringEnum.c054.getI18nKey()),
	FS_CLOUD_NOT_DATA(45, "FS_CLOUD_NOT_DATA", "纷享没有该数据", I18NStringEnum.c055.getI18nKey()),
	FS_CLOUD_CREATE_API_SIGNATURE(46, "FS_CLOUD_CREATE_API_SIGNATURE", "纷享生成JS失败", I18NStringEnum.c056.getI18nKey()),

	SERVER_ERROR(47, "SERVER_ERROR", "服务错误", I18NStringEnum.c057.getI18nKey()),

	ACCESS_TOKEN_INVALID(50, "SERVER_ERROR", "ACCESS TOKEN过期", I18NStringEnum.c058.getI18nKey()),

	TODO_NOT_SUPPORT(60, "fs todo not supported", "此待办不支持跳转，请到纷享内处理", I18NStringEnum.c059.getI18nKey()),

	FUNCTION_FILTER_MESSAGE_ERROR(10100101, "todo function had errormessage", "函数执行报错", I18NStringEnum.c060.getI18nKey()),

	TODO_MESSAGE_FILTER_NOT_SEND(10100102, "todo message filter not send", "消息过滤", I18NStringEnum.c061.getI18nKey()),
	;

	/** 错误码 */
	private int errorCode;

	/** 错误信息 */
	private String errorMessage;

	/** 错误描叙 */
	private String description;

	/** 多语key */
	private String i18nKey;

	ResultCode(int errorCode, String errorMessage, String description) {
		this.errorCode = errorCode;
		this.errorMessage = errorMessage;
		this.description = description;
		this.i18nKey = null;
	}

	ResultCode(int errorCode, String errorMessage, String description, String i18nKey) {
		this.errorCode = errorCode;
		this.errorMessage = errorMessage;
		this.description = description;
		this.i18nKey = i18nKey;
	}

	public Integer getErrorCode() {
		return errorCode;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public String getDescription() {
		return description;
	}

	public String getI18nKey() {
		return i18nKey;
	}

	public static ResultCode getEnumsByErrorCode(int errorCode) {
		ResultCode[] resultCodesEnums = ResultCode.values();
		for (int i = 0 ; i < resultCodesEnums.length; i++) {
			if (resultCodesEnums[i].errorCode == errorCode) {
				return resultCodesEnums[i];
			}
		}
		return null;
	}
}
