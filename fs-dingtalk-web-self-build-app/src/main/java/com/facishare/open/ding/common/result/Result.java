package com.facishare.open.ding.common.result;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;

/**
 * <p>类的详细说明</p>
 * <AUTHOR> yin<PERSON>@fxiaoke.com
 * @version 1.0
 * @dateTime 2018/7/12 11:59
 */
@ToString
// IgnoreI18nFile
@AllArgsConstructor
@Data
public class Result<T> implements Serializable {

    private int errorCode = ResultCode.SUCCESS.getErrorCode();

    private String errorMessage = ResultCode.SUCCESS.getErrorMessage();

    private String errorDescription;

    @JsonIgnore
    @JSONField(serialize = false)
    private String i18nKey;
    @JsonIgnore
    @JSONField(serialize = false)
    private List<String> i18nExtra;

    private T data;

    /**
     * 保证Hessian 序列化成功
     **/
    public Result() {
    }

    private Result(ResultCode resultCode, T data) {
        this.errorCode = resultCode.getErrorCode();
        this.errorMessage = resultCode.getErrorMessage();
        this.errorDescription = resultCode.getDescription();
        this.data = data;
    }

    private Result(int errorCode, String errorMessage, T data) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.data = data;
    }

    private Result(ResultCode resultCode) {
        this.errorCode = resultCode.getErrorCode();
        this.errorMessage = resultCode.getErrorMessage();
        this.errorDescription = resultCode.getDescription();
    }

    private Result(int errorCode, String errorMessage, String errorDescription) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.errorDescription = errorDescription;
    }

    public static <R> Result<R> newError(ResultCode resultCode, R data) {
        return new Result<>(resultCode, data);
    }

    public static <R> Result<R> newError(ResultCode resultCode) {
        return new Result<>(resultCode);
    }

    public static <R> Result<R> newError(int errorCode, String errorMessage) {
        return new Result<>(errorCode, errorMessage, null);
    }

    public static <R> Result<R> newError(int errorCode, String errorMessage, String errorDescription) {
        return new Result<>(errorCode, errorMessage, errorDescription);
    }

    public static <R> Result<R> newSuccess(R data) {
        return new Result<>(ResultCode.SUCCESS, data);
    }

    public static <R> Result<R> newSuccess(int errorCode, String errorMessage, R data) {
        return new Result<>(errorCode, errorMessage, data);
    }

    public static <R> Result<R> newSuccess() {
        return newSuccess(null);
    }

    public int getErrorCode() {
        return this.errorCode;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public String getErrorDescription() {
        return errorDescription;
    }

    public void setErrorDescription(String errorDescription) {
        this.errorDescription = errorDescription;
    }

    public boolean isSuccess() {
        return ResultCode.SUCCESS.getErrorCode() == this.errorCode;
    }

    public T getData() {
        return this.data;
    }

    public Result(ResultCode resultCode, String errorMessage) {
        this.errorCode = resultCode.getErrorCode();
        this.errorMessage = resultCode.getErrorMessage();
        this.errorDescription = resultCode.getDescription() + "，原因：" + errorMessage;
    }
}
