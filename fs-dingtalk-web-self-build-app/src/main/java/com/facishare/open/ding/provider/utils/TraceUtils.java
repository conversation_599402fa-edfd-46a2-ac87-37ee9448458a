//package com.facishare.open.ding.provider.utils;
//
//import com.github.trace.TraceContext;
//import org.slf4j.MDC;
//
//public class TraceUtils {
//    public static void initTraceId(String traceId) {
//        TraceContext context = TraceContext.get();
//        context.setTraceId(traceId);
//        MDC.put("traceId", traceId);
//    }
//
//    public static String getTraceId() {
//        TraceContext context = TraceContext.get();
//        return context.getTraceId();
//    }
//}
