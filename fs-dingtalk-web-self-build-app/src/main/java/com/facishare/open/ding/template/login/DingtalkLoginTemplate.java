package com.facishare.open.ding.template.login;

import com.facishare.open.ding.template.model.FsUserModel;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.inner.login.LoginTemplate;
import com.facishare.open.outer.oa.connector.common.api.info.ticket.GenFsTicketModel;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class DingtalkLoginTemplate extends LoginTemplate {
    @Resource
    private LoginService loginService;

    @Override
    public void getOutUserInfoByCode(MethodContext context) {
        log.info("DingtalkLoginTemplate.getOutUserInfoByCode,context={}", context);

        //暂时不实现
        context.setResult(TemplateResult.newSuccess(null));
    }

    @Override
    public void genFsTicket(MethodContext context) {
        log.info("DingtalkLoginTemplate.genFsTicket,context={}", context);

        GenFsTicketModel fsTicketModel = context.getData();
        String corpId = fsTicketModel.getOutEa();
        String appId = fsTicketModel.getAppId();
        String fsEa = fsTicketModel.getFsEa();
        String outUserId = fsTicketModel.getOutUserId();

        Result<String> result = loginService.genFsTicket(corpId, appId, outUserId, fsEa);

        log.info("DingtalkLoginTemplate.genFsTicket, result={}", result);
        context.setResult(TemplateResult.newSuccess(result));
    }

    @Override
    public void getFsUserInfoByTicket(MethodContext context) {
        log.info("DingtalkLoginTemplate.getFsUserInfoByTicket,context={}", context);
        String ticket = context.getData().toString();

        Result<FsUserModel> result = loginService.getFsUser(ticket);
        log.info("DingtalkLoginTemplate.getFsUserInfoByTicket, result={}", result);

        context.setResult(TemplateResult.newSuccess(result));
    }

    @Override
    public void createUserToken(MethodContext context) {
        super.createUserToken(context);
        //sso token下线
//        log.info("DingtalkLoginTemplate.createUserToken,context={}", context);
//        CreateUserTokenDto.Argument userTokenArg = context.getData();
//        CreateUserTokenDto.Result result = ssoLoginService.createUserToken(userTokenArg);
//        log.info("DingtalkLoginTemplate.createUserToken,result={}", result);
//        context.setResult(TemplateResult.newSuccess(result));
    }
}
