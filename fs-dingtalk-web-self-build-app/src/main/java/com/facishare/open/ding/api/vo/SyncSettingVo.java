package com.facishare.open.ding.api.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/8/6 10:40
 */
@Data
public class SyncSettingVo implements Serializable{
    /** 纷享企业ei **/
    private Integer ei;

    /** 纷享员工id **/
    private Integer employeeId;

    /** 每页条数 **/
    private Integer pageSize;

    /** 页码 **/
    private Integer pageNumber;

    /** 操作类型(新增/修改/作废/上架/下架) **/
    private Integer operationType;

    /** 操作状态(0同步失败1同步成功2部分成功) **/
    private Integer operationStatus;

    /** 对象名称值 1：客户，2：销售订单，3： 联系人，4：纷享库存，5：开票申请，6：回款，7：产品，8：发货单，9：ERP仓库，-1：全部**/
    private Integer apiNameValue;

    /** 开始时间 **/
    private Long startTime;

    /** 结束时间 **/
    private Long endTime;

    /** 内容关键字 **/
    private String contentKey;

}
