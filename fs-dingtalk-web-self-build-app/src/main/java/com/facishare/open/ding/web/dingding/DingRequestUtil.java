package com.facishare.open.ding.web.dingding;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkSignatureUtil;
import com.dingtalk.api.response.OapiDepartmentListResponse;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.dingtalk.api.response.OapiUserGetResponse;
import com.dingtalk.api.response.OapiUserListResponse;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.handler.DingRetryHandler;
import com.facishare.open.ding.web.modle.HttpResponseMessage;
import com.facishare.open.ding.web.utils.HttpRequestUtils;
import com.facishare.open.ding.web.utils.HttpUtils;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.jexl3.JxltEngine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>获取token</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-08-19 14:45
 */
@Slf4j
public class DingRequestUtil {

    public static Map<String, String> token = new ConcurrentHashMap<>();

    public static Long TOKEN_ERROR_CODE = 40014L;

    public static Integer TOKEN_ERROR_CODE_2 = 40014;

    public static int DING_SUCCESS = 0;
    public static int DING_CALLBACK_REGIST = 71006;

    public static String appendUrl(String clientIp){
        String clientUrl = clientIp + "proxy/proxyRequest";
        return clientUrl;
    }

    public static String appendCallBackUrl(String clientIp){
        String clientUrl = clientIp + "proxy/registCallBack";
        return clientUrl;
    }

    public static String appendDetpListUrl(String clientIp){
        String clientUrl = clientIp + "proxy/queryDetpList";
        return clientUrl;
    }

    public static String appendDetpUsersUrl(String clientIp){
        String clientUrl = clientIp + "proxy/queryDetpUsers";
        return clientUrl;
    }

    public static String appendGetUsersUrl(String clientIp){
        String clientUrl = clientIp + "proxy/getUser";
        return clientUrl;
    }

    public static Object proxyRequest(String url, String argJson){
        CloseableHttpResponse response = HttpUtils.httpPost(url, argJson, null);
        log.info("proxyRequest,url={},response={}",url,response);
        if (Objects.isNull(response)){
            log.warn("proxy post failed,url={},argJson={}", url, argJson);
            return null;
        }
        Object entity = null;
        try {
            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (Exception e) {
                    log.warn("close response failed", e);
                }
            }
        }
        log.info("Invoke={}", entity);
        return entity;
    }



    public static String getToken(String clientIp, String appKey, String appSecret) throws RuntimeException {
        Gson gson = new Gson();
        String clientUrl = DingRequestUtil.appendUrl(clientIp);
        Map<String, Object> getTokenArg = new HashMap<>();
        getTokenArg.put("type","GET");
        String getTokenUrl = DingUrl.GET_TOKEN_URL.concat("?appkey=").concat(appKey).concat("&appsecret=").concat(appSecret);
        getTokenArg.put("url", getTokenUrl);
        Object tokenResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(getTokenArg));
        if (Objects.isNull(tokenResult)){
            log.warn("startConnect appKey或appSecret错误.");
            return null;
        }
        JSONObject tokenJson = gson.fromJson(tokenResult.toString(), JSONObject.class);
        if (tokenJson.get("errcode").equals(0)){
            log.warn("startConnect appKey或appSecret错误.");
            return null;
        }
        return tokenJson.getString("access_token");
    }

    /**
     * 获取token
     * @param appKey
     * @param appSecret
     * @return
     * @throws RuntimeException
     */
    public static Map<String, String> getProxyToken(String appKey, String appSecret) throws RuntimeException {
        String url = DingUrl.GET_TOKEN_URL + "?appkey=" + appKey + "&appsecret=" + appSecret;
        HttpResponseMessage response = DingRetryHandler.sendOkHttp3Get(url, new HashMap<>(), new HashMap<>());
        log.info("getProxyToken,url={},response={}",url,response);
        if (Objects.isNull(response)) {
            log.warn("getProxyToken GET failed,url={}",url);
            return null;
        }
        Object tokenResult = response.getContent();
        JSONObject jsonObject = JSONObject.parseObject(tokenResult.toString());
        if (Objects.isNull(tokenResult)) {
            log.warn("getProxyToken appKey或appSecret错误.url={}", url);
            return null;
        }
        if (!HttpRequestUtils.DING_SUCCESS.equals(jsonObject.get("errcode"))) {
            log.warn("getProxyToken appKey或appSecret错误.url={}", url);
            return null;
        }
        token.put(appKey+appSecret, jsonObject.getString("access_token"));
        return token;
    }


    /**
     * 查询部门列表
     * @param appKey
     * @param appSecret
     * @return
     */
    public static List<OapiDepartmentListResponse.Department> queryDeptList(String appKey, String appSecret,String id) {
        //查询所有部门
        String key = appKey + appSecret;
        String url = DingUrl.DEPT_LIST + "?access_token=" + token.get(key) + "&lang=zh_CN&fetch_child=true&id=" + id;
        HttpResponseMessage response = DingRetryHandler.sendOkHttp3Get(url, new HashMap<>(), new HashMap<>());
            Object tokenResult = response.getContent();
        log.info("queryDeptList,url={},response={}",url,response);
        JSONObject jsonObject = JSONObject.parseObject(tokenResult.toString());
        if (TOKEN_ERROR_CODE_2.equals(jsonObject.get("errcode"))){
            getProxyToken(appKey, appSecret);
            url = DingUrl.DEPT_LIST + "?access_token=" + token.get(key) + "&lang=zh_CN&fetch_child=true&id=" + id;
            response = DingRetryHandler.sendOkHttp3Get(url, new HashMap<>(), new HashMap<>());
            tokenResult = response.getContent();
            jsonObject = JSONObject.parseObject(tokenResult.toString());
        }
        String result = jsonObject.getString("department");
        if(StringUtils.isEmpty(result)) {
            return null;
        }
        List<OapiDepartmentListResponse.Department> departments = JSONArray.parseArray(result, OapiDepartmentListResponse.Department.class);
        return departments;
    }

    /**
     * 分页查询钉钉某部门下员工列表
     * @param deptId
     * @param appKey
     * @param appSecret
     * @return
     */
    public static List<OapiUserListResponse.Userlist> queryDeptUser(Long deptId, String appKey, String appSecret){
        try {
            Long offset = 0L;
            Long size = 100L;
            List<OapiUserListResponse.Userlist> list = new ArrayList<>();
            HttpResponseMessage response = null;
            JSONObject jsonObject = null;
            String key = appKey + appSecret;
            do {
                String url = DingUrl.USER_LIST + "?access_token=" + token.get(key) + "&lang=zh_CN&department_id=" + deptId + "&offset=" + offset + "&size=" + size + "&order=entry_desc";
                response = DingRetryHandler.sendOkHttp3Get(url, new HashMap<>(), new HashMap<>());
                log.info("queryDeptUser,url={},response={}",url,response);
                Object tokenResult = response.getContent();
                jsonObject = JSONObject.parseObject(tokenResult.toString());
                if (TOKEN_ERROR_CODE_2.equals(jsonObject.get("errcode"))){
                    getProxyToken(appKey, appSecret);
                    url = DingUrl.USER_LIST + "?access_token=" + token.get(key) + "&lang=zh_CN&department_id=" + deptId + "&offset=" + offset + "&size=" + size + "&order=entry_desc";
                    response = DingRetryHandler.sendOkHttp3Get(url, new HashMap<>(), new HashMap<>());
                    tokenResult = response.getContent();
                    jsonObject = JSONObject.parseObject(tokenResult.toString());
                }
                if (DING_SUCCESS == Integer.parseInt(jsonObject.get("errcode").toString())){
                    String userResult = JSONArray.toJSONString(jsonObject.get("userlist"));
                    List<OapiUserListResponse.Userlist> userlists = JSONArray.parseArray(userResult, OapiUserListResponse.Userlist.class);
                    list.addAll(userlists);
                }
                offset += 1;
            }while (Boolean.parseBoolean(jsonObject.get("hasMore").toString()));
            return list;
        } catch (JxltEngine.Exception e) {
            log.warn("queryUserList failed", e);
            return null;
        }
    }

    public static OapiUserGetResponse getUser(String appKey, String appSecret, String userId) {
        try {
            HttpResponseMessage response = null;
            JSONObject jsonObject = null;
            String key = appKey + appSecret;
            String url = DingUrl.GET_USER + "?access_token=" + token.get(key) + "&userid=" + userId;
            response = DingRetryHandler.sendOkHttp3Get(url, new HashMap<>(), new HashMap<>());
            log.info("getUser,url={},response={}",url,response);
            Object tokenResult = response.getContent();
            jsonObject = JSONObject.parseObject(tokenResult.toString());
            if (TOKEN_ERROR_CODE_2.equals(jsonObject.get("errcode"))){
                getProxyToken(appKey, appSecret);
                url = DingUrl.GET_USER + "?access_token=" + token.get(key) + "&userid=" + userId;
                response = DingRetryHandler.sendOkHttp3Get(url, new HashMap<>(), new HashMap<>());
                tokenResult = response.getContent();
                jsonObject = JSONObject.parseObject(tokenResult.toString());
            }
            if (DING_SUCCESS != Integer.parseInt(jsonObject.get("errcode").toString())){
                return null;
            }
            OapiUserGetResponse userGetResponse = JSON.parseObject(jsonObject.toString(), OapiUserGetResponse.class);
            return userGetResponse;
        }catch (JxltEngine.Exception e){
            log.warn("getUser failed", e);
            return null;
        }
    }


    public static OapiSnsGetuserinfoBycodeResponse getUserByCode(String redirectAppId, String redirectAppSecret, String code) {
//        DefaultDingTalkClient client = new DefaultDingTalkClient(DingUrl.GET_USER_URL);
//        OapiSnsGetuserinfoBycodeRequest req = new OapiSnsGetuserinfoBycodeRequest();
//        req.setTmpAuthCode(code);
//        OapiSnsGetuserinfoBycodeResponse response = null;
//        try {
//            log.info("getUserByCode request id:{},code:{}", UUID.randomUUID().toString(),code);
//            response = client.execute(req, redirectAppId, redirectAppSecret);
//        } catch (ApiException e) {
//            log.warn("authorize failed！", e);
//            return null;
//        }
//        return response;

        try {
            log.info("getUserByCode,redirectAppId={},redirectAppSecret={},code={}",redirectAppId,redirectAppSecret,code);
            HttpResponseMessage response = null;
            Long timestamp = System.currentTimeMillis();
            //String message = timestamp + "\n" + redirectAppSecret;

            String canonicalString = DingTalkSignatureUtil.getCanonicalStringForIsv(timestamp, null);
            String signature = DingTalkSignatureUtil.computeSignature(redirectAppSecret, canonicalString);
            String encoded = null;
            try {
                encoded = URLEncoder.encode(signature, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            String urlEncodeSignature = encoded.replace("+", "%20").replace("*", "%2A").replace("~", "%7E").replace("/", "%2F");

            //String signature = getSignature(redirectAppSecret,message);
            //signature = URLEncoder.encode(signature);
            log.info("getUserByCode,signature={},code={}",urlEncodeSignature,code);

            String url = DingUrl.GET_USER_URL + "?accessKey=" + redirectAppId + "&timestamp=" + timestamp + "&signature="+urlEncodeSignature;
            Map<String,Object> paramsMap = new HashMap<>();
            paramsMap.put("tmp_auth_code",code);
            log.info("getUserByCode,url={},paramsMap={}",url,paramsMap);
            response = DingRetryHandler.sendOkHttp3Post(url, new HashMap<>(), JSONObject.toJSONString(paramsMap));
            log.info("getUserByCode,url={},response={}",url,response);
            String content = response.getContent();
            OapiSnsGetuserinfoBycodeResponse result = JSONObject.parseObject(content,OapiSnsGetuserinfoBycodeResponse.class);
            return result;
        }catch (JxltEngine.Exception e){
            log.warn("getUserByCode failed", e);
            return null;
        }
    }

    public static String getSignature(String appSecret, String message) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(appSecret.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signatureBytes = mac.doFinal(message.getBytes("UTF-8"));
            String signature = new String(org.apache.commons.codec.binary.Base64.encodeBase64(signatureBytes));
            if("".equals(signature)) {
                return "";
            }
            String encoded = URLEncoder.encode(signature, "UTF-8");
            String urlEncodeSignature = encoded.replace("+", "%20").replace("*", "%2A").replace("~", "%7E").replace("/", "%2F");
            log.info("getSignature,urlEncodeSignature={}",urlEncodeSignature);
            return urlEncodeSignature;
        } catch (Exception e) {
            log.info("getSignature,exception",e);
        }
        return null;
    }

    public static Result<EmployeeDingVo> getUserByMe(DingEnterpriseResult dingEnterpriseResult, String accessToken) {
        String proxyUrl = com.facishare.open.ding.provider.dingding.DingRequestUtil.appendProxyHttpUrl(dingEnterpriseResult.getClientIp());
        String getMeUrl = "https://api.dingtalk.com/v1.0/contact/users/me";
        Map<String, String> hearsMap = Maps.newHashMap();
        Map<String, Object> messageArg = new HashMap<>();
        messageArg.put("url", getMeUrl);//钉钉发送消息的url
        messageArg.put("type", "GET");
        messageArg.put("token", dingEnterpriseResult.getToken());
        hearsMap.put("x-acs-dingtalk-access-token", accessToken);
        messageArg.put("header",JSONObject.toJSONString(hearsMap));
        CloseableHttpResponse response = com.facishare.open.ding.common.utils.HttpUtils.httpPost(proxyUrl, JSONObject.toJSONString(messageArg), hearsMap);
        String entity = null;
        try {
            if (Objects.isNull(response)) {
                log.warn("getUserByMe failed,response is null");
                return Result.newError(ResultCode.GET_DING_EMP_FAILED);
            }
            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
            log.info("callBackController entity :{}", entity);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (Exception e) {
                    log.warn("close response failed", e);
                }
            }
        }

        Map<String, String> personMap = JSONObject.parseObject(entity, new TypeReference<Map<String, String>>() {
        });
        String openId = personMap.get("openId");

        if (org.apache.commons.lang3.ObjectUtils.isEmpty(openId)) {
            String message = personMap.get("message");
            return Result.newError(ResultCode.GET_DING_EMP_FAILED.getErrorCode(), message);
        }
        EmployeeDingVo employeeDingVo = new EmployeeDingVo();
        employeeDingVo.setMobile(personMap.get("mobile"));
        employeeDingVo.setName(personMap.get("nick"));
        employeeDingVo.setUnionid(personMap.get("unionId"));
        employeeDingVo.setUserid(personMap.get("openId"));
        log.info("person userInfo:{}", employeeDingVo);
        return Result.newSuccess(employeeDingVo);
    }

    public static Result< Map<String, String>> getUserToken(DingEnterpriseResult dingEnterpriseResult, String appKey, String appSecret,String code) {
        Map<String, String> argMap = new HashMap<>();
        String tokenUrl = "https://api.dingtalk.com/v1.0/oauth2/userAccessToken";
        argMap.put("clientId", appKey);
        argMap.put("clientSecret", appSecret);
        argMap.put("code", code);
        argMap.put("grantType", "authorization_code");
        Map<String, Object> messageArg = new HashMap<>();
        messageArg.put("url", tokenUrl);//钉钉发送消息的url
        messageArg.put("type", "POST");
        messageArg.put("token", dingEnterpriseResult.getToken());
        messageArg.put("data", argMap);
        //获取用户信息
        String clientUrl = com.facishare.open.ding.provider.dingding.DingRequestUtil.appendProxyHttpUrl(dingEnterpriseResult.getClientIp());

        CloseableHttpResponse response = com.facishare.open.ding.common.utils.HttpUtils.httpPost(clientUrl, JSONObject.toJSONString(messageArg), null);
        String entity = null;
        try {
            if (Objects.isNull(response)) {
                log.warn("getUserToken failed,response is null");
                return Result.newError(ResultCode.GET_DING_EMP_FAILED);
            }
            // {"errorCode":200,"errorMessage":"","data":"{\"expireIn\":7200,\"accessToken\":\"09754d30833630beab93993881bd3cfe\",\"refreshTo
            //ken\":\"be449e2717dd3544962c17e4dcc980ef\"}"}
            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
            log.info("callBackController entity :{}", entity);
        } catch (Exception e) {
            log.warn("getUserToken failed", e);
            return Result.newError(ResultCode.DING_CALL_BACK_URL_EXIST.getErrorCode(), response.getEntity().toString());
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (Exception e) {
                    log.warn("close response failed", e);
                }
            }
        }

        JSONObject authorizeResult = JSONObject.parseObject(entity);

        if (authorizeResult.getInteger("errorCode") != 200) {
            return Result.newError(ResultCode.DING_CALL_BACK_URL_EXIST.getErrorCode(), authorizeResult.getString("errorMessage"));
        }

        final Map<String, String> data = JSONObject.parseObject(authorizeResult.getString("data"), new TypeReference<Map<String, String>>() {
        });

        return Result.newSuccess(data);
    }
}
