package com.facishare.open.ding.web.aop;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-9-18
 * 国际化AOP
 */
@Slf4j
@Aspect
@Order(1)
@Component
public class ControllerI18NAspect {
    @Resource
    private I18NStringManager i18NStringManager;

    @Around("execution(* com.facishare.open.ding.web..*.*(..))")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();

        //获取前端当前语言，只能使用这个key，不能从cookie里面获取，因为这个request的已经被CEP改变
        String lang = request.getHeader("x-fs-locale");
        if (StringUtils.isEmpty(lang)) {
            if (Objects.nonNull(request.getHeader("Accept-Language"))) {
                lang = request.getLocale().toLanguageTag();
            }
        }
        String tenantId = request.getHeader("x-fs-ei");
        if (StringUtils.isEmpty(tenantId)) {
            // push 接口使用的企业header
            tenantId = request.getHeader("tenantId");
            if (StringUtils.isEmpty(tenantId)) {
                tenantId = request.getParameter("tenantId");
            }
        }

        Object result = joinPoint.proceed();
        log.trace("ControllerI18NAspect.around,result={}", result);
        if (result != null) {
           Result result2 = null;
            if(result instanceof Result) {
                result2 = (Result) result;
            }
            log.trace("ControllerI18NAspect.around,result2={}", result);
            if(result2!=null) {
                if(result2.getErrorCode()== ResultCode.SUCCESS.getErrorCode()) {
                    if(!StringUtils.equalsIgnoreCase(result2.getErrorDescription(),ResultCode.SUCCESS.getDescription())) {
                        log.trace("ControllerI18NAspect.around,result2,success error msg changed,result={}", result);
                        result2.setI18nKey(null);
                        result2.setI18nExtra(null);
                        return result;
                    }
                }
                if(StringUtils.isNotEmpty(result2.getI18nKey())) {
                    result2.setErrorMessage(i18NStringManager.get2(result2.getI18nKey(), lang,tenantId, result2.getErrorDescription(),result2.getI18nExtra()));
                    result2.setI18nKey(null);
                    result2.setI18nExtra(null);
                }
                log.trace("ControllerI18NAspect.around,end,result2={}", result2);
            }
        }
        log.trace("ControllerI18NAspect.around,end,result={}", result);
        return result;
    }

}
