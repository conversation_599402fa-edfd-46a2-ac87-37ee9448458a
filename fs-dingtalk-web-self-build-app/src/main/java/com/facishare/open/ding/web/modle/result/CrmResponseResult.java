package com.facishare.open.ding.web.modle.result;

import com.facishare.open.ding.web.utils.HttpRequestUtils;
import lombok.Data;

/**
 * <p>纷享SFA接口返回结果封装类</p>
 * @dateTime 2018/7/13 14:50
 * <AUTHOR> <EMAIL>
 * @version 1.0
 */
@Data
public class CrmResponseResult<T> {

    /** 返回码 **/
    private Integer errorCode;

    /** 返回描叙 **/
    private String errorMessage;

    /** 实际数据 **/
    private T data;

    public boolean isSuccess() {
        if (HttpRequestUtils.CRM_SUCCESS == errorCode) {
            return true;
        }
        return false;
    }

    public boolean isNeedRetry() {
        if (HttpRequestUtils.CONNECTION_FAILED == errorCode || HttpRequestUtils.CONNECTION_TIMEOUT == errorCode) {
            return true;
        }
        return false;
    }

}
