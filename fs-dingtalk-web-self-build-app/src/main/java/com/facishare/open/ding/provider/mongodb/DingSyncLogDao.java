package com.facishare.open.ding.provider.mongodb;

import com.facishare.open.ding.api.enums.OperationTypeEnum;
import com.facishare.open.ding.api.enums.SyncLogStatusEnum;
import com.facishare.open.ding.provider.mongodb.common.MyBasicMongodbDao;
import com.facishare.open.ding.provider.mongodb.entity.DingSyncLogDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Key;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/23 14:11
 * @<NAME_EMAIL>
 * @version 1.0 
 */
@Slf4j
@Repository
public class DingSyncLogDao extends MyBasicMongodbDao<DingSyncLogDO> {

    @Override
    protected Class<DingSyncLogDO> getEntityClazz() {
        return DingSyncLogDO.class;
    }

    public Key<DingSyncLogDO> saveDingSyncLogDO(DingSyncLogDO dingSyncLogDO) {
        return this.mongoDBTemplate.save(dingSyncLogDO);
    }

    public DingSyncLogDO findById(ObjectId objId) {
        Query<DingSyncLogDO> query = createQuery();
        query.field("_id").equal(objId);
        return this.mongoDBTemplate.findOne(query);
    }

    public DingSyncLogDO findByEI(int ei) {
        Query<DingSyncLogDO> query = createQuery();
        query.field("ei").equal(ei);
        return this.mongoDBTemplate.findOne(query);
    }

    public List<DingSyncLogDO> queryDingSyncLogDOPage(int ei, Integer operationType, Integer operationStatus, int pageSize, int pageNo,
                                                    String apiName, String contentKey, Date startDate, Date endDate) {
        Query<DingSyncLogDO> query = this.mongoDBTemplate.createQuery(DingSyncLogDO.class);
        if(Objects.isNull(ei)){
            log.warn("queryKcSyncLogDOPage param error, ei is null.");
            return Lists.newArrayList();
        }
        query.field("ei").equal(ei);
        generalQuery(query,operationType,operationStatus,apiName,contentKey,startDate,endDate);
		query.order("-createTime");						// 查询结果按时间倒序排序
        query.offset((pageNo -1) * pageSize);
        query.limit(pageSize);
        return query.asList();
    }

    public Integer queryTotalCount(int ei, Integer operationType, Integer operationStatus,String apiName,String contentKey,Date startDate,Date endDate){
        Query<DingSyncLogDO> query = this.mongoDBTemplate.createQuery(DingSyncLogDO.class);
        if(Objects.isNull(ei)){
            log.warn("queryKcSyncLogDOPage param error, ei is null.");
            return 0;
        }
        query.field("ei").equal(ei);
        generalQuery(query,operationType,operationStatus,apiName,contentKey,startDate,endDate);
        int count = (int) query.countAll();
        return count;
    }

    public void generalQuery(Query<DingSyncLogDO> query, Integer operationType, Integer operationStatus, String apiName, String contentKey, Date startDate, Date endDate){
        if(Objects.nonNull(operationType)){
            query.field("operationType").equal(operationType);
        }
        else{
            query.field("operationType").notEqual(OperationTypeEnum.UNDO.getType());
        }
        if(Objects.nonNull(operationStatus)&&operationStatus>=0){
            query.field("operationStatus").equal(operationStatus);
        }
        if(Objects.nonNull(apiName)){
            query.field("apiName").equal(apiName);
        }
        if(StringUtils.isNotEmpty(contentKey)){
            query.field("operationDetails").contains(contentKey);
        }
        if(Objects.nonNull(startDate)){

            query.filter("updateTime >=",startDate);
        }
        if(Objects.nonNull(endDate)){

            query.filter("updateTime <=",endDate);
        }
        query.field("status").notEqual(SyncLogStatusEnum.ROOT.getStatus());
    }

}
