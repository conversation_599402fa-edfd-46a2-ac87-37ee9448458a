package com.facishare.open.ding.provider.arg;

import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/12/26 11:13
 * @desc
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SendTextCardContextArg extends MessageDataBaseArg implements Serializable {
    private SendTextCardMessageArg sendTextCardMessageArg;


}
