package com.facishare.open.ding.provider.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.enums.*;
import com.facishare.open.ding.api.model.FsUserInfoModel;
import com.facishare.open.ding.api.result.*;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.RedisDingService;
import com.facishare.open.ding.api.vo.*;
import com.facishare.open.ding.common.model.*;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.constants.Constant;
import com.facishare.open.ding.provider.crm.CrmRestManager;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.facishare.open.ding.provider.entity.DingSyncApi;
import com.facishare.open.ding.provider.entity.LogWriteVo;
import com.facishare.open.ding.provider.handler.QPSLimitHandlerFromProvider;
import com.facishare.open.ding.provider.manager.*;
import com.facishare.open.ding.provider.mongodb.entity.DingSyncLogDO;
import com.facishare.open.ding.provider.task.executor.DeleteEmployeeTask;
import com.facishare.open.ding.provider.task.executor.InsertEmployeeTask;
import com.facishare.open.ding.provider.task.executor.UpdateEmployeeTask;
import com.facishare.open.ding.provider.task.executor.againMappingTask;
import com.facishare.open.ding.provider.utils.*;
import com.facishare.open.ding.web.redis.RedisDataSource;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.RemoveEmployeeEventType;
import com.facishare.open.outer.oa.connector.common.api.object.DingTalkEmployeeObject;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.organization.adapter.api.config.model.GetConfigDto;
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
import com.facishare.organization.adapter.api.model.EnterpriseConfigKey;
import com.facishare.organization.adapter.api.model.biz.employee.MobileStatus;
import com.facishare.organization.adapter.api.model.biz.employee.ModifyEmployee;
import com.facishare.organization.adapter.api.model.biz.employee.arg.CreateEmployeeArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.CreateEmployeeResult;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.exception.OrganizationException;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.arg.GetAllEmployeeIdsArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.model.employee.result.GetAllEmployeeIdsResult;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.restful.common.StopWatch;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.IncrementUpdateResult;
import com.google.common.collect.Lists;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <p>类的详细说明</p>
 *
 * @version 1.0
 * @dateTime 2018/7/12 10:58
 */
@Slf4j
@Service("objectMappingService")
// IgnoreI18nFile
public class ObjectMappingServiceImpl implements ObjectMappingService {
    private static final Integer ALLBINDSTATUS = 0;

    private static final Integer CURRENT_EMPLOYEE_ID = -9;

    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Autowired
    private DingMappingEmployeeManager dingMappingEmployeeManager;

    @Autowired
    private EmployeeProviderService employeeProviderService;

    @Autowired
    private EmployeeService employeeAdapterService;

    @Autowired
    private EnterpriseConfigService enterpriseConfigService;

    @Autowired
    private DingSyncLogManager dingSyncLogManager;
//    @Autowired
//    private CircleService circleService;

    @Autowired
    private DingDeptMananger dingDeptMananger;

    @Autowired
    private RedisDingService redisDingService;

    @Autowired
    private CrmRestManager crmRestManager;

    @Autowired
    private LogManager logManager;
    @Autowired
    private RedisDataSource redisDataSource;

    @Autowired
    private QPSLimitHandlerFromProvider qpsLimitHandlerFromProvider;

    @Resource
    private EIEAConverter eieaConverter;

    @Autowired
    private TokenManager tokenManager;
    @Autowired
    private ObjectDataManager objectDataManager;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;

    private static final Integer OPERATOR = -10000;

    private static final String EMP_OBJ = "EmpObj";
    private static final String DEP_OBJ = "DepObj";

    private static final String Symbol = "__";
    private String NOT_DISTRIBUTION="999998";
    private String APP_EMP_LOCK="%s_%s";

    private static ExecutorService executorService = new ThreadPoolExecutor(10, 10, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());
    @Autowired
    private OuterOaDeptDataManager outerOaDeptDataManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;


    @Override
    public Result<Map<String, Object>> queryMappingEmployee(BindEmpVo vo, Integer ei, String appId) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("totalCount", dingMappingEmployeeManager.getEmpTotalCount(ei, vo.getBindStatus(), vo.getDingEmployeeName(), vo.getDingEmployeePhone(), appId));
        dataMap.put("dataList", dingMappingEmployeeManager.queryEmpsBindByEi(ei, appId, vo.getBindStatus(), vo.getPageNumber(), vo.getPageSize(), vo.getDingEmployeeName(), vo.getDingEmployeePhone(), null).getData());

        return Result.newSuccess(dataMap);
    }

    @Override
    public Result<Map<String, Object>> queryNewEmployee(BindEmpVo vo, Integer ei, String ea, Integer userId, String appId) {
        log.info("queryNewEmployee,ei={},vo={}",ei,vo);
        //两种模式 第一种模式是没有员工时去拉取员工，第二种模式是部门表为空的时候 去拉取员工
        Result<DingEnterpriseResult> dingEnterpriseResultResult = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        log.info("queryNewEmployee,dingEnterpriseResultResult={}",dingEnterpriseResultResult);
        if (dingEnterpriseResultResult.getData() != null) {
            if (dingEnterpriseResultResult.getData().getDevModel().equals(1)) {
                Integer allCount = dingMappingEmployeeManager.getEmpAllCount(ei, appId);
                log.info("queryNewEmployee,allCount={}",allCount);
                if (Objects.nonNull(allCount) && allCount > 0) {
                    Result<Map<String, Object>> result = queryMappingEmployee(vo, ei, appId);
                    return result;
                }
                String traceId = TraceUtils.getTraceId();
                log.info("queryNewEmployee,traceId={}",traceId);
                Thread thread = new Thread() {
                    public void run() {
                        TraceUtils.initTraceId(traceId);
                        initDingEmployee(ei, userId, appId);
                    }
                };
                thread.start();
                return Result.newError(ResultCode.INIT_DING_EMP);
            } else {
                Integer deptCount = dingDeptMananger.countByEi(ei, appId);
                log.info("queryNewEmployee,deptCount={}",deptCount);
                if (Objects.nonNull(deptCount) && deptCount > 0) {
                    Result<Map<String, Object>> result = queryMappingEmployee(vo, ei, appId);
                    return result;
                }
                String traceId = TraceUtils.getTraceId();
                log.info("queryNewEmployee,traceId2={}",traceId);
                executorService.execute(new Runnable() {
                    @Override
                    public void run() {
                        TraceUtils.initTraceId(traceId);
                        initModelEmployee(ei, userId, ea, appId);
                    }
                });
                return Result.newError(ResultCode.INIT_DING_EMP);
            }
        }
        return Result.newError(ResultCode.INIT_DING_EMP);

    }

    @Override
    public Result<Map<String, Object>> syncPullOrganizationData(Integer ei, String ea, Integer userId, String appId) {
        initModelEmployee(ei, userId, ea, appId);
        return Result.newError(ResultCode.ALL_PULL_ORGANIZATION_ING);
    }


    @Override
    public Result<Boolean> syncNewEmployee(Integer ei, Integer userId, String appId) {
        //判断该企业的回调地址是否在使用
        Result<DingEnterpriseResult> dingEnterpriseResultResult = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        if (dingEnterpriseResultResult.getData().getIsCallback() == Constant.IS_NOT_CALLBACK) {
            Thread thread = new Thread() {
                public void run() {
                    allPullEmployee(ei, userId, appId);
                }
            };
            thread.start();
            return Result.newSuccess(Boolean.TRUE);
        }
        return Result.newSuccess(Boolean.FALSE);
    }

    //模式二拉取员工
    @Override
    public Result initModelEmployee(Integer ei, Integer userId, String ea, String appId) {
        StopWatch stopWatch = StopWatch.create("initModelEmployee");

        log.info("initModelEmployee,start,ei={}", ei);
        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        log.info("initModelEmployee,mappingEnterprise={}", mappingEnterprise);
        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }

        String appSecret = mappingEnterprise.getData().getAppSecret();
        //查询部门列表
        ScopeVo scopeVo = DingRequestUtil.queryScoreDeptEmployee(ei, mappingEnterprise.getData().getClientIp(),
                appId, appSecret, mappingEnterprise.getData().getToken(), tokenManager.getToken(ei, appId));
        log.info("initModelEmployee,scopeVo={}", scopeVo);
        if (scopeVo == null) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return Result.newError(ResultCode.DING_CONNECT_PARAM_ERROR);
        }
        if (CollectionUtils.isEmpty(scopeVo.getAuthedDept())) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return Result.newError(ResultCode.DEPT_LIST_ERROR);
        }
        //解析出部门，查询部门信息
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedDept())) {
            List<Dept> finalDetailList = new ArrayList<>();
            //如果scopeVo的部门为1.说明权限是全部门需要遍历全部部门
            List<Dept> depts = new ArrayList<>();
            List<Dept> detailList = Lists.newArrayList();
            if (scopeVo.getAuthedDept().get(0) == 1) {
                detailList = DingRequestUtil.queryDeptList(ei, mappingEnterprise.getData().getClientIp(),
                        appId, appSecret, mappingEnterprise.getData().getToken(), "1");
                log.info("initModelEmployee,detailList={}", detailList);
                if (CollectionUtils.isEmpty(detailList)) {
                    //理论上不存在没有子部门的企业，这里先记录下
                    log.info("query dept list failed, ei={}, deptResponse={}.", ei, detailList);
                    return Result.newError(ResultCode.DEPT_LIST_ERROR);
                }
                //有些企业的人员会直接在根部门下，也需要找出来
                //先注释，会导致中间表数据被删
//                Dept deptDetail = DingRequestUtil.queryDeptDetail(mappingEnterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), mappingEnterprise.getData().getToken(), 1L);
//                finalDetailList.add(deptDetail);
            } else {
                for (int i = 0; i < scopeVo.getAuthedDept().size(); i++) {
                    List<Long> deptIdList = new ArrayList<>();
                    //先把父级部门查询出来
                    List<Dept> deptItem = DingRequestUtil.queryDeptList(ei, mappingEnterprise.getData().getClientIp(),
                            appId, appSecret, mappingEnterprise.getData().getToken(), scopeVo.getAuthedDept().get(i).toString());
                    log.info("initModelEmployee,deptItem={}", deptItem);
                    //有些企业对接只授权最基层部门，这里返回会为空
                    if(CollectionUtils.isNotEmpty(deptItem)) {
                        List<Long> collect = deptItem.stream().map(Dept::getId)
                                .collect(Collectors.toList());
                        deptIdList.addAll(collect);
                        depts.addAll(deptItem);
                    }
                    deptIdList.add(scopeVo.getAuthedDept().get(i));
                    for (int j = 0; j < deptIdList.size(); j++) {
                        Dept deptDetail = DingRequestUtil.queryDeptDetail(ei, mappingEnterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), mappingEnterprise.getData().getToken(), deptIdList.get(j));
                        log.info("initModelEmployee,deptDetail={}", deptDetail);
                        detailList.add(deptDetail);
                    }
                }
                List<Long> scopeCollect = detailList.stream().map(Dept::getId)
                        .collect(Collectors.toList());
                scopeVo.setAuthedDept(scopeCollect);
            }
            //创建部门
            finalDetailList.addAll(detailList);
            //同步组织架构
            recallInitDept(detailList, ei, ea, appId);
            //查询部门下所有员工，另起线程同步员工数量
            log.info("finalDetail size:{}", finalDetailList.size());
            for (int i = 0; i < finalDetailList.size(); i++) {
                //查询部门信息
                Dept dept = finalDetailList.get(i);
                insertMappingEmp(dept, ei, mappingEnterprise.getData().getClientIp(), Long.valueOf(mappingEnterprise.getData().getAgentId()), appId);
            }
            //添加部门负责人
            log.info("createDeptOwner starting");
            createDeptOwner(mappingEnterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), mappingEnterprise.getData().getToken(), ei, appId);
        }

        //如果选择是部分员工，需要同步员工
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedUser())) {
            List<User> differEmployeeList = Lists.newArrayList();
            scopeVo.getAuthedUser().parallelStream().filter(item -> !isExistData(ei, item, appId)).forEach(item -> {
                //添加未加入的员工
                User user = DingRequestUtil.getUser(ei, mappingEnterprise.getData().getClientIp(),
                        appId, appSecret, item, mappingEnterprise.getData().getToken(), tokenManager.getToken(ei, appId), Long.valueOf(mappingEnterprise.getData().getAgentId()));
                log.info("initModelEmployee,user={}", user);
                if(ObjectUtils.isNotEmpty(user)
                        && StringUtils.isNotEmpty(user.getMobile())
                        && StringUtils.isNotEmpty(user.getStateCode())
                        && !user.getStateCode().equals("86")) {
                    String phone = "+" + user.getStateCode() + "-" + user.getMobile();
                    user.setMobile(phone);
                }
                differEmployeeList.add(user);
            });
            log.info("initModelEmployee,differEmployeeList={}", differEmployeeList);
            Integer count = dingMappingEmployeeManager.initMappingEmployee(differEmployeeList, ei, null, StringUtils.EMPTY, appId);
        }

        stopWatch.lap("createEmployee");
        stopWatch.log();

        return Result.newSuccess();
    }

    @Override
    public Result<Integer> insertMappingEmp(Dept dept, Integer ei, String clientIp, Long agentId, String appId) {
        //查询部门信息
        Long deptId = dept.getId();
        String deptName = dept.getName();
        log.info("trace allPullEmployee size,deptIndex:{},deptId:{}", dept, dept.getId());
        UserDetailVo userDetailVo = DingRequestUtil.queryUserByDeptId(ei, clientIp, tokenManager.getToken(ei, appId), deptId.intValue(), agentId);

        if (ObjectUtils.isEmpty(userDetailVo) || CollectionUtils.isEmpty(userDetailVo.getUserlist())) {
            log.info("this dept no user, ei={},deptId={}.", ei, dept);
            return Result.newSuccess();
        }
        List<User> deptUsers = Lists.newArrayList();
        userDetailVo.getUserlist().stream().forEach(item -> {
            User user = new User();
            BeanUtils.copyProperties(item, user);
            log.info("item :{},user:{}", item, user);
            if(StringUtils.isNotEmpty(item.getMobile())
                    && StringUtils.isNotEmpty(item.getStateCode())
                    && !item.getStateCode().equals("86")) {
                String phone = "+" + item.getStateCode() + "-" + item.getMobile();
                user.setMobile(phone);
            }
            deptUsers.add(user);
        });

        //保存钉钉员工信息
        log.info("this dept  user={}, ei={},deptId={}.,userSize:{}", deptUsers, ei, dept, deptUsers.size());
        Integer count = dingMappingEmployeeManager.initMappingEmployee(deptUsers, ei, deptId, deptName, appId);
        if (Objects.nonNull(count) && count.equals(deptUsers.size())) {
            log.info("初始化员工成功" + count + "条");
        } else {
            log.warn("初始化员工失败,员工可能已经存在,deptId={},deptName={}.", deptId, deptName);
        }
        return Result.newSuccess(count);
    }


    //解析部门信息，返回出主管列表设置部门负责人

    @Override
    public Result<Void> allPullEmployeeInsert(Integer ei, Integer userId, String ea, String appId) {
        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        log.info("allPullEmployeeInsert,mappingEnterprise={}",mappingEnterprise);
        List<Dept> detailList = requestDingAllDept(ei, userId, ea, appId);
        //插入员工
        log.info("trace allPullEmployee size:{}", detailList.size());

        for (int i = 0; i < detailList.size(); i++) {
            //查询部门信息
            Dept dept = detailList.get(i);
            insertMappingEmp(dept, ei, mappingEnterprise.getData().getClientIp(), null, appId);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> upsertAllDept(Integer ei, Integer userId, String ea, String appId) {
        //读取部门信息
        List<Dept> allDingDept = requestDingAllDept(ei, userId, ea, appId);
        //转换成map结构
        Map<Long, Dept> DingDeptMap = allDingDept.stream().collect(Collectors.toMap(Dept::getId, Function.identity(), (key1, key2) -> key2));
        List<DeptVo> deptTable = dingDeptMananger.getDeptByEI(ei, appId);
        Map<Long, DeptVo> tableMap = deptTable.stream().collect(Collectors.toMap(DeptVo::getDingDeptId, Function.identity(), (key1, key2) -> key2));

        allDingDept.stream().forEach(item -> {
            //如果是中间表没有的映射关系，则新建
            if (ObjectUtils.isEmpty(tableMap.get(item.getId()))) {
                supportAddDept(ei, ea, item, DingDeptMap, tableMap, appId);
            } else {
                //如果是中间表存在的映射关系，则更新
                executorService.execute(new Runnable() {
                    @Override
                    public void run() {
                        supportUpdateDept(ei, ea, item, DingDeptMap, tableMap, appId);
                    }
                });
//               supportUpdateDept(ei,ea,item,DingDeptMap,tableMap);
            }
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Integer> deleteEmpByDingId(Integer ei, String dingEmp, String appId) {
        Integer count = dingMappingEmployeeManager.relieveBind(ei, dingEmp, appId);
        return Result.newSuccess(count);
    }

    private void supportAddDept(Integer ei, String ea, Dept dept, Map<Long, Dept> DingDeptMap, Map<Long, DeptVo> tableMap, String appId) {
        //直接调用接口请求钉钉部门的所有的上级
        log.info("重新映射新增钉钉部门 ea:{},dept:{}", ea, dept);
        DingEnterpriseResult enterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId).getData();
        List<Long> listParent = DingRequestUtil.getListParent(ei, enterprise.getClientIp(), dept.getId(), tokenManager.getToken(ei, appId));
        if (ObjectUtils.isEmpty(listParent)) {
            log.warn("钉钉部门上级部门为空,dept:{}", dept);
            return;
        }
        Collections.reverse(listParent);
        //找出没有创建的上级部门并创建
        LinkedList<Dept> depts = Lists.newLinkedList();
        listParent.stream().forEach(item -> {
            DeptVo temp = tableMap.get(item.longValue());
            if (ObjectUtils.isNotEmpty(temp)) {
                return;
            }
            // 检查是否有其他appId已经映射了
            final String outDeptId = String.valueOf(item);
            final String fsDeptId = outerOaDepartmentBindManager.getFsDeptIdAndAddByEaAndOutDeptId(ChannelEnum.dingding, ea, appId, outDeptId);
            if (StringUtils.isNotEmpty(fsDeptId)) {
                // 写入绑定表
                tableMap.put(item, dingDeptMananger.queryByDingId(ei, item, appId));
                return;
            }
            depts.add(DingDeptMap.get(item));
        });
        log.info("重新映射新增钉钉部门 ea:{},dept:{}", ea, depts);
        //MetaParam metaParam = new MetaParam(enterprise.getEa(), enterprise.getCreateBy());
        commonCreateUpDept(ei, depts, tableMap, appId);
    }

    private Map<Long, DeptVo> commonCreateUpDept(Integer ei, LinkedList<Dept> depts, Map<Long, DeptVo> tableMap, String appId) {
        depts.stream().forEach(item -> {
            //查询父级部门
            DeptVo deptVo = tableMap.get(item.getParentid());
            Integer parentId = Optional.ofNullable(deptVo.getCrmDeptId()).orElse(Constant.TREE_PARENT_ID);
            //BeanResult<CreatedCircle> circle = circleService.createCircle(metaParam, validName(item.getName()), parentId);
            DeptVo vo = new DeptVo();
            vo.setEi(ei);
            vo.setName(validName(item.getName()));
            vo.setCrmParentId(parentId);
            Result<Integer> deptResult = crmRestManager.createDept(vo);
            log.info("circleBeanResult objectMapping :{},deptName:{}", deptResult, item.getName());
            DingSyncApi syncApi = new DingSyncApi();
            syncApi.setApiName(DEP_OBJ);
            syncApi.setSyncDirection(2);
            DingSyncLogDO kcSyncLogDO = DynamicParamUtil.buildSyncLogDO(ei, syncApi, item.getName(), OPERATOR, SyncTypeEnum.CALL_BACK_SYNC.getType(), OperationTypeEnum.ADD.getType());
            String detail = "dingding：create apiName[{" + DEP_OBJ + "}], [" + item.getName() + "]";
            String content = String.format("[%s] 创建纷享部门，数据流向为[%s]", item.getName(), DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
            DynamicParamUtil.supplementLogDO(kcSyncLogDO, detail, null, null, content, OperationStatusEnum.SYNC_SUCCESS.getStatus(), 1, 0, 1, SyncLogStatusEnum.SUCCESS.getStatus());
            dingSyncLogManager.logDingSync(kcSyncLogDO);
            Map<String, Object> sameMap = Maps.newHashMap();
            if (deptResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
                sameMap = fixCallBackName(ei, item, appId);
                if (Boolean.parseBoolean(sameMap.get("same").toString())) {
                    //如果是true，上级不同名
                    //创建部门
                    //circle = circleService.createCircle(metaParam, sameMap.get("name").toString(), parentId);
                    vo.setName(sameMap.get("name").toString());
                    deptResult = crmRestManager.createDept(vo);
                }
            }
            //查询crm的父级部门ID
//            BeanResult<Circle> circleNoAdminId = circleService.getCircleNoAdminId(enterprise.getEa(), circle.getResult().getCircleId());
            //DeptVo vo = new DeptVo();
            vo.setEi(ei);
            vo.setDingDeptId(item.getId());
            vo.setDingParentId(item.getParentid());
            vo.setName(item.getName());
            vo.setDingDeptOwner(item.getDeptOwner());
            vo.setCrmDeptId(deptResult.getData());
            vo.setCrmParentId(parentId);
            //vo.setSeq(circle.getResult().getOrder());
            vo.setCreateTime(new Date());
            vo.setUpdateTime(new Date());
            tableMap.put(item.getId(), vo);
            Integer count = dingDeptMananger.addDeptByEi(vo, appId);
            if (count != 0) {
                log.info("insert ding_dept row:{},ei:{},deptVo:{}", count, ei, vo);
            }
        });
        return tableMap;
    }


    private Map<Long, DeptVo> supportUpdateDept(Integer ei, String ea, Dept dept, Map<Long, Dept> DingDeptMap, Map<Long, DeptVo> tableMap, String appId) {
        //更新部门信息
        log.info("重新映射更新钉钉部门 ea:{},dept:{}", ea, dept);
        DingEnterpriseResult enterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId).getData();
        List<Long> listParent = DingRequestUtil.getListParent(ei, enterprise.getClientIp(), dept.getId(), tokenManager.getToken(ei, appId));
        if (ObjectUtils.isEmpty(listParent)) {
            log.info("重新映射更新钉钉部门 ea:{},dept:{}", ea, dept);
            return tableMap;
        }
        Collections.reverse(listParent);
        //如果上级部门不存在，则需要先新增父部门
        LinkedList<Dept> depts = Lists.newLinkedList();
        for (int i = 0; i < listParent.size(); i++) {
            DeptVo temp = tableMap.get(listParent.get(i).longValue());
            if (ObjectUtils.isEmpty(temp)) {
                final String outDeptId = String.valueOf(temp.getDingDeptId());
                final String fsDeptId = outerOaDepartmentBindManager.getFsDeptIdAndAddByEaAndOutDeptId(ChannelEnum.dingding, ea, appId, outDeptId);
                if (StringUtils.isNotEmpty(fsDeptId)) {
                    // 写入绑定表
                    tableMap.put(temp.getDingDeptId(), dingDeptMananger.queryByDingId(ei, temp.getDingDeptId(), appId));
                } else {
                    depts.add(DingDeptMap.get(listParent.get(i).longValue()));
                }
            }
        }
        log.info("更新部门ing,重新映射新增钉钉部门 ea:{},dept:{}", ea, depts);
        //MetaParam metaParam = new MetaParam(enterprise.getEa(), Constant.SYSTEM_USER);
        tableMap = commonCreateUpDept(ei, depts, tableMap, appId);
        Integer crmPart = tableMap.get(listParent.get(listParent.size() - 1).longValue()).getCrmParentId();
        String name = validName(dept.getName());
        DeptVo updateDeptVo = tableMap.get(dept.getId());
        Integer crmID = updateDeptVo.getCrmDeptId();
        //BaseResult baseResult = circleService.updateCircle(metaParam, crmID, name, crmPart, null);
        DeptVo vo = new DeptVo();
        vo.setCrmDeptId(crmID);
        vo.setName(name);
        vo.setCrmParentId(crmPart);
        Result<Void> baseResult = crmRestManager.modifyDept(vo);
        Map<String, Object> sameMap = Maps.newHashMap();
        if (baseResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
            //如果部门名称已经存在，获取上级部门名称作为前缀
            sameMap = fixCallBackName(ei, dept, appId);
            if (Boolean.valueOf(sameMap.get("same").toString())) {
                //如果是true，上级不同名
                //创建部门
                //baseResult = circleService.updateCircle(metaParam, crmID, validName(sameMap.get("name").toString()), crmPart, null);
                vo.setName(validName(sameMap.get("name").toString()));
                baseResult = crmRestManager.modifyDept(vo);
                dept.setName(sameMap.get("name").toString());
            }
        }
        DingSyncApi syncApi = new DingSyncApi();
        syncApi.setApiName(DEP_OBJ);
        syncApi.setSyncDirection(2);
        DingSyncLogDO kcSyncLogDO = DynamicParamUtil.buildSyncLogDO(ei, syncApi, dept.getName(), OPERATOR, SyncTypeEnum.CALL_BACK_SYNC.getType(), OperationTypeEnum.UPDATE.getType());
        String detail = "dingding：create apiName[{" + DEP_OBJ + "}], [" + dept.getName() + "]";
        String content = String.format("[%s] 修改纷享部门，数据流向为[%s]", dept.getName(), DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
        DynamicParamUtil.supplementLogDO(kcSyncLogDO, detail, null, null, content, OperationStatusEnum.SYNC_SUCCESS.getStatus(), 1, 0, 1, SyncLogStatusEnum.SUCCESS.getStatus());
        dingSyncLogManager.logDingSync(kcSyncLogDO);
        log.info("callback controller modifyDepartmentResult result:{}", baseResult);
        //更新数据库
        //更新名字/上级部门/主管
        updateDeptVo.setName(dept.getName());
        updateDeptVo.setDingParentId(dept.getParentid());
        updateDeptVo.setDingDeptOwner(dept.getDeptOwner());
        updateDeptVo.setCrmParentId(crmPart);
        updateDeptVo.setCrmDeptId(crmID);
        Result<Integer> integerResult = dingDeptMananger.updateDept(updateDeptVo, appId);
        log.info("supportUpdateDept insert:{}", integerResult.getData());
        return tableMap;
    }


    private List<Dept> requestDingAllDept(Integer ei, Integer userId, String ea, String appId) {
        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        List<Dept> detailList = Lists.newArrayList();
        //查询部门列表
        String appSecret = mappingEnterprise.getData().getAppSecret();
        ScopeVo scopeVo = DingRequestUtil.queryScoreDeptEmployee(ei, mappingEnterprise.getData().getClientIp(),
                appId, appSecret, mappingEnterprise.getData().getToken(), tokenManager.getToken(ei, appId));
        log.info("requestDingAllDept,scopeVo={}",scopeVo);
        if (scopeVo == null) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return detailList;
        }
        if (CollectionUtils.isEmpty(scopeVo.getAuthedDept())) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return detailList;
        }
        //解析出部门，查询部门信息
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedDept())) {

            //如果scopeVo的部门为1.说明权限是全部门需要遍历全部部门
            List<Dept> depts = new ArrayList<>();
            if (scopeVo.getAuthedDept().get(0) == 1) {
                detailList = DingRequestUtil.queryDeptList(ei, mappingEnterprise.getData().getClientIp(),
                        appId, appSecret, mappingEnterprise.getData().getToken(), "1");
                if (CollectionUtils.isEmpty(detailList)) {
                    log.warn("query dept list failed, ei={}, deptResponse={}.", ei, detailList);
                    return detailList;
                }
                Dept deptDetail = DingRequestUtil.queryDeptDetail(ei, mappingEnterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), mappingEnterprise.getData().getToken(), 1L);
                detailList.add(deptDetail);
            } else {
                for (int i = 0; i < scopeVo.getAuthedDept().size(); i++) {
                    List<Long> deptIdList = new ArrayList<>();
                    deptIdList.add(scopeVo.getAuthedDept().get(i));
                    //先把父级部门查询出来
                    List<Dept> deptItem = DingRequestUtil.queryDeptList(ei, mappingEnterprise.getData().getClientIp(),
                            appId, appSecret, mappingEnterprise.getData().getToken(), scopeVo.getAuthedDept().get(i).toString());
                    //有可能没有子部门的情况
                    if(CollectionUtils.isNotEmpty(deptItem)) {
                        List<Long> collect = deptItem.stream().map(Dept::getId)
                                .collect(Collectors.toList());
                        deptIdList.addAll(collect);
                        depts.addAll(deptItem);
                    }
                    for (int j = 0; j < deptIdList.size(); j++) {
                        Dept deptDetail = DingRequestUtil.queryDeptDetail(ei, mappingEnterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), mappingEnterprise.getData().getToken(), deptIdList.get(j));
                        detailList.add(deptDetail);
                    }
                }
                List<Long> scopeCollect = detailList.stream().map(Dept::getId)
                        .collect(Collectors.toList());
                scopeVo.setAuthedDept(scopeCollect);
            }
        }
        log.info("requestDingAllDept,detailList={}",detailList);
        return detailList;
    }


    //递归创建部门
    public void recallInitDept(List<Dept> depts, Integer ei, String ea, String appId) {
        log.info("recallInitDept ：{}", ei);
        List<Dept> orderDepts = ReorderDeptUtils.reorderDingdingDepartments(depts);
        //更新或者创建部门
        obtainOrderDept(ei, ea, orderDepts, appId);
    }

    //递归创建钉钉部门
    private ConcurrentHashMap<Long, Dept> recallConstructDept
    (Integer ei, String ea, Dept dept, Map<Long, Dept> DingDeptMap, ConcurrentHashMap<Long, Dept> deptMap, Map<String, Dept> nameDept, LinkedList<Dept> linkDepts) {
        if (ObjectUtils.isEmpty(DingDeptMap.get(dept.getId())) || ObjectUtils.isNotEmpty(deptMap.get(dept.getId())))
            return deptMap;
        if (ObjectUtils.isNotEmpty(DingDeptMap.get(dept.getParentid())) && ObjectUtils.isEmpty(deptMap.get(dept.getId()))) {
            recallConstructDept(ei, ea, DingDeptMap.get(dept.getParentid()), DingDeptMap, deptMap, nameDept, linkDepts);
        }
        //创建部门
//        if(ObjectUtils.isNotEmpty(nameDept.get(dept.getName()))){
//            String parentId=deptMap.get
//        }
        deptMap.put(dept.getId(), dept);
        linkDepts.addLast(dept);
        return deptMap;
    }

    private void obtainOrderDept(Integer ei, String ea, List<Dept> orderDepts, String appId) {
        log.info("obtainOrderDept:{}", ei);
        //创建crm部门返回的result
        DeptVo parentDeptVo = null;
        //ListResult<Circle> allCircles = new ListResult<>();
        Integer parentCrmId = Constant.TREE_PARENT_ID;
        Integer CrmId = null;
        Integer order = null;
        for (int i = 0; i < orderDepts.size(); i++) {
            Dept originDept = orderDepts.get(i);
            originDept.setName(validName(originDept.getName()));
            //部门在crm的数据
            DeptVo crmData = queryDeptData(ei, originDept.getId(), appId);
            //父级部门在crm的数据
            DeptVo crmParentData = queryDeptData(ei, originDept.getParentid(), appId);
            if (ObjectUtils.isNotEmpty(crmData)) {
                parentCrmId = ObjectUtils.isEmpty(crmParentData) ? Constant.TREE_PARENT_ID : crmParentData.getCrmDeptId();
                //如果不为空，说明数据已经存在。走更新部门的信息
                DeptVo vo = new DeptVo(ei, originDept.getName(), null, parentCrmId, crmData.getCrmDeptId());
                Result<Void> updateResult = crmRestManager.modifyDept(vo);
                //更新中间表的信息
                crmData.setName(originDept.getName());
                crmData.setCrmParentId(parentCrmId);
                crmData.setUpdateTime(new Date());
                crmData.setDingParentId(originDept.getParentid());
                Integer count = updateDeptData(ei, crmData, appId);
                log.info("update originDept:{},crmData:{},count:{},result:{}", originDept, crmData, count, updateResult);
                //写入日志
                Integer statusCode = updateResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
                LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.UPDATE.getType(), originDept.getName(), statusCode, updateResult.getErrorMessage());
                logManager.writeLog(logWriteVo, "DepObj");
            } else {
                //不存在映射关系，就创建
                DeptVo vo = new DeptVo(ei, originDept.getName(), null, Constant.TREE_PARENT_ID);
                //如果是根级部门，直接创建，然后插入表格
                if (ObjectUtils.isNotEmpty(originDept.getParentid()) && originDept.getParentid() != 1) {
                    //查询父级部门的信息
                    parentDeptVo = queryDeptData(ei, originDept.getParentid(), appId);
                    log.info("parentId:{},parentDeptVo:{},time:{}", originDept.getParentid(), parentDeptVo, System.currentTimeMillis());
                    parentCrmId = ObjectUtils.isNotEmpty(parentDeptVo) ? parentDeptVo.getCrmDeptId() : Constant.TREE_PARENT_ID;
                    vo.setCrmParentId(parentCrmId);
                }
                Result<Integer> createResult = crmRestManager.createDept(vo);
                if (createResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
                    StringBuilder nameBuilder = new StringBuilder(vo.getName());
                    nameBuilder.append("__").append(String.valueOf(Math.round((Math.random() + 1) * 1000)));
                    vo.setName(nameBuilder.toString());
                    createResult = crmRestManager.createDept(vo);
                }
                CrmId = createResult.getData();
                //不处理crm已经存在同名或者在中间表没有映射已经存在的部门
                if (ObjectUtils.isNotEmpty(CrmId)) {
                    DeptVo deptVo = new DeptVo();
                    deptVo.setEi(ei);
                    deptVo.setDingDeptId(originDept.getId());
                    deptVo.setDingParentId(originDept.getParentid());
                    deptVo.setName(originDept.getName());
                    deptVo.setDingDeptOwner(originDept.getDeptOwner());
                    deptVo.setCrmDeptId(CrmId);
                    deptVo.setCreateTime(new Date());
                    deptVo.setUpdateTime(new Date());
                    deptVo.setCrmParentId(parentCrmId);
                    deptVo.setSeq(order);
                    Integer count = insertDeptData(Lists.newArrayList(deptVo), ei, Constant.SYSTEM_MANAGER, appId);
                    log.info("insert originDept:{},crmData:{},count:{}", originDept, crmData, count);
                }
                //写入日志
                Integer statusCode = ObjectUtils.isNotEmpty(CrmId) ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
                LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.ADD.getType(), originDept.getName(), statusCode, createResult.getErrorMessage());
                logManager.writeLog(logWriteVo, "DepObj");
            }
        }
    }

    //插入数据
    private Integer insertDeptData(List<DeptVo> deptVos, Integer ei, Integer userId, String appId) {
        Integer count = dingDeptMananger.initDeptManager(deptVos, ei, userId, appId);
        return count;
    }

    //查询数据
    private DeptVo queryDeptData(Integer ei, Long deptId, String appId) {
        DeptVo deptVo = dingDeptMananger.queryByDingId(ei, deptId, appId);
        if (ObjectUtils.isNotEmpty(deptVo)) {
            return deptVo;
        }

        final String ea = eieaConverter.enterpriseIdToAccount(ei);
        final String fsDeptId = outerOaDepartmentBindManager.getFsDeptIdAndAddByEaAndOutDeptId(ChannelEnum.dingding, ea, appId, String.valueOf(deptId));

        return StringUtils.isEmpty(fsDeptId) ? null : dingDeptMananger.queryByDingId(ei, deptId, appId);
    }

    //修改数据
    private Integer updateDeptData(Integer ei, DeptVo deptVo, String appId) {
        Result<Integer> count = dingDeptMananger.updateDept(deptVo, appId);
        return count.getData();
    }


//    private Map<String, Object> fixSameDeptName(Map<Long, Dept> DingDeptMap, Dept dept, Circle circle, String ea) {
//        Dept parentDept = DingDeptMap.get(dept.getParentid());
//        MetaParam metaParam = new MetaParam(ea, Constant.SYSTEM_USER);
//        BeanResult<Circle> parentResult = circleService.getCircle(metaParam, circle.getParentId());
//        Map<String, Object> map = Maps.newHashMap();
//        if (ObjectUtils.isNotEmpty(parentResult.getResult()) && ObjectUtils.isNotEmpty(parentDept)) {
//            if (!parentDept.getName().equals(parentResult.getResult().getName()) && parentResult.getResult().getCircleId() != Constant.TREE_DEPT) {
//                StringBuilder builder = new StringBuilder();
//                String combineName = builder.append(parentDept.getName()).append("-").append(dept.getName()).toString();
//                map.put("same", Boolean.TRUE);
//                map.put("name", validName(combineName));
//                dept.setName(validName(combineName));
//                DingDeptMap.put(dept.getId(), dept);
//                log.info("fixsameDept combineName not same:{}", validName(combineName));
//                return map;
//            }
//        }
//        //如果上级部门同名
//        map.put("same", Boolean.FALSE);
//        map.put("name", validName(dept.getName()));
//        log.info("fixsameDept combineName:{}", validName(dept.getName()));
//        return map;
//    }

    //处理回调事件部门同名
    private Map<String, Object> fixCallBackName(Integer ei, Dept dept, String appId) {
        //回调事件处理变更
        Map<String, Object> map = Maps.newHashMap();
        //判断中间表的数据是否已经存在该部门
        List<DeptVo> deptName = dingDeptMananger.getByEIAndName(ei, dept.getName(), appId);
        Boolean isEquals = false;
        for (DeptVo vo : deptName) {
            if (vo.getDingDeptId().equals(dept.getId()) && vo.getCrmDeptId() != 0) {
                isEquals = true;
            }
        }

        if (!isEquals) {
            //如果不一致，拿enterprise的全局index++。默认一开始10000
            Result<DingEnterpriseResult> result = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
            if (ObjectUtils.isNotEmpty(result.getData())) {
                Integer all_index = Objects.nonNull(result.getData().getAllIndex()) ? result.getData().getAllIndex() + 1 : 1;
                String finalName = dept.getName().concat(Symbol).concat(all_index.toString());
                //更新enterprise的信息
                DingEnterpriseResult enterpriseResult = result.getData();
                DingEnterprise enterprise = new DingEnterprise();
                BeanUtils.copyProperties(enterpriseResult, enterprise);
                enterprise.setAllIndex(all_index);
                dingEnterpriseManager.updateEnterprise(enterprise);
                map.put("same", Boolean.TRUE);
                map.put("name", validName(finalName));
                return map;
            }
        }
        //如果前缀名字一致，确定是同名的，拿中间表的数据去当做部门名字
        map.put("same", Boolean.FALSE);
        map.put("name", validName(dept.getName()));
        return map;
    }

    //初始化员工
    public Result initDingEmployee(Integer ei, Integer userId, String appId) {
        log.info("initDingEmployee,ei={}", ei);
        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        log.info("initDingEmployee,mappingEnterprise={}",mappingEnterprise);
        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }

        String appKey = mappingEnterprise.getData().getAppKey();
        String appSecret = mappingEnterprise.getData().getAppSecret();
        //查询部门列表
        ScopeVo scopeVo = DingRequestUtil.queryScoreDeptEmployee(ei, mappingEnterprise.getData().getClientIp(),
                appKey, appSecret, mappingEnterprise.getData().getToken(), tokenManager.getToken(ei, appId));
        log.info("initDingEmployee,scopeVo={}", scopeVo);
        if (scopeVo == null) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return Result.newError(ResultCode.DING_CONNECT_PARAM_ERROR);
        }
        //查询部门列表,调整获取应用所赋予的部门权限范围
        List<Dept> deptResponse = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedDept())) {
            //如果scopeVo的部门为1.说明权限是全部门需要遍历全部部门
            List<Long> deptIds = new LinkedList<>();
            deptIds.addAll(scopeVo.getAuthedDept());
            if (scopeVo.getAuthedDept().get(0) == 1) {
                List<Dept> depts = DingRequestUtil.queryDeptList(ei, mappingEnterprise.getData().getClientIp(),
                        appKey, appSecret, mappingEnterprise.getData().getToken(), "1");
                log.info("initDingEmployee,depts={}",depts);
                if(CollectionUtils.isNotEmpty(depts)) {
                    List<Long> collect = depts.stream().map(Dept::getId)
                            .collect(Collectors.toList());
                    deptIds.addAll(collect);
                }
            } else {
                scopeVo.getAuthedDept().stream().forEach(item -> {
                    //获取子部门详情
                    List<Dept> depts = DingRequestUtil.queryDeptList(ei, mappingEnterprise.getData().getClientIp(),
                            appKey, appSecret, mappingEnterprise.getData().getToken(), item.toString());
                    log.info("initDingEmployee,depts2={}", depts);
                    if(CollectionUtils.isNotEmpty(depts)) {
                        List<Long> collect = depts.stream().map(Dept::getId)
                                .collect(Collectors.toList());
                        deptIds.addAll(collect);
                    }
                });
            }
            for(Long id : deptIds) {
                Dept dept = DingRequestUtil.queryDeptDetail(ei, mappingEnterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), mappingEnterprise.getData().getToken(), id);
                log.info("initDingEmployee,dept={}", dept);
                if(ObjectUtils.isNotEmpty(dept)) {
                    deptResponse.add(dept);
                }
            }
        }
        log.info("ObjectMappingServiceImpl.initDingEmployee.deptResponse.size={}", deptResponse.size());
        if (CollectionUtils.isNotEmpty(deptResponse)) {
            //查询部门下所有员工，并初始化到中间表
            Gson gson = new Gson();
            for (int i = 0; i < deptResponse.size(); i++) {
                Dept dept = gson.fromJson(gson.toJson(deptResponse.get(i)), Dept.class);
                List<User> deptUsers = DingRequestUtil.queryDeptUser(ei, mappingEnterprise.getData().getClientIp(),
                        dept.getId(), appKey, appSecret, mappingEnterprise.getData().getToken(), tokenManager.getToken(ei, appId), Long.valueOf(mappingEnterprise.getData().getAgentId()));
                if (CollectionUtils.isEmpty(deptUsers)) {
                    log.info("this dept no user, ei={},deptId={},deptName={}.", ei, dept.getId(), dept.getName());
                    continue;
                }
                log.info("initDingEmployee,deptUsers={},dept={},ei={}.", deptUsers, dept, ei);
                //保存钉钉员工信息
                Integer count = dingMappingEmployeeManager.initMappingEmployee(deptUsers, ei, dept.getId(), dept.getName(), appId);
                log.info("initDingEmployee,count={}",count);
                if (Objects.nonNull(count) && count.equals(deptUsers.size())) {
                    log.info("初始化员工成功" + count + "条");
                } else {
                    log.warn("初始化员工失败，deptId={},deptName={}.", dept.getId(), dept.getName());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedUser())) {
            List<User> differEmployeeList = Lists.newArrayList();
            scopeVo.getAuthedUser().parallelStream().filter(item -> !isExistData(ei, item, appId)).forEach(item -> {
                //添加未加入的员工
                User user = DingRequestUtil.getUser(ei, mappingEnterprise.getData().getClientIp(),
                        appKey, appSecret, item, mappingEnterprise.getData().getToken(), tokenManager.getToken(ei, appId), Long.valueOf(mappingEnterprise.getData().getAgentId()));
                log.info("initDingEmployee,user={}", user);
                //获取不到dingEmployeeId的时候，忽略
                if(ObjectUtils.isNotEmpty(user) && StringUtils.isNotEmpty(user.getUserid())) {
                    differEmployeeList.add(user);
                }
            });
            Integer count = dingMappingEmployeeManager.initMappingEmployee(differEmployeeList, ei, null, StringUtils.EMPTY, appId);
        }
        //初始化时中间表为全部未绑定，直接通过ei去获取全部数据
        autoSaveDingEmployeeAccount(ei, new LinkedList<>(), appId);
        log.info("initDingEmployee,end,ei={}", ei);
        return Result.newSuccess();
    }

    //全量拉取员工，比对数据库中的表
    public Result allPullEmployee(Integer ei, Integer userId, String appId) {
        StopWatch stopWatch = StopWatch.create("allPullEmployee");

        log.info("pullEmployee dingding emp startz1 ei={}.", ei);
        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }

        String appKey = mappingEnterprise.getData().getAppKey();
        String appSecret = mappingEnterprise.getData().getAppSecret();
        //查询部门列表
        ScopeVo scopeVo = DingRequestUtil.queryScoreDeptEmployee(ei, mappingEnterprise.getData().getClientIp(),
                appKey, appSecret, mappingEnterprise.getData().getToken(), tokenManager.getToken(ei, appId));
        if (scopeVo == null) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return Result.newError(ResultCode.DING_CONNECT_PARAM_ERROR);
        }
        if (CollectionUtils.isEmpty(scopeVo.getAuthedDept())) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return Result.newError(ResultCode.DEPT_LIST_ERROR);
        }
        //解析出部门，查询部门信息
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedDept())) {
            List<Long> deptList = new ArrayList<>();
            //如果scopeVo的部门为1.说明权限是全部门需要遍历全部部门
            if (scopeVo.getAuthedDept().get(0) == 1) {
                List<Dept> depts = DingRequestUtil.queryDeptList(ei, mappingEnterprise.getData().getClientIp(),
                        appKey, appSecret, mappingEnterprise.getData().getToken(), "1");

                List<Long> collect = depts.stream().map(Dept::getId)
                        .collect(Collectors.toList());
                scopeVo.setAuthedDept(collect);
            } else {
                scopeVo.getAuthedDept().stream().forEach(item -> {
                    List<Dept> depts = DingRequestUtil.queryDeptList(ei, mappingEnterprise.getData().getClientIp(),
                            appKey, appSecret, mappingEnterprise.getData().getToken(), item.toString());
                    List<Long> collect = depts.stream().map(Dept::getId)
                            .collect(Collectors.toList());
                    deptList.addAll(collect);

                });
                scopeVo.setAuthedDept(deptList);
            }
            //查询部门下所有员工，

            for (int i = 0; i < scopeVo.getAuthedDept().size(); i++) {
                //查询部门信息
                Dept dept = DingRequestUtil.queryDeptDetail(ei, mappingEnterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), mappingEnterprise.getData().getToken(), scopeVo.getAuthedDept().get(i));

                List<User> deptUsers = DingRequestUtil.queryDeptUser(ei, mappingEnterprise.getData().getClientIp(),
                        dept.getId(), appKey, appSecret, mappingEnterprise.getData().getToken(), tokenManager.getToken(ei, appId), Long.valueOf(mappingEnterprise.getData().getAgentId()));
                if (CollectionUtils.isEmpty(deptUsers)) {
                    log.info("this dept no user, ei={},deptId={},deptName={}.", ei, dept.getId(), dept.getName());
                    continue;
                }
                Gson gson = new Gson();
                //从数据库
                Result<List<DingMappingEmployeeResult>> employeeList = dingMappingEmployeeManager.queryEmpsBindByEi(ei, appKey, null, null, null, null, null, dept.getId());

                HashMap<String, User> employeeDingMap = Maps.newHashMap();
                HashMap<String, User> employeeDataBaseMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(employeeList.getData())) {
                    employeeList.getData().parallelStream().forEach(item -> {
                        User user = new User(item.getDingUnionId(), item.getDingEmployeeId(), item.getDingEmployeeName(), item.getDingEmployeePhone(), StringUtils.EMPTY, "", "", "", null, null, null,null);
                        employeeDataBaseMap.put(user.getUserid(), user);

                    });
                }
                for (int j = 0; j < deptUsers.size(); j++) {
                    String itemValue = gson.toJson(deptUsers.get(j));
                    User user = gson.fromJson(itemValue, User.class);
                    employeeDingMap.put(user.getUserid(), user);

                }
                //已经在数据库中的钉钉员工与拉取过来的钉钉员工差异化比较，再存入数据库中。
                MapDifference<String, User> difference = Maps.difference(employeeDingMap, employeeDataBaseMap);
                Map<String, User> insertUserMap = difference.entriesOnlyOnLeft();
                //插入员工信息
                if (CollectionUtils.isNotEmpty(insertUserMap.entrySet()))
                    executorService.execute(new InsertEmployeeTask(dingMappingEmployeeManager, insertUserMap, ei, userId, dept.getId(), dept.getName(), appId));
//                    insertEmployee(insertUserMap, ei, userId, dept.getId(), dept.getName());
                //更新员工信息
                Sets.SetView<String> setDiffer = Sets.intersection(employeeDataBaseMap.keySet(), employeeDingMap.keySet());
                if (CollectionUtils.isNotEmpty(setDiffer))
                    executorService.execute(new UpdateEmployeeTask(setDiffer, employeeDataBaseMap, employeeDingMap, ei, appId));
                //删除员工信息
                Map<String, User> deleteMap = difference.entriesOnlyOnRight();
                log.info("delete ei:{}, employeeMap:{}", ei, deleteMap);
                if (MapUtils.isNotEmpty(deleteMap))
                    executorService.execute(new DeleteEmployeeTask(deleteMap, ei, appId));
            }
        }
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedUser())) {
            List<User> differEmployeeList = Lists.newArrayList();
            scopeVo.getAuthedUser().parallelStream().filter(item -> !isExistData(ei, item, appId)).forEach(item -> {
                //添加未加入的员工
                User user = DingRequestUtil.getUser(ei, mappingEnterprise.getData().getClientIp(),
                        appKey, appSecret, item, mappingEnterprise.getData().getToken(), tokenManager.getToken(ei, appId), Long.valueOf(mappingEnterprise.getData().getAgentId()));
                differEmployeeList.add(user);
            });
            Integer count = dingMappingEmployeeManager.initMappingEmployee(differEmployeeList, ei, null, StringUtils.EMPTY, appId);
        }

        stopWatch.lap("allPullEmployee");
        stopWatch.log();
        return Result.newSuccess();
    }

    //根据用户钉钉的ID确定是否在数据库已经存在
    public boolean isExistData(Integer ei, String userId, String appId) {
        return dingMappingEmployeeManager.queryEmpByDingUserId(ei, appId, userId).getData() == null ? false : true;
    }

    //封装集合差集的方法，不改变原来集合的数据
    public static <T, K extends Collection<? extends T>> Set<T> sub(K a, K b) {
        Set<T> result = new HashSet<>();
        if (a != null) {
            result = new HashSet<>(a);
            result.removeAll(b);
        }
        return result;
    }

    //封装集合差集的方法，不改变原来集合的数据
    public static <T, K extends Collection<? extends T>> Set<T> retainAll(K a, K b) {
        Set<T> result = new HashSet<>();
        if (a != null) {
            result = new HashSet<>(a);
            result.retainAll(b);
        }
        return result;
    }

    @Override
    public Result<Map<String, Integer>> bindEmployee(List<DingMappingEmployeeResult> list, Integer ei, Integer employeeId, boolean flag, String appId) {
        log.info("bindEmployee ei:{} appId:{} empId:{} flag:{} list:{}", ei, appId, employeeId, flag, list);
        //查询纷享员工
        final String ea = eieaConverter.enterpriseIdToAccount(ei);
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, ea, appId);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, ea, appId);
        final SystemFieldMappingResult.ItemFieldMapping mapping = outerOaConfigInfoManager.getEmployeeUniqueIdentity(dcId);
        if (Objects.nonNull(mapping)) {
            log.info("bindEmployee mapping:{}", mapping);
            for (DingMappingEmployeeResult employee : list) {
                final OuterOaEmployeeDataEntity dataEntity = outerOaEmployeeDataManager.queryByChannelAndAppIdAndEmpId(ChannelEnum.dingding, appId, outEa, employee.getDingEmployeeId());
                final JSONObject outUserInfo = dataEntity.getOutUserInfo();
                final Object o = outUserInfo.get(mapping.getOuterOAFieldApiName());
                if (Objects.isNull(o)) {
                    continue;
                }
                final String id = crmRestManager.queryByPhone(ei, mapping.getCrmFieldApiName(), o.toString()).getData().get("user_id");
                if (Objects.nonNull(id)) {
                    log.info("bindEmployee id:{}", id);
                    employee.setEmployeeId(Integer.valueOf(id));
                }
            }
        } else {
            //查询纷享所有员工
            Result<List<DingMappingEmployeeResult>> fxResult = getEmployeeFs(ei, new LinkedList<>());
            List<DingMappingEmployeeResult> employeeDtos = fxResult.getData();
            if (CollectionUtils.isNotEmpty(employeeDtos)) {
                //通过手机号比较哪些可以绑定
                for (DingMappingEmployeeResult employee : list) {
                    employee.setEi(ei);
                    employee.setUpdateBy(employeeId);
                    employee.setCreateBy(employeeId);
                    for (DingMappingEmployeeResult employeeDto : employeeDtos) {
                        if (StringUtils.isNotEmpty(employee.getDingEmployeePhone()) && StringUtils.isNotEmpty(employeeDto.getEmployeePhone())
                                && employee.getDingEmployeePhone().equals(employeeDto.getEmployeePhone())) {
                            employee.setEmployeePhone(employeeDto.getEmployeePhone());
                            employee.setEmployeeName(employeeDto.getEmployeeName());
                            employee.setEmployeeId(employeeDto.getEmployeeId());
                            employee.setEmployeeStatus(employeeDto.getEmployeeStatus());
                        } else if (!flag && StringUtils.isNotEmpty(employee.getDingEmployeeName()) && StringUtils.isNotEmpty(employeeDto.getEmployeeName())
                                && employee.getDingEmployeeName().equals(employeeDto.getEmployeeName()) && StringUtils.isEmpty(employeeDto.getDingEmployeePhone())) {
                            //适用于有些客户不肯授权手机号码做的兼容
                            //如果手机号匹配不上，根据名字匹配
                            employee.setEmployeePhone(employeeDto.getEmployeePhone());
                            employee.setEmployeeName(employeeDto.getEmployeeName());
                            employee.setEmployeeId(employeeDto.getEmployeeId());
                            employee.setEmployeeStatus(employeeDto.getEmployeeStatus());
                        }
                    }
                }
            }
        }

        //将数据保存到员工绑定表
        Integer count;
        if (flag) {
            count = dingMappingEmployeeManager.saveAutoMappingEmployee(list, appId);
        } else {
            count = dingMappingEmployeeManager.saveMappingEmployee(list, appId);
        }

        Map<String, Integer> map = new HashMap<>();
        map.put("newEmpCount", count);
//        map.put("changeStatusCount", 0);
        return Result.newSuccess(map);
    }

    @Override
    public Result<List<DingMappingEmployeeResult>> getEmployeeFs(Integer ei, List<Integer> userList) {
        BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
        batchGetEmployeeDtoArg.setEnterpriseId(ei);
        batchGetEmployeeDtoArg.setRunStatus(RunStatus.ALL);

        BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = null;
        try {
            GetAllEmployeeIdsArg getAllEmployeeIdsArg = new GetAllEmployeeIdsArg();
            getAllEmployeeIdsArg.setRunStatus(RunStatus.ALL);
            getAllEmployeeIdsArg.setEnterpriseId(ei);
            //userList不为空的话，证明查询的是部分员工
            if (!ObjectUtils.isEmpty(userList)) {
                batchGetEmployeeDtoArg.setEmployeeIds(userList);
            } else {
                GetAllEmployeeIdsResult ids = employeeProviderService.getAllEmployeeIds(getAllEmployeeIdsArg);
                log.info("return ids:{}", ids);
                batchGetEmployeeDtoArg.setEmployeeIds(ids.getEmployeeIds());
            }
            batchGetEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
            log.info("return batchGetEmployeeDtoResult:{}", batchGetEmployeeDtoResult);
        } catch (OrganizationException e) {
            log.error("获取纷享职员信息失败，exception = {}.", e);
            return Result.newError(ResultCode.GET_FXEMP_FAILED);
        }

        List<EmployeeDto> employeeDtoList = batchGetEmployeeDtoResult.getEmployeeDtos();
        List<DingMappingEmployeeResult> list = new ArrayList<>();
        for (EmployeeDto employeeDto : employeeDtoList) {
            DingMappingEmployeeResult dingMappingEmployeeResult = new DingMappingEmployeeResult();
            dingMappingEmployeeResult.setEmployeeId(employeeDto.getEmployeeId());
            dingMappingEmployeeResult.setEmployeeName(employeeDto.getName());
            dingMappingEmployeeResult.setEmployeePhone(employeeDto.getMobile());
            dingMappingEmployeeResult.setEmployeeStatus(employeeDto.getStatus().getValue());
            list.add(dingMappingEmployeeResult);
        }

        return Result.newSuccess(list);
    }

    @Override
    public Result<Integer> deleteBind(Integer ei, String dingEmployeeId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmployeeId)) {
            log.warn("deleteBind param ei or dingEmployeeId is null.");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        //解绑员工，不能真正删除中间表数据，而是将状态改为0
        return Result.newSuccess(dingMappingEmployeeManager.relieveBind(ei, dingEmployeeId, appId));
    }

    @Override
    public Result<Integer> saveOperation(DingMappingEmployeeResult dingMappingEmployeeResult, String appId) {
        if (Objects.isNull(dingMappingEmployeeResult) || Objects.isNull(dingMappingEmployeeResult.getEmployeeId())) {
            log.warn("saveOperation param emp is null,dingMappingEmployeeResult={}.", dingMappingEmployeeResult);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if (dingMappingEmployeeManager.checkEmpBindByEmpId(dingMappingEmployeeResult.getEi(), dingMappingEmployeeResult.getEmployeeId(), appId)) {
            log.warn("纷享职员已被绑定");
            return Result.newError(ResultCode.EMP_HAS_BIND);
        }

        //根据Id查询纷享员工信息
        GetEmployeeDtoArg getEmployeeDtoArg = new GetEmployeeDtoArg();
        getEmployeeDtoArg.setEnterpriseId(dingMappingEmployeeResult.getEi());
        getEmployeeDtoArg.setEmployeeId(dingMappingEmployeeResult.getEmployeeId());
        GetEmployeeDtoResult getEmployeeDtoResult = employeeProviderService.getEmployeeDto(getEmployeeDtoArg);
        if (Objects.isNull(getEmployeeDtoResult)) {
            log.warn("saveOperation param getEmployeeDtoResult is null.");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        dingMappingEmployeeResult.setEmployeeName(getEmployeeDtoResult.getEmployeeDto().getName());
        dingMappingEmployeeResult.setEmployeePhone(getEmployeeDtoResult.getEmployeeDto().getMobile());
        dingMappingEmployeeResult.setEmployeeStatus(getEmployeeDtoResult.getEmployeeDto().getStatus().getValue());
        dingMappingEmployeeResult.setGender(getEmployeeDtoResult.getEmployeeDto().getGender());
        dingMappingEmployeeResult.setBindStatus(EmpStatusEnum.BIND.getStatus());
        return dingMappingEmployeeManager.saveOperation(dingMappingEmployeeResult, appId);
    }

    @Override
    public Result<Integer> createFxEmployee(EmployeeVo vo, String appId) {
        log.info("createFxEmployee,vo={}",vo);
        //获取企业mobileStatus
        GetConfigDto.Argument argument = new GetConfigDto.Argument();
        String key = EnterpriseConfigKey.CONFIG_KEY_NEW_EMP_MOBILE_SETTING.getKey();
        argument.setKey(key);
        argument.setEmployeeId(CURRENT_EMPLOYEE_ID);
        argument.setCurrentEmployeeId(CURRENT_EMPLOYEE_ID);
        argument.setEnterpriseId(vo.getEi());
        GetConfigDto.Result configResult = enterpriseConfigService.getConfig(argument);
        String configValue = configResult.getValue();

        CreateEmployeeArg arg = new CreateEmployeeArg();
        arg.setName(vo.getName());
        arg.setFullName(vo.getName());
        arg.setGender(StringUtils.isNotEmpty(vo.getGender()) ? vo.getGender() : "F");
        arg.setMobile(vo.getMobile());
        arg.setTelephone(vo.getMobile());
        arg.setCurrentEmployeeId(CURRENT_EMPLOYEE_ID);
        arg.setDepartmentIds(new ArrayList<>());
        arg.setEnterpriseId(vo.getEi());
        arg.setMobileStatus(MobileStatus.valueOf(Integer.valueOf(configValue)));
        arg.setAccount(vo.getMobile());
        //平台新增规则，新建员工一定需要主属部门，默认为待分配
        arg.setMainDepartmentId(999998);

        //支持同步入职日期字段
        if(vo.getHiredDate()!=null) {
            String hiredDate = DateUtils.dateToString(vo.getHiredDate());
            arg.setHireDate(hiredDate);
        }

        CreateEmployeeResult result = null;
        Integer count = 0;
        DingSyncApi syncApi = new DingSyncApi();
        syncApi.setApiName(EMP_OBJ);
        syncApi.setSyncDirection(2);
        DingSyncLogDO kcSyncLogDO = DynamicParamUtil.buildSyncLogDO(vo.getEi(), syncApi, vo.getName(), OPERATOR, SyncTypeEnum.CALL_BACK_SYNC.getType(), OperationTypeEnum.ADD.getType());
        String detail = "dingding：create apiName[{" + EMP_OBJ + "}], [" + vo.getName() + "]";
        String request = null;
        String response = null;
        try {
            result = employeeAdapterService.createEmployee(arg);
            if (Objects.isNull(result) || Objects.isNull(result.getEmployee())) {
                String content = String.format("[%s] 创建纷享员工异常，数据流向为[%s]", vo.getName(), DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
                DynamicParamUtil.supplementLogDO(kcSyncLogDO, detail, null, null, content, OperationStatusEnum.SYNC_FAIL.getStatus(), 1, 0, 1, SyncLogStatusEnum.NO_RECOVER.getStatus());
                dingSyncLogManager.logDingSync(kcSyncLogDO);
                log.warn("创建纷享职员失败,arg=[{}],CreateEmployee.Result = [{}].", argument, result);
                return Result.newError(ResultCode.MODIFY_FXEMP_FAILED);
            } else {
                request = new Gson().toJson(argument);
                response = new Gson().toJson(result);
                String content = String.format("[%s] 创建纷享员工成功，数据流向为[%s]", vo.getName(), DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
                DynamicParamUtil.supplementLogDO(kcSyncLogDO, detail, request, response, content, OperationStatusEnum.SYNC_SUCCESS.getStatus(), 1, 1, 0, SyncLogStatusEnum.SUCCESS.getStatus());
                log.info("[" + vo.getEi() + "]:createFxEmployee apiName[{}], result=[{}].", EMP_OBJ, result);
                dingSyncLogManager.logDingSync(kcSyncLogDO);
            }
        } catch (OrganizationException e) {
            String content = String.format("[%s] 创建纷享员工异常，数据流向为[%s]", vo.getName(), DynamicParamUtil.convertDirection(syncApi.getSyncDirection()));
            DynamicParamUtil.supplementLogDO(kcSyncLogDO, e.getMessage(), null, null, content, OperationStatusEnum.SYNC_FAIL.getStatus(), 1, 0, 1, SyncLogStatusEnum.NO_RECOVER.getStatus());
            dingSyncLogManager.logDingSync(kcSyncLogDO);
            log.warn("创建纷享员工异常,arg=[{}],e=[{}].", arg, e.toString());
            Result createResult = Result.newError(ResultCode.CREATE_FXEMP_FAILED);
            createResult.setErrorDescription(e.getMessage());
            return createResult;
        }
        //创建之后建立绑定关系
        DingMappingEmployeeResult dingMappingEmployeeResult = new DingMappingEmployeeResult();
        dingMappingEmployeeResult.setEmployeeName(vo.getName());
        dingMappingEmployeeResult.setDingEmployeeName(vo.getName());
        dingMappingEmployeeResult.setEmployeePhone(vo.getMobile());
        dingMappingEmployeeResult.setDingEmployeePhone(vo.getMobile());
        dingMappingEmployeeResult.setEmployeeId(result.getEmployee().getEmployeeId());
        dingMappingEmployeeResult.setDingEmployeeId(vo.getDingEmployeeId());
        dingMappingEmployeeResult.setEmployeeStatus(1);
        dingMappingEmployeeResult.setDingEmployeeStatus(1);
        dingMappingEmployeeResult.setEi(vo.getEi());
        dingMappingEmployeeResult.setCreateBy(vo.getUpdateBy());
        dingMappingEmployeeResult.setUpdateBy(vo.getUpdateBy());
        List<DingMappingEmployeeResult> list = new ArrayList<>();
        list.add(dingMappingEmployeeResult);
        dingMappingEmployeeManager.saveMappingEmployee(list, appId);
        count++;
        return Result.newSuccess(count);
    }

    //传递部门信息创建员工
    @Override
    public Result<Integer> batchCreateFxEmployee(List<CreateCrmEmployeeVo> vo, Integer ei, String appId) {
        log.info("batchCreateFxEmployee,ei={},vo={}",ei,vo);
        //获取该ei下面的部门映射
        StopWatch stopWatch = StopWatch.create("batchCreateFxEmployee--" + ei);
        Map<Long, DeptVo> deptMap = Maps.newHashMap();
        List<DeptVo> deptByEI = dingDeptMananger.getDeptByEI(ei, appId);
        log.info("batchCreateFxEmployee,deptByEI={}",deptByEI);

        Map<Long, DeptVo> deptMaps = Maps.newHashMap();
        deptByEI.stream().forEach(item -> deptMaps.put(item.getDingDeptId(), item));
        deptMap.putAll(deptMaps);

        log.info("batchCreateFxEmployee,deptMaps={}",deptMaps);

        stopWatch.lap("getDeptInfos");
        List<DingMappingEmployeeResult> list = new ArrayList<>();
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        final String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        for (int i = 0; i < vo.size(); i++) {
            CreateCrmEmployeeVo item = vo.get(i);
            log.info("batchCreateFxEmployee,item={}",item);
            // 校验是否已存在对应的员工映射,同一个企业的员工映射只能有一个
            final String fsEmpId = outerOaEmployeeBindManager.getFsEmpIdByEaAndOutEmpId(ChannelEnum.dingding, appId, entity.getFsEa(), entity.getOutEa(), item.getDingEmployeeId());
            if (StringUtils.isNotEmpty(fsEmpId)) {
                // 已存在绑定关系,直接绑定后结束
                // 写入绑定表
                outerOaEmployeeBindManager.upsert(new OuterOaEmployeeBindEntity(IdGenerator.get(), ChannelEnum.dingding, entity.getId(), entity.getFsEa(), entity.getOutEa(), appId, fsEmpId, item.getDingEmployeeId(), BindStatusEnum.normal, System.currentTimeMillis(), System.currentTimeMillis()));
                // 修改crm对象
                objectDataManager.updateEmpData(entity, item.getDingEmployeeId());
                continue;
            }

            DeptVo dingDeptEntity = dingDeptMananger.queryByDingId(ei, item.getDingDeptId(), appId);
            log.info("batchCreateFxEmployee,dingDeptEntity={}",dingDeptEntity);
            //如果员工存在多个主属部门的时候，我们创建的时候需要通过多个主属部门递归附属部门，然后选择一个主属部门作为crm部门的信息
            DingTalkEmployeeObject dingEmp = outerOaEmployeeDataManager.findDingTalkEmployeeObject(ChannelEnum.dingding, fsEa, item.getDingEmployeeId(), appId);
            log.info("batchCreateFxEmployee,dingEmpIdList={}",dingEmp);

            List<Integer> depts = new ArrayList<>();
            UserVo userDetail = DingRequestUtil.getUserDetail(ei, tokenManager.getToken(ei, appId), item.getDingEmployeeId(), enterprise.getData().getClientIp(), Long.valueOf(enterprise.getData().getAgentId()));
            log.info("batchCreateFxEmployee,userDetail={}",userDetail);
            if (ObjectUtils.isEmpty(userDetail)) return Result.newError(ResultCode.PARAMS_ERROR);

            item.setDingDeptId(userDetail.getMainDepartment());
            item.setManagerUserid(userDetail.getManagerUserid());
            log.info("batchCreateFxEmployee,item2={}",item);

            if(StringUtils.isNotEmpty(userDetail.getMobile())
                    && StringUtils.isNotEmpty(userDetail.getStateCode())
                    && !userDetail.getStateCode().equals("86")) {
                String phone = "+" + userDetail.getStateCode() + "-" + userDetail.getMobile();
                userDetail.setMobile(phone);
                item.setMobile(phone);
            }

            if (dingDeptEntity == null || dingEmp == null) {

                if (ObjectUtils.isEmpty(dingDeptEntity)) {
                    //存在部门已经删除的状态
                    //删除已经存在的
                    dingMappingEmployeeManager.physicalDeleteByDeptId(ei, item.getDingEmployeeId(), item.getDingDeptId(), appId);
                    //更新员工信息
                    if (CollectionUtils.isNotEmpty(userDetail.getDepartment())) {
                        for (int j = 0; j < userDetail.getDepartment().size(); j++) {
                            Long temp = userDetail.getDepartment().get(j);
                            DeptVo deptVo = dingDeptMananger.queryByDingId(ei, temp, appId);
                            log.info("batchCreateFxEmployee,deptVo={}",deptVo);
                            if (ObjectUtils.isEmpty(deptVo)) continue;
                            //钉钉创建员工时，会同时发送创建以及编辑。插入失败就不需要创建员工。让成功的创建员工
                            //这里其实是锁,如果状态已经为0,修改失败,就让另一个线程处理后面的事情
                            final OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = outerOaEmployeeBindManager.queryByFsEaAndOutEmpId(ChannelEnum.dingding, fsEa, appId, userDetail.getUserid());
                            log.info("check outerOaEmployeeBindEntity exists, outerOaEmployeeBindEntity:{}", outerOaEmployeeBindEntity);
                            if (Objects.nonNull(outerOaEmployeeBindEntity)) {
                                return Result.newSuccess();
                            }
                        }
                    }
                    dingDeptEntity = dingDeptMananger.queryByDingId(ei, userDetail.getDepartment().get(0), appId);
                    log.info("batchCreateFxEmployee,dingDeptEntity={}",dingDeptEntity);
                }
            }
            dingEmp = outerOaEmployeeDataManager.findDingTalkEmployeeObject(ChannelEnum.dingding, fsEa, item.getDingEmployeeId(), appId);
            log.info("batchCreateFxEmployee,dingEmpIdList={}",dingEmp);
            List<Integer> deptIds = treeDeptIds(deptMap, dingEmp.getDeptId(), new ArrayList<>());
            depts.addAll(deptIds);
            log.info("batchCreateFxEmployee,depts={}",depts);
            log.info("batchCreateFxEmployee,deptMap2={}",deptMap);
            if (item.getDingDeptId() == null) {
                item.setDingDeptId(userDetail.getMainDepartment());
            }
            if (ObjectUtils.isNotEmpty(dingDeptEntity) && CollectionUtils.isNotEmpty(depts)) {
                log.info("batchCreateFxEmployee,dingDeptId={}",item.getDingDeptId());
                //主属部门
                item.setCrmMainDeptId(deptMap.get(item.getDingDeptId()).getCrmDeptId().toString());
                //附属部门 底层json数据需要转换成String
                List<String> viceDepts = depts.stream().map(String::valueOf).collect(Collectors.toList());
                log.info("batchCreateFxEmployee,viceDepts={}",viceDepts);
                item.setCrmViceDepts(viceDepts);
                //设置汇报对象
                //item.setLeader(dingDeptEntity.getCrmDeptOwner());
            } else {
                //没有部门的默认待分配
                item.setCrmMainDeptId(NOT_DISTRIBUTION);
            }
            String legalName = item.getName();//employeeValidName(item.getName());
            item.setName(legalName);
            item.setManagerUserid(userDetail.getManagerUserid());
            item.setEi(ei);
            if(item.getLeader() == null) {
                //取员工的直属主管
                if(StringUtils.isNotEmpty(userDetail.getManagerUserid())) {
                    //查询直属主管绑定的纷享员工，这个有可能直属主管还没有新增过去，有可能为空
                    List<DingMappingEmployeeResult> dingManagerEmpIdList = dingMappingEmployeeManager.findDingEmpIdList(ei, userDetail.getManagerUserid(), appId);
                    if(CollectionUtils.isNotEmpty(dingManagerEmpIdList)) {
                        dingManagerEmpIdList.forEach(v -> {
                            if(v.getEmployeeId() != null) {
                                item.setLeader(v.getEmployeeId());
                            }
                        });
                    }
                }
            }
            if(item.getEmployeeNumber() == null) {
                item.setEmployeeNumber(userDetail.getJobnumber());
            }
            if(item.getEmail() == null) {
                item.setEmail(userDetail.getEmail());
            }
            if(item.getSexType() == null) {
                item.setSexType(userDetail.getSexType());
            }
            log.info("batchCreateFxEmployee,createEmp,item={}",item);

            final DingTalkEmployeeObject employeeData = convert2DingTalkEmployeeObject(item, legalName);
            outerOaEmployeeDataManager.batchUpsert(Lists.newArrayList(employeeData), ChannelEnum.dingding, entity.getId());

            com.facishare.open.outer.oa.connector.common.api.result.Result<ActionAddResult> createResult = objectDataManager.createEmployee(entity, employeeData.getUserid());
            log.info("batchCreateFxEmployee,createEmp,createResult={}",createResult);
            Integer empId = Optional.ofNullable(createResult.getData())
                    .map(ActionAddResult::getObjectData)
                    .map(objectData -> objectData.getInt("user_id"))
                    .orElse(null);
            //如果手机同号则自动匹配。
            //按照现有的错误，去掉错误的数据重试
            for (int j = 0; j < 4&&createResult.getCode() != ResultCode.SUCCESS.getErrorCode(); j++) {
                if (createResult.getCode() == ResultCode.EMPLOYEE_MOBILE_EXIST.getErrorCode()) {
                    //通过手机号搜索员工
                    Object result = crmRestManager.queryByPhone(ei, "phone", item.getMobile()).getData().get("user_id");
                    if (ObjectUtils.isNotEmpty(result)) empId = Integer.parseInt(result.toString());
                    //修改员工
                    item.setCrmEmpId(empId);
                    outerOaEmployeeBindManager.upsert(new OuterOaEmployeeBindEntity(IdGenerator.get(), ChannelEnum.dingding, entity.getId(), entity.getFsEa(), entity.getOutEa(), appId, String.valueOf(empId), item.getDingEmployeeId(), BindStatusEnum.normal, System.currentTimeMillis(), System.currentTimeMillis()));
                    final com.facishare.open.outer.oa.connector.common.api.result.Result<IncrementUpdateResult> updateResultResult = objectDataManager.updateEmpData(entity, item.getDingEmployeeId());
                    createResult = new com.facishare.open.outer.oa.connector.common.api.result.Result<>(updateResultResult.getCode(), updateResultResult.getMsg(), null);
                }
                if (createResult.getCode() == ResultCode.EMPLOYEE_NAME_IS_EXIST.getErrorCode()) {
                    //同名处理
                    String phone = item.getMobile();
                    String suffix = StringUtils.isNotEmpty(phone) ? phone.substring(phone.length() - 4, phone.length()) : String.valueOf(Math.round((Math.random() + 1) * 1000));
//                    item.setName(employeeValidName(item.getName().concat(suffix)));
                    item.setName(item.getName().concat(suffix));
                    updateEmployeeName(appId, entity, item.getDingEmployeeId(), item.getName());
                    createResult = objectDataManager.createEmployee(entity, item.getDingEmployeeId());
                    if (createResult.isSuccess()) empId = createResult.getData().getObjectData().getInt("user_id");
                }
//                if(ResultCode.LEADER_NOT_EXISTS_CODE.getErrorCode().equals(createResult.getCode())){
//                    item.setLeader(null);
//                    createResult = crmRestManager.createEmp(item);
//                    if (createResult.isSuccess()) {
//                        empId = createResult.getData();
//                    }
//                }
                if(ResultCode.DEPT_NOT_EXISTS_CODE.getErrorCode().equals(createResult.getCode())){
                    item.setCrmMainDeptId(null);
                    item.setCrmViceDepts(null);
                    employeeData.setDeptId(null);
                    outerOaEmployeeDataManager.batchUpsert(Lists.newArrayList(employeeData), ChannelEnum.dingding, entity.getId());
                    createResult = objectDataManager.createEmployee(entity, item.getDingEmployeeId());
                    if (createResult.isSuccess()) empId = createResult.getData().getObjectData().getInt("user_id");
                }
            }

            //加入日志
            Integer statusCode = createResult.getCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
            LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.UPDATE.getType(), item.getName(), statusCode, createResult.getMsg());
            logManager.writeLog(logWriteVo, "EmpObj");
            if (ObjectUtils.isEmpty(empId))
                return Result.newError(createResult.getCode(), createResult.getMsg());

            stopWatch.lap("createEmployee");
//            //创建之后建立绑定关系
//            DingMappingEmployeeResult dingMappingEmployeeResult = new DingMappingEmployeeResult();
//            dingMappingEmployeeResult.setEmployeeName(item.getName());
//            dingMappingEmployeeResult.setDingEmployeeName(item.getName());
//            dingMappingEmployeeResult.setEmployeePhone(item.getMobile());
//            dingMappingEmployeeResult.setDingEmployeePhone(item.getMobile());
//            //兼容一下已经在crm过的部门
//            dingMappingEmployeeResult.setEmployeeId(empId);
//            dingMappingEmployeeResult.setDingEmployeeId(item.getDingEmployeeId());
//            dingMappingEmployeeResult.setEmployeeStatus(1);
//            dingMappingEmployeeResult.setDingEmployeeStatus(1);
//            dingMappingEmployeeResult.setEi(ei);
//            dingMappingEmployeeResult.setDingJobNumber(item.getEmployeeNumber());
//            dingMappingEmployeeResult.setDingEmployeeEmail(item.getEmail());
//            dingMappingEmployeeResult.setDingSexType(item.getSexType());
//            dingMappingEmployeeResult.setCreateBy(userId);
//            dingMappingEmployeeResult.setUpdateBy(userId);
//            dingMappingEmployeeResult.setBindStatus(EmpStatusEnum.BIND.getStatus());
//            dingMappingEmployeeResult.setEmployeeId(empId);
//            list.add(dingMappingEmployeeResult);
//            //批量更新所属的中间表的映射关系
//            log.info("objectMapping service saveMappingEmployee:{}", dingMappingEmployeeResult);
//            //TODO 如果所属部门没有映射，会出现绑定失败
//            dingEmpIdList.forEach(tempDept -> {
//                if (deptMap.get(tempDept.getDingDeptId()) != null) {
//                    dingMappingEmployeeResult.setDingDeptId(tempDept.getDingDeptId());
//                    dingMappingEmployeeResult.setCrmDeptId(deptMap.get(tempDept.getDingDeptId()).getCrmDeptId().intValue());
//                }
//                log.info("objectMapping service dingMappingEmployeeResult:{}", dingMappingEmployeeResult);
//                //区别于其他保存，这个基于dingDept
//                dingMappingEmployeeManager.saveMappingEmployee(Lists.newArrayList(dingMappingEmployeeResult), appId);
//            });
        }
        stopWatch.lap("dbSaveEmployee");
        stopWatch.log();
        return Result.newSuccess();
    }

    @NotNull
    private static DingTalkEmployeeObject convert2DingTalkEmployeeObject(CreateCrmEmployeeVo item, String legalName) {
        DingTalkEmployeeObject employeeData = new DingTalkEmployeeObject();
        employeeData.setStatus(0);
        employeeData.setJobNumber(item.getEmployeeNumber());
        employeeData.setManager_userid(item.getManagerUserid());
        employeeData.setPosition(item.getPosition());
        employeeData.setEmail(item.getEmail());
        employeeData.setSexType(String.valueOf(item.getSexType()));
        employeeData.setUnionId(item.getUnionid());
        employeeData.setUserid(item.getDingEmployeeId());
        employeeData.setName(legalName);
        employeeData.setPhone(item.getMobile());
        employeeData.setDeptId(item.getDingDeptId());
        return employeeData;
    }

    private void updateEmployeeName(String appId, OuterOaEnterpriseBindEntity enterpriseBindEntity, String userId, String name) {
        final OuterOaEmployeeDataEntity dataEntity = outerOaEmployeeDataManager.queryByChannelAndAppIdAndEmpId(ChannelEnum.dingding, appId, enterpriseBindEntity.getOutEa(),  userId);
        final DingTalkEmployeeObject employeeObject = dataEntity.getOutUserInfo().toJavaObject(DingTalkEmployeeObject.class);
        employeeObject.setName(name);
        outerOaEmployeeDataManager.batchUpsert(Lists.newArrayList(employeeObject), ChannelEnum.dingding, enterpriseBindEntity.getId());
    }

    //递归获取附属部门
    public List<Integer> treeDeptIds(Map<Long, DeptVo> deptMaps, Long dingDeptId, List<Integer> ids) {
//        List<Long> ids = new ArrayList<>();
        if ((null != dingDeptId) && (deptMaps.get(dingDeptId) != null)) {
            Integer crmDeptId = deptMaps.get(dingDeptId).getCrmDeptId();
            Long dingDeptID = deptMaps.get(dingDeptId).getDingParentId();
            ids.add(crmDeptId.intValue());
            treeDeptIds(deptMaps, dingDeptID, ids);
        }
        return ids;
    }

    @Override
    public Result<Void> stopFxEmployee(Integer ei, Integer fxEmpId, String fxEmpName, String appId, String dingUserId) {
        final String ea = eieaConverter.enterpriseIdToAccount(ei);
        final OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, ea, appId);
        final com.facishare.open.outer.oa.connector.common.api.result.Result<Void> stopResult = objectDataManager.removeEmpData(entity, dingUserId, RemoveEmployeeEventType.RESIGN_EMPLOYEE);
        Integer statusCode = stopResult.getCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
        LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.DELETE.getType(), fxEmpName, statusCode, stopResult.getMsg());
        logManager.writeLog(logWriteVo, "EmpObj");
        return Result.newError(stopResult.getCode(), stopResult.getMsg());
    }

    /**
     * 钉钉员工变动后，更新绑定关系表以及绑定的纷享员工
     *
     * @param ei
     * @param userIds
     * @param appId
     * @return
     */
    @Override
    public Result<Void> updateEmp(Integer ei, List<String> userIds, String appId) {
        StopWatch stopWatch = StopWatch.create("updateEmp");
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseInfoByEi(ei, appId);
        if (!enterprise.isSuccess() || ObjectUtils.isEmpty(enterprise)) {
            log.warn("updateDingEmp:the enterprise not bind, ei=[{}].", ei);
            return Result.newError(enterprise.getErrorCode(), enterprise.getErrorMessage());
        }
        String accessToken = DingRequestUtil.getToken(ei, enterprise.getData().getClientIp(), enterprise.getData().getAppKey(), enterprise.getData().getAppSecret());

        if (StringUtils.isEmpty(accessToken)) {
            log.warn("updateDingEmp:the enterprise authorization failure, ei=[{}].", ei);
            return Result.newError(ResultCode.TOKEN_NOT_EXIST);
        }
        //对接完成，但还没有进行初始化同步的时候，钉钉人员新增账户或修改信息后，这里同步进去，会导致初始化同步失败，这里先判断是否为空，如果为空，则return
        Integer count = dingMappingEmployeeManager.getEmpAllCount(ei, appId);
        if(count == null || count == 0) {
            log.warn("updateDingEmp:the enterprise not first bind, ei=[{}].", ei);
            return Result.newError(ResultCode.INIT_DING_EMP);
        }
        //查询集成模式是1：同步员工 2：组织架构
        if (enterprise.getData().getDevModel().equals(Constant.SYNC_MODELONE)) {
            syncEmployeeFir(ei, userIds, enterprise, appId);
        } else {
            syncEmployeeSec(ei, userIds, accessToken, enterprise.getData().getClientIp(), enterprise.getData(), appId);
        }
        stopWatch.lap("updateEmp");
        stopWatch.log();
        return Result.newSuccess();
    }

    /**
     * 同步模式1
     */
    public Result<Void> syncEmployeeFir(Integer ei, List<String> userIds, Result<DingEnterpriseResult> enterprise, String appId) {
        for (String userId : userIds) {
            //查询用户详情
            Boolean isQPSLimit = qpsLimitHandlerFromProvider.isQPSLimitByEa(eieaConverter.enterpriseIdToAccount(ei));
            if(isQPSLimit) {
                //限流且重试多次失败
                log.info("ObjectMappingServiceImpl.syncEmployeeFir,query user detail failed.qpsLimit.ei={},userId={}", ei, userId);
                return new Result<>();
            }
            User user = DingRequestUtil.getUser(ei, enterprise.getData().getClientIp(), enterprise.getData().getAppKey(),
                    enterprise.getData().getAppSecret(), userId, enterprise.getData().getToken(), tokenManager.getToken(ei, appId), Long.valueOf(enterprise.getData().getAgentId()));
            if (ObjectUtils.isEmpty(user)) {
                log.warn("get dingding user result is null, ei={}, userId={}.", ei, userId);
               continue;
            }
            String name = user.getName();

            String phone = user.getMobile();
            if(StringUtils.isNotEmpty(user.getMobile())
                    && StringUtils.isNotEmpty(user.getStateCode())
                    && !user.getStateCode().equals("86")) {
                phone = "+" + user.getStateCode() + "-" + user.getMobile();
            }
//            int status = userGetResponse.getActive() ? 1 : 2;
//            final Dept deptDetail = DingRequestUtil.getDeptDetail(tokenManager.getToken(ei, appId), enterprise.getData().getClientIp(), user.getMainDepartment());
            //修改绑定关系表
            // 模式一就不管部门了
            DingMappingEmployeeResult dingMappingEmployee = new DingMappingEmployeeResult();
            dingMappingEmployee.setDingEmployeePosition(user.getPosition());
            dingMappingEmployee.setDingDeptId(user.getMainDepartment());
//            dingMappingEmployee.setDingDeptName(Objects.nonNull(deptDetail) ? deptDetail.getName() : null);
            dingMappingEmployee.setEi(enterprise.getData().getEi());
            dingMappingEmployee.setDingEmployeeId(userId);
            dingMappingEmployee.setDingUnionId(user.getUnionid());
            dingMappingEmployee.setDingEmployeeName(name);
            dingMappingEmployee.setDingEmployeePhone(phone);
            dingMappingEmployee.setDingEmployeeStatus(1);
            dingMappingEmployee.setDingEmployeeEmail(user.getEmail());
            dingMappingEmployee.setDingJobNumber(user.getJobnumber());
            dingMappingEmployee.setManagerUserid(user.getManagerUserid());
            dingMappingEmployee.setDingSexType(user.getSexType());
            dingMappingEmployee.setCreateBy(OPERATOR);
            dingMappingEmployee.setUpdateBy(OPERATOR);
            DingMappingEmployeeResult mappingEmp = dingMappingEmployeeManager.findIsBindByDingId(dingMappingEmployee, appId);
            int bindStatus = 0;
            Integer fxEmpId = 0;
            if (Objects.isNull(mappingEmp)) {
                dingMappingEmployeeManager.insertDingEmp(dingMappingEmployee, appId);
                //新增账号时自动同步员工
                log.info("ObjectMappingServiceImpl.syncEmployeeFir,autoSaveDingEmployeeAccount,ei={},userId={}", ei, userId);
                autoSaveDingEmployeeAccount(ei, Lists.newArrayList(userId), appId);
            } else {
                bindStatus = mappingEmp.getBindStatus();
                fxEmpId = mappingEmp.getEmployeeId();
                dingMappingEmployeeManager.updateDingEmpByDingId(dingMappingEmployee, appId);
            }
            //修改纷享员工
            //在灰度项控制的企业不用更新
            if (bindStatus == EmpStatusEnum.BIND.getStatus() && !ConfigCenter.NOT_UPDATE_EA.contains(enterprise.getData().getEa())) {
                final OuterOaEnterpriseBindEntity enterpriseBind = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, eieaConverter.enterpriseIdToAccount(ei), appId);
                objectDataManager.updateEmpData(enterpriseBind, userId);
            }
        }
        return Result.newSuccess();
    }

    /**
     * 同步模式2
     */
    public Result<Void> syncEmployeeSec(Integer ei, List<String> userIds, String accessToken, String clientIp, DingEnterpriseResult enterpriseResult, String appId) {
        StopWatch stopWatch = StopWatch.create("syncEmployeeSec" + ei);

        final String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        for (String userId : userIds) {
            // 上个分布式锁 redis
            String lockFormat = String.format(APP_EMP_LOCK, ei, userId);
            String uuid = UUID.randomUUID().toString();
            if (RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), lockFormat, uuid, 15)) {
                //查询用户详情
                Boolean isQPSLimit = qpsLimitHandlerFromProvider.isQPSLimitByEa(eieaConverter.enterpriseIdToAccount(ei));
                if(isQPSLimit) {
                    //限流且重试多次失败
                    log.info("ObjectMappingServiceImpl.syncEmployeeSec,query user detail failed.qpsLimit.ei={},userId={}", ei, userId);
                    return new Result<>();
                }
                UserVo user = DingRequestUtil.getUserDetail(ei, accessToken, userId, clientIp, Long.valueOf(enterpriseResult.getAgentId()));
                if (ObjectUtils.isEmpty(user) || CollectionUtils.isEmpty(user.getDepartment())) {
                    log.warn("get dingding user result is null, ei={}, userId={}.", ei, userId);
                    return Result.newError(ResultCode.GET_DING_EMP_FAILED);
                }

                if(StringUtils.isNotEmpty(user.getMobile())
                        && StringUtils.isNotEmpty(user.getStateCode())
                        && !user.getStateCode().equals("86")) {
                    String phone = "+" + user.getStateCode() + "-" + user.getMobile();
                    user.setMobile(phone);
                }

                stopWatch.lap("queryUser");
                User item = new User();
                BeanUtils.copyProperties(user, item);
                List<User> users = Lists.newArrayList();
                users.add(item);
                final OuterOaEmployeeBindEntity mappingEmp = outerOaEmployeeBindManager.queryNormalByFsEaAndOutEmpId(ChannelEnum.dingding, fsEa, appId, userId);
                Integer fxEmpId = 0;
                if (Objects.isNull(mappingEmp)) {
                    //根据钉钉部门ID查询crm
                    //如果客户存在多个主属部门，应该插入多条记录
                    if (CollectionUtils.isNotEmpty(user.getDepartment())) {

                        for (int i = 0; i < user.getDepartment().size(); i++) {
                            Long temp = user.getDepartment().get(i);
                            DeptVo deptVo = dingDeptMananger.queryByDingId(ei, temp, appId);
                            if (ObjectUtils.isEmpty(deptVo)) {
                                log.warn("ObjectMappingServiceImpl.syncEmployeeSec,部门没有绑定,所以不同步人员信息,ei={},userId={},temp={} appId:{}", ei, userId, temp, appId);
                                continue;
                            }
                            Integer count = dingMappingEmployeeManager.insertModeEmployee(users, ei, deptVo.getDingDeptId(), Constant.SYSTEM_MANAGER, deptVo.getName(), deptVo.getCrmDeptId(), appId);
                            log.info("insert dingMapping:{}", count);
                            //钉钉创建员工时，会同时发送创建以及编辑。插入失败就不需要创建员工。让成功的创建员工
                            if (count == 0) continue;
                        }
                    }
                    // 再查一次数据,如果还是没有,说明不在可见范围或者没有主属部门,或者只在1部门
                    final OuterOaEmployeeDataEntity dataEntity = outerOaEmployeeDataManager.queryByChannelAndAppIdAndEmpId(ChannelEnum.dingding, appId, entity.getOutEa(), userId);
                    if (ObjectUtils.isEmpty(dataEntity)) {
                        log.warn("ObjectMappingServiceImpl.syncEmployeeSec,dingding user not in crm.ei={},userId={} deptIds={}", ei, userId, user.getDepartment());
                        continue;
                    }

                    stopWatch.lap("insert model");
                    //在CRM创建员工
                    CreateCrmEmployeeVo vo = new CreateCrmEmployeeVo();
                    BeanUtils.copyProperties(user, vo);
                    vo.setDingEmployeeId(user.getUserid());
                    vo.setDingDeptId(user.getMainDepartment());
                    vo.setEmployeeNumber(user.getJobnumber());
                    vo.setSexType(user.getSexType());
                    List<CreateCrmEmployeeVo> voList = Lists.newArrayList();
                    voList.add(vo);
                    log.info("createFxEmployee:volist:{},ei:{}", voList, ei);
                    batchCreateFxEmployee(voList, ei, appId);
                    stopWatch.lap("batchCreateFxEmployee");
                } else {
//                    BindStatusEnum bindStatus = mappingEmp.getBindStatus();
//                    if (bindStatus == ) {
//                        //避免出现未绑定的员工出现获取employeeId未空的情况
//                        CreateCrmEmployeeVo vo = new CreateCrmEmployeeVo();
//                        BeanUtils.copyProperties(user, vo);
//                        vo.setDingEmployeeId(user.getUserid());
//                        vo.setDingDeptId(user.getMainDepartment());
//                        vo.setEmployeeNumber(user.getJobnumber());
//                        vo.setSexType(user.getSexType());
//                        List<CreateCrmEmployeeVo> voList = Lists.newArrayList();
//                        voList.add(vo);
//                        batchCreateFxEmployee(voList, ei, appId);
//                        return Result.newSuccess();
//                    }
                    fxEmpId = Integer.valueOf(mappingEmp.getFsEmpId());

                    //根据所属的user返回的主属部门，修改信息
                    DeptVo deptVo = dingDeptMananger.queryByDingId(ei, user.getMainDepartment(), appId);
                    String crmDept = ObjectUtils.isEmpty(deptVo) ? NOT_DISTRIBUTION : deptVo.getCrmDeptId().toString();
                    List<DeptVo> deptByEI = dingDeptMananger.getDeptByEI(ei, appId);
                    HashMap<Long, DeptVo> deptMap = Maps.newHashMap();
                    deptByEI.stream().forEach(itemCrmDept -> deptMap.put(itemCrmDept.getDingDeptId(), itemCrmDept));
                    //附属部门的信息
                    List<Integer> viceDepts = new ArrayList<>();
                    user.getDepartment().stream().forEach(itemDept -> {
                        List<Integer> deptIds = treeDeptIds(deptMap, itemDept, new ArrayList<>());
                        viceDepts.addAll(deptIds);
                    });
                    //TODO 递归太耗时，直接调用api接口获取对应的部门的父级部门

                    log.info("modify fxemp vicedept:{},ei:{},userid:{}", viceDepts, ei, userId);
                    CreateCrmEmployeeVo vo = new CreateCrmEmployeeVo();
                    vo.setUnionid(user.getUnionid());
//                    vo.setGender(String.valueOf(user.getSexType()));
                    vo.setDingEmployeeId(user.getUserid());
                    vo.setDingDeptId(user.getMainDepartment());
                    vo.setManagerUserid(user.getManagerUserid());
                    vo.setEi(ei);
                    vo.setCrmEmpId(fxEmpId);
                    vo.setCrmMainDeptId(crmDept);
                    //附属部门 底层json数据需要转换成String
                    List<String> nearDepts = viceDepts.stream().map(String::valueOf).collect(Collectors.toList());
                    vo.setCrmViceDepts(nearDepts);
                    final String legalName = validName(user.getName());
                    vo.setName(legalName);
                    vo.setManagerUserid(user.getManagerUserid());
                    vo.setPosition(user.getPosition());
                    vo.setEmail(user.getEmail());
                    vo.setEmployeeNumber(user.getJobnumber());
                    vo.setSexType(user.getSexType());
                    vo.setMobile(user.getMobile());
                    if(StringUtils.isNotEmpty(user.getManagerUserid())) {
                        List<DingMappingEmployeeResult> dingManagerEmpIdList = dingMappingEmployeeManager.findDingEmpIdList(ei, user.getManagerUserid(), appId);
                        if(CollectionUtils.isNotEmpty(dingManagerEmpIdList)) {
                            dingManagerEmpIdList.forEach(v -> {
                                if(v.getEmployeeId() != null) {
                                    vo.setLeader(v.getEmployeeId());
                                }
                            });
                        }
                    }

                    final DingTalkEmployeeObject employeeData = convert2DingTalkEmployeeObject(vo, legalName);
                    outerOaEmployeeDataManager.batchUpsert(Lists.newArrayList(employeeData), ChannelEnum.dingding, entity.getId());

                    com.facishare.open.outer.oa.connector.common.api.result.Result<IncrementUpdateResult> modifyResult = objectDataManager.updateEmpData(entity, userId);

                    if (modifyResult.getCode() == ResultCode.EMPLOYEE_NAME_IS_EXIST.getErrorCode()) {
                        //手机号不同的情况，用手机号后4位 Math.round((Math.random()+1) * 1000)
                        String phone = user.getMobile();
                        String suffix = StringUtils.isNotEmpty(phone) ? phone.substring(phone.length() - 4, phone.length()) : String.valueOf(Math.round((Math.random() + 1) * 1000));
                        vo.setName(item.getName().concat(suffix));
                        employeeData.setName(item.getName().concat(suffix));
                        outerOaEmployeeDataManager.batchUpsert(Lists.newArrayList(employeeData), ChannelEnum.dingding, entity.getId());
                        modifyResult = objectDataManager.updateEmpData(entity, userId);
                    }

                    Integer statusCode = modifyResult.getCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
                    LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.UPDATE.getType(), vo.getName(), statusCode, modifyResult.getMsg());
                    logManager.writeLog(logWriteVo, "EmpObj");
                    updateDingMapping(ei, userId, user, deptMap, fxEmpId, appId);
                }

            }
            RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), userId, uuid);
        }
        return Result.newSuccess();

    }

    private void updateDingMapping(Integer ei, String dingUserId, UserVo user, HashMap<Long, DeptVo> deptMap, Integer fxEmpId, String appId) {
        //根据dingEmpID查询数据，如果不在user.getDept中的数据删除，没有的插入，有的更新
        List<DingMappingEmployeeResult> mappingData = dingMappingEmployeeManager.findDingEmpIdList(ei, dingUserId, appId);
        if(CollectionUtils.isEmpty(mappingData)){
            return;
        }
        Map<Long, DingMappingEmployeeResult> maps = mappingData.stream().collect(Collectors.toMap(DingMappingEmployeeResult::getDingDeptId, Function.identity(), (key1, key2) -> key2));

        user.getDepartment().stream().forEach(itemUpdate -> {
            DeptVo deptVo = Optional.ofNullable(deptMap.get(itemUpdate)).orElseGet(() -> new DeptVo());
            DingMappingEmployeeResult dingMappingEmployee = new DingMappingEmployeeResult();
            dingMappingEmployee.setManagerUserid(user.getManagerUserid());
            dingMappingEmployee.setEi(ei);
            dingMappingEmployee.setDingEmployeeId(dingUserId);
            dingMappingEmployee.setDingUnionId(user.getUnionid());
            dingMappingEmployee.setDingEmployeeName(user.getName());
            dingMappingEmployee.setEmployeeName(validName(user.getName()));
            dingMappingEmployee.setEmployeeId(fxEmpId);
            dingMappingEmployee.setDingEmployeePosition(user.getPosition());
            dingMappingEmployee.setDingEmployeeEmail(user.getEmail());
            dingMappingEmployee.setDingJobNumber(user.getJobnumber());
            dingMappingEmployee.setManagerUserid(user.getManagerUserid());
            dingMappingEmployee.setEmployeePhone(user.getMobile());
            dingMappingEmployee.setDingEmployeePhone(user.getMobile());
            dingMappingEmployee.setDingEmployeeStatus(1);
            dingMappingEmployee.setDingDeptId(itemUpdate);
            dingMappingEmployee.setDingSexType(user.getSexType());
            dingMappingEmployee.setCreateBy(OPERATOR);
            dingMappingEmployee.setUpdateBy(OPERATOR);
            dingMappingEmployee.setCrmDeptId(deptVo.getCrmDeptId());
            dingMappingEmployee.setBindStatus(EmpStatusEnum.BIND.getStatus());
            dingMappingEmployee.setDingDeptName(deptVo.getName());
            if (ObjectUtils.isNotEmpty(maps.get(itemUpdate))) {
                //更新
                dingMappingEmployeeManager.updateModelEmp(dingMappingEmployee, appId);
                log.info("objectMapping update dingMapping service ei:{},arg:{}", ei, dingMappingEmployee);
                maps.remove(itemUpdate);
            } else {
                //插入
                dingMappingEmployeeManager.insertAllModelData(Lists.newArrayList(dingMappingEmployee), ei, appId);
                log.info("objectMapping insert dingMapping service ei:{},arg:{}", ei, dingMappingEmployee);
                maps.remove(itemUpdate);
            }
        });
        if (CollectionUtils.isNotEmpty(maps.keySet())) {
            //删除中间表的数据
            maps.keySet().stream().forEach(itemDelete -> {
                log.info("objectMapping delete dingMapping service ei:{},userid:{},arg:{}", ei, dingUserId, itemDelete);
                dingMappingEmployeeManager.deleteByDeptId(ei, dingUserId, itemDelete, appId);
            });
        }
    }


    /**
     * 钉钉删除员工，同步停用纷享员工，解绑（删除）绑定关系
     *
     * @param
     * @param dingUserIds
     * @param appId
     * @return
     */
    @Override
    public Result<Void> stopEmp(Integer ei, List<String> dingUserIds, String appId) {
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        if (!enterprise.isSuccess()) {
            log.warn("stopDingEmp:the enterprise not bind, ei=[{}].", ei);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }

        //在灰度项控制的企业不用更新
        if(ConfigCenter.NOT_UPDATE_EA.contains(enterprise.getData().getEa())) {
            log.info("objectMappingServiceImpl.stopEmp,is not stop.ea={}", enterprise.getData().getEa());
            return new Result<>();
        }

        for (String dingUserId : dingUserIds) {
            DingMappingEmployeeResult dingMappingEmployee = new DingMappingEmployeeResult();
            dingMappingEmployee.setEi(enterprise.getData().getEi());
            dingMappingEmployee.setDingEmployeeId(dingUserId);
            //停用纷享员工
            DingMappingEmployeeResult mappingEmp = dingMappingEmployeeManager.findIsBindByDingId(dingMappingEmployee, appId);
            if (Objects.nonNull(mappingEmp) && Objects.nonNull(mappingEmp.getEmployeeId())) {
                Integer fxEmpId = mappingEmp.getEmployeeId();
                String fxEmpName = mappingEmp.getEmployeeName();
                stopFxEmployee(ei, fxEmpId, fxEmpName, appId, dingUserId);
            }

        }
        return Result.newSuccess();
    }

    @Override
    public Result<DingMappingEmployeeResult> queryEmployeeByUnionId(Integer ei, String unionId, String appId) {
        return dingMappingEmployeeManager.queryEmpByUnionId(ei, unionId, appId);
    }

    @Override
    public Result<DingMappingEmployeeResult> queryEmpByDingUserId(Integer ei, String dingUserId, String appId) {
        return dingMappingEmployeeManager.queryEmpByDingUserId(ei, appId, dingUserId);
    }

    /**
     * 批量查询已绑定的纷享员工，需要同步待办消息
     *
     * @param offset
     * @param limit
     * @return
     */
    @Override
    public Result<List<BindFxUserResult>> getBindEiAndUser(Integer offset, Integer limit, String appId) {
        if (Objects.isNull(offset) || Objects.isNull(limit)) {
            log.warn("getBindEiAndUser param is null");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (limit > 1000) {
            log.warn("query count is out of limit, maximum is 1000");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        return dingMappingEmployeeManager.queryBindFxEmp(offset, limit, appId);
    }

    @Override
    public Result<List<BindFxEaResult>> getBindEa(Integer offset, Integer limit, String appId) {
        if (Objects.isNull(offset) || Objects.isNull(limit)) {
            log.warn("getBindEa param is null");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (limit > 1000) {
            log.warn("query count is out of limit, maximum is 1000");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        return dingEnterpriseManager.queryBindFxEa(offset, limit, appId);
    }

    @Override
    public Result<ConditionEmployeeResult> conditionEmployee(QueryEmployeeVo vo, Integer ei, String appId) {
        Integer count = dingMappingEmployeeManager.conditionEmployeeCount(ei, appId, vo.getBindStatus(), vo.getDingNameOrPhone(), vo.getDingDeptId());
        List<DingMappingEmployeeResult> data = dingMappingEmployeeManager.conditionQueryEmployee(ei, appId, vo.getBindStatus(), vo.getPageNumber(), vo.getPageSize(), vo.getDingNameOrPhone(), vo.getDingDeptId()).getData();
        ConditionEmployeeResult conditionEmployeeResult = new ConditionEmployeeResult();
        conditionEmployeeResult.setTotalCount(count);
        conditionEmployeeResult.setDataList(data);
        return Result.newSuccess(conditionEmployeeResult);
    }

    @Override
    public Result<List<DeptVo>> conditionDepts(Integer enterpriseId, String appId) {
        if (enterpriseId == null) {
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        return Result.newSuccess(dingDeptMananger.getDeptByEI(enterpriseId, appId));

    }

    //创建部门（插入表，创建crm部门)
    @Override
    @CacheInvalidate(name = "getDeptByEI", key = "#ei + '_' + #appId")
    public Result<Integer> createFxDept(Integer ei, List<Long> depts, String appId) {
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        if (enterprise.getData().getDevModel().equals(Constant.SYNC_MODELONE)) return Result.newSuccess();

        Integer allCount = 0;
        if (!enterprise.isSuccess()) {
            log.warn("updateDingEmp:the enterprise not bind, ei=[{}].", ei);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        for (Long deptID : depts) {
            //查询部门详情
            Boolean isQPSLimit = qpsLimitHandlerFromProvider.isQPSLimitByEa(eieaConverter.enterpriseIdToAccount(ei));
            if(isQPSLimit) {
                //限流且重试多次失败
                log.info("ObjectMappingServiceImpl.createFxDept,query dept detail failed.qpsLimit.ei={},deptID={}", ei, deptID);
                return new Result<>();
            }
            Dept dept = DingRequestUtil.queryDeptDetail(ei, enterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), enterprise.getData().getToken(), deptID);
            if (Objects.isNull(dept)) {
                log.warn("get dingding dept result is null, ei={}, userId={}.", ei, deptID);
                return Result.newError(ResultCode.QUERY_DEPT_DETAIL);
            }
            //创建crm部门
            //查询父级部门
            DeptVo deptVo = dingDeptMananger.queryByDingId(ei, dept.getParentid(), appId);
            log.info("query deptVo deptVo:{},ei:{}", deptVo, ei);
            // 如果上级部门不存在，则查询该部门的所有上级部门并创建
            if (ObjectUtils.isEmpty(deptVo) && dept.getParentid() != 1) {
                //查询没有创建的上级部门列表并创建
                allCount += createNotFoundDept(ei, enterprise, deptID, enterprise.getData().getEa(), appId);
            } else {
                allCount += createCrmDept(dept, ObjectUtils.isEmpty(deptVo) ? Constant.TREE_PARENT_ID : deptVo.getCrmDeptId(), ei, appId);
            }

        }
        return Result.newSuccess(allCount);
    }

    //查询所有的上级部门，没有创建的创建
    private Integer createNotFoundDept(Integer ei, Result<DingEnterpriseResult> enterprise, Long deptID, String ea, String appId) {

        List<Long> listParent = DingRequestUtil.getListParent(ei, enterprise.getData().getClientIp(), deptID, tokenManager.getToken(ei, appId));
        if (ObjectUtils.isEmpty(listParent)) {
            log.warn("get dingding dept parent list is null, ei={}, deptID={}.", ei, deptID);
            return 0;
        }
        //翻转列表
        Collections.reverse(listParent);
        Integer count = 0;
        for (int i = 0; i < listParent.size(); i++) {
            //查询列表详情，找出父级parentId
            Long deptId = listParent.get(i);
            DeptVo deptVo = dingDeptMananger.queryByDingId(ei, deptId, appId);
            if (ObjectUtils.isEmpty(deptVo)) {
                Dept deptDetail = DingRequestUtil.getDeptDetail(ei, tokenManager.getToken(ei, appId), enterprise.getData().getClientIp(), deptId);
                if(ObjectUtils.isNotEmpty(deptDetail)){
                    DeptVo parentVo = dingDeptMananger.queryByDingId(ei, deptDetail.getParentid(), appId);
                    Integer parentId = ObjectUtils.isEmpty(parentVo) ? Constant.TREE_PARENT_ID : parentVo.getCrmDeptId();
                    Integer temp = createCrmDept(deptDetail, parentId, ei, appId);
                    count += temp;
                }

            }
        }
        return count;
    }


    private Integer createCrmDept(Dept dept, Integer parentId, Integer ei, String appId) {
        //先插入dept，避免批量更新
        DeptVo vo = new DeptVo();
        vo.setEi(ei);
        final Long deptId = dept.getId();
        vo.setDingDeptId(deptId);
        vo.setDingParentId(dept.getParentid());
        vo.setName(validName(dept.getName()));
        vo.setDingDeptOwner(dept.getDeptOwner());
        vo.setCrmDeptId(0);//避免空指针唯一索引约束失效
        vo.setCrmParentId(parentId);
        if (dingDeptMananger.addDeptByEi(vo, appId) == 0) {
            return 0;
        }
        final String ea = eieaConverter.enterpriseIdToAccount(ei);
        final String fsDeptId = outerOaDepartmentBindManager.getFsDeptIdAndAddByEaAndOutDeptId(ChannelEnum.dingding, ea, appId, String.valueOf(deptId));
        if (fsDeptId != null) {
            return 0;
        }
        //查询负责人
        Result<DingMappingEmployeeResult> queryResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, appId, dept.getDeptOwner());
        Integer crmEmployeeId = queryResult.getData() == null ? null : queryResult.getData().getEmployeeId();
        vo.setCrmDeptOwner(crmEmployeeId);
        Result<Integer> createDeptResult = crmRestManager.createDept(vo);
        Map<String, Object> sameMap = Maps.newHashMap();

        if (createDeptResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
            sameMap = fixCallBackName(ei, dept, appId);
            if (Boolean.valueOf(sameMap.get("same").toString())) {
                //创建部门
                vo.setName(sameMap.get("name").toString());
                createDeptResult = crmRestManager.createDept(vo);
                dept.setName(sameMap.get("name").toString());
            }
        }
        if (createDeptResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode() || !createDeptResult.isSuccess()) {
            //如果处理完同名的部门后，还是同名就返回 删除中间表的数据
            Integer statusCode = createDeptResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
            String message = createDeptResult.getErrorMessage() + "\t" + vo.toString();
            LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.ADD.getType(), vo.getName(), statusCode, message);
            logManager.writeLog(logWriteVo, "DepObj");

            return 0;
        }
        vo.setCrmDeptId(createDeptResult.getData());
        vo.setCrmParentId(parentId);
        vo.setUpdateTime(new Date());
        Result<Integer> updateDept = dingDeptMananger.updateDept(vo, appId);
        if (updateDept.getData() != 0) {
            log.info("insert ding_dept row:{},ei:{},deptVo:{}", updateDept.getData(), ei, vo);
        }
        Integer statusCode = createDeptResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
        String message = createDeptResult.getErrorMessage() + "\t" + vo.toString();
        LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.ADD.getType(), vo.getName(), statusCode, message);
        logManager.writeLog(logWriteVo, "DepObj");
        return updateDept.getData();
    }


    //更新部门（修改部门表以及crm部门)
    @Override
    public Result<Integer> modifyFxDept(Integer ei, List<Long> depts, String appId) {
        StopWatch stopWatch = StopWatch.create("modifyFxDept");
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        if (enterprise.getData().getDevModel().equals(Constant.SYNC_MODELONE)) return Result.newSuccess();
        Integer allCount = 0;
        if (!enterprise.isSuccess()) {
            log.warn("updateDingEmp:the enterprise not bind, ei=[{}].", ei);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }

        for (Long deptID : depts) {
            //加锁 同一个事件修改避免底层数据频繁修改导致数据会短时间查询失效
            String lockFormat = String.format(APP_EMP_LOCK, ei, deptID);
            String uuid = UUID.randomUUID().toString();
            if (RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), lockFormat, uuid, 15)) {

                //查询部门详情
                Boolean isQPSLimit = qpsLimitHandlerFromProvider.isQPSLimitByEa(eieaConverter.enterpriseIdToAccount(ei));
                if(isQPSLimit) {
                    //限流且重试多次失败
                    log.info("ObjectMappingServiceImpl.modifyFxDept,query dept detail failed.qpsLimit.ei={},deptID={}", ei, deptID);
                    return new Result<>();
                }
                Dept dingDept = DingRequestUtil.queryDeptDetail(ei, enterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), enterprise.getData().getToken(), deptID);
                if (Objects.isNull(dingDept)) {
                    log.warn("get dingding dept result is null, ei={}, deptId={}.", ei, deptID);
                    return Result.newSuccess();
                }
                //查询部门的时候，看有没有创建。如果没有则触发创建部门createFxDept
                DeptVo dbDeptVo = dingDeptMananger.queryByDingId(ei, deptID, appId);
                if (ObjectUtils.isEmpty(dbDeptVo)) {
                    return createFxDept(ei, Lists.newArrayList(deptID), appId);
                }
                //这边做个过滤如果判断名字是否一致，以前缀的"__"名字是否一致
                //如果上级部门变动，需要更新crm的上级部门
                DeptVo deptParent = dingDeptMananger.queryByDingId(ei, dingDept.getParentid(), appId);
                Integer parentId = Constant.TREE_PARENT_ID;
                if (ObjectUtils.isNotEmpty(deptParent)) {
                    parentId = Optional.ofNullable(deptParent.getCrmDeptId()).orElse(Constant.TREE_PARENT_ID);
                }
                //查询部门管理员
                Result<DingMappingEmployeeResult> dingMappingEmployeeResultResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, appId, dingDept.getDeptOwner());
                Integer crmEmployeeId = dingMappingEmployeeResultResult.getData() == null ? null : dingMappingEmployeeResultResult.getData().getEmployeeId();

                dbDeptVo.setName(validName(dingDept.getName()));
                dbDeptVo.setDingParentId(dingDept.getParentid());
                dbDeptVo.setDingDeptOwner(dingDept.getDeptOwner());
                dbDeptVo.setCrmParentId(parentId);
                dbDeptVo.setCrmDeptOwner(crmEmployeeId);
                Result<Void> modifyResult = crmRestManager.modifyDept(dbDeptVo);
                if (modifyResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
                            //如果部门名称已经存在
                            Map<String, Object> sameMap = fixCallBackName(ei, dingDept, appId);
                            if (Boolean.valueOf(sameMap.get("same").toString())) {
                                //如果是true，名字前缀不一致，通过index后缀修改
                                //更新部门上下级关系
                                dbDeptVo.setName(sameMap.get("name").toString());
                                modifyResult = crmRestManager.modifyDept(dbDeptVo);
                                dingDept.setName(sameMap.get("name").toString());
                            }
                        }
                        //写入日志
                        Integer statusCode = modifyResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
                        LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.UPDATE.getType(), dbDeptVo.getName(), statusCode, modifyResult.getErrorMessage());
                        logManager.writeLog(logWriteVo, "DepObj");
                        //更新数据库
                        //更新名字/上级部门/主管
                        dingDept.setName(dingDept.getName());
                        dbDeptVo.setDingParentId(dingDept.getParentid());
                        dbDeptVo.setDingDeptOwner(dingDept.getDeptOwner());
                        dbDeptVo.setCrmDeptOwner(crmEmployeeId);
                        dbDeptVo.setCrmParentId(parentId);
                        dbDeptVo.setCrmDeptId(dbDeptVo.getCrmDeptId());
                        Result<Integer> integerResult = dingDeptMananger.updateDept(dbDeptVo, appId);
                    }
                    stopWatch.lap("modifyFxDept");
                    stopWatch.log();
                    RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), deptID.toString(), uuid);

                }
        return Result.newSuccess(allCount);
    }

    private Boolean suffixName(String deptName, String dataName) {
        String[] names = dataName.split("__");
        if (deptName.equals(names[0])) {
            return false;
        }
        return true;
    }

    // StringUtils.isNoneBlank(dept.getDeptOwner(),deptVo.getDingDeptOwner()) ||!dept.getDeptOwner().equals(deptVo.getDingDeptOwner()

    private Boolean compareDeptOwner(String deptOwner, String dingOwner) {
        if (StringUtils.isAllBlank(deptOwner, dingOwner)) {
            return false;
        }
        if (deptOwner != null && !deptOwner.equals(dingOwner)) {
            return true;
        }
        if (dingOwner != null && !dingOwner.equals(deptOwner)) {
            return true;
        }
        return false;
    }


    //删除部门（删除部门数据以及crm部门)
    @Override
    public Result<Integer> removeFxDept(Integer ei, List<Long> depts, String appId) {
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        final DingEnterpriseResult data = enterprise.getData();
        if (data.getDevModel().equals(Constant.SYNC_MODELONE)) return Result.newSuccess();

        Integer allCount = 0;
        if (!enterprise.isSuccess()) {
            log.warn("updateDingEmp:the enterprise not bind, ei=[{}].", enterprise);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        for (Long deptID : depts) {
            final String outDeptId = String.valueOf(deptID);
            DeptVo dataBaseDept = dingDeptMananger.queryDeptDetail(data.getEa(), outDeptId, appId);
            if (Objects.nonNull(dataBaseDept)) {
                Result<Integer> integerResult = dingDeptMananger.deleteDept(ei, deptID, appId);
                //
                final String fsDepId = String.valueOf(dataBaseDept.getCrmDeptId());
                if (outerOaDepartmentBindManager.checkFsDeptHasBind(data.getEa(), fsDepId)) {
                    continue;
                }

                allCount++;
                Result<Void> stopResult = crmRestManager.stopDept(dataBaseDept);
                //写入日志
                Integer statusCode = stopResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
                LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.DELETE.getType(), dataBaseDept.getName(), statusCode, stopResult.getErrorMessage());
                logManager.writeLog(logWriteVo, "DepObj");
                //删除数据库
            }
        }
        if (allCount != 0) {
            log.info("delete crm dept ,ei:{},deptIDs:{}", ei, depts);
        }
        return Result.newSuccess(allCount);
    }

//    @Override
//    public Result<Integer> removeDataDept(Integer corpId, Integer id, String appId) {
//        Integer count = dingDeptMananger.fixDept(corpId, id, appId);
//        return Result.newSuccess(count);
//    }

    @Override
    public Result<Void> createDeptOwner(String clientIp, String accessToken, String token, Integer ei, String appId) {
        //请求遍历
        List<DeptVo> baseDept = dingDeptMananger.getDeptByEI(ei, appId);
        //创建部门负责人，默认把负责人在crm创建员工
        if (CollectionUtils.isNotEmpty(baseDept)) {
            log.info("baseDept size:{}", baseDept.size());
            //创建负责人
            for (int i = 0; i < baseDept.size(); i++) {
                DeptVo deptVo = baseDept.get(i);
                Dept dept = DingRequestUtil.queryDeptDetail(ei, clientIp, accessToken, token, deptVo.getDingDeptId());
                if (StringUtils.isNoneEmpty(dept.getDeptOwner())) {
                    deptVo.setDingDeptOwner(dept.getDeptOwner());
                    Integer crmEmpId = null;
                    Result<DingMappingEmployeeResult> crmEmpResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, appId, dept.getDeptOwner());
                    if (ObjectUtils.isNotEmpty(crmEmpResult.getData()) && ObjectUtils.isNotEmpty(crmEmpResult.getData().getEmployeeId())) {
                        //说明员工已经创建
                        crmEmpId = crmEmpResult.getData().getEmployeeId();
                    } else {
                        //创建员工
                        DingMappingEmployeeResult data = crmEmpResult.getData();
                        CreateCrmEmployeeVo createCrmEmployeeVo = new CreateCrmEmployeeVo();
                        createCrmEmployeeVo.setMobile(data.getDingEmployeePhone());
                        createCrmEmployeeVo.setName(data.getDingEmployeeName());
                        createCrmEmployeeVo.setEi(data.getEi());
                        createCrmEmployeeVo.setDingDeptId(data.getDingDeptId());
                        createCrmEmployeeVo.setManagerUserid(data.getManagerUserid());
                        createCrmEmployeeVo.setGender(data.getGender());
                        createCrmEmployeeVo.setId(data.getId().intValue());
                        createCrmEmployeeVo.setUpdateBy(data.getUpdateBy());
                        createCrmEmployeeVo.setDingEmployeeId(data.getDingEmployeeId());
                        createCrmEmployeeVo.setPosition(data.getDingEmployeePosition());
                        createCrmEmployeeVo.setEmail(data.getDingEmployeeEmail());
                        createCrmEmployeeVo.setEmployeeNumber(data.getDingJobNumber());
                        createCrmEmployeeVo.setSexType(data.getDingSexType());
                        List<CreateCrmEmployeeVo> createCrmEmployeeVosList = Lists.newArrayList();
                        createCrmEmployeeVosList.add(createCrmEmployeeVo);
                        batchCreateFxEmployee(createCrmEmployeeVosList, ei, appId);
                        crmEmpResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, appId, dept.getDeptOwner());
                        crmEmpId = crmEmpResult.getData().getEmployeeId();
                    }
                    deptVo.setCrmDeptOwner(crmEmpId);
                    //更新部门信息
                    Result<Void> modifyResult = crmRestManager.modifyDept(deptVo);
                    log.info("trace crmowner modify dept:{}.result:{}", deptVo, modifyResult);
                    if (modifyResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
                        StringBuilder nameBuilder = new StringBuilder(deptVo.getName());
                        nameBuilder.append("__").append(String.valueOf(Math.round((Math.random() + 1) * 1000)));
                        deptVo.setName(nameBuilder.toString());
                        modifyResult = crmRestManager.modifyDept(deptVo);
                    }
                    dingDeptMananger.updateDept(deptVo, appId);
                    //加入日志
                    Integer statusCode = modifyResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
                    LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.UPDATE.getType(), deptVo.getName(), statusCode, modifyResult.getErrorMessage());
                    logManager.writeLog(logWriteVo, "DepObj");
                }

            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> againEmployeeMapping(Integer ei, String appId) {
        StopWatch stopWatch = StopWatch.create("againEmployeeMapping");
        Result<DingEnterpriseResult> enterpriseResult = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        String clientIp = enterpriseResult.getData().getClientIp();

        //获取crm的人员信息
//      //获取全部dingDeptMap
        List<DeptVo> deptList = dingDeptMananger.getDeptByEI(ei, appId);
        Map<Long, DeptVo> deptMaps = deptList.stream().collect(Collectors.toMap(DeptVo::getDingDeptId, Function.identity(), (key1, key2) -> key2));
        QueryEmployeeVo vo = new QueryEmployeeVo();
//        vo.setBindStatus(2);
        Result<ConditionEmployeeResult> result = this.conditionEmployee(vo, ei, appId);
        log.info("condition employee size:{}", result.getData().getDataList().size());
        //获取到list数据
        List<DingMappingEmployeeResult> emps = result.getData().getDataList();
        Map<String, DingMappingEmployeeResult> employeeDtoMap = emps.stream().collect(Collectors.toMap(DingMappingEmployeeResult::getDingEmployeeId, Function.identity(), (key1, key2) -> key2));
        for (int i = 0; i < emps.size(); i++) {
            int index = i;
            StringBuilder employees = new StringBuilder();
            employees.append(emps.get(index));
            log.info("again mapping employee list:{},end：{}", employees, index);
            String accessToken = tokenManager.getToken(ei, appId);
            List<EmployeeDingVo> smartWorkList = DingRequestUtil.getSmartWorkList(ei, employees.toString(), accessToken, clientIp);
            //全量更新
            smartWorkList.stream().forEach(item -> {
                Integer crmParentID = Constant.TREE_PARENT_ID;
                log.info("again mapping item:{}", item);
                DingMappingEmployeeResult mappingResult = employeeDtoMap.get(item.getUserid());
                ModifyEmployee.Argument argument = new ModifyEmployee.Argument();
                argument.setEnterpriseId(ei);
                argument.setCurrentEmployeeId(CURRENT_EMPLOYEE_ID);
                argument.setEmployeeId(mappingResult.getEmployeeId());
                argument.setMobile(item.getMobile());
                if (null != item.getMainDept()) {
                    log.info("this employee not mainDept:{}", item);
                    DeptVo deptVo = deptMaps.get(item.getMainDept());
                    crmParentID = deptVo.getCrmDeptId();
                }
                argument.setMainDepartmentId(crmParentID);
                //附属部门
                List<Integer> depts = treeDeptIds((HashMap<Long, DeptVo>) deptMaps, item.getMainDept(), new ArrayList<Integer>());
                argument.setViceDepartmentIds(depts);
                log.info("againMappingEmployee starting ....:{}", argument);
                ModifyEmployee.Result modifyResult = null;
                try {
                    modifyResult = employeeAdapterService.modifyEmployeeV2(argument);
                    log.info("modify argument ei:{},arg:{},result:{}", ei, argument, modifyResult);
                } catch (Exception e) {
                    log.error("modify argument ei:{},arg:{},result:{}", ei, argument, modifyResult);
                    e.printStackTrace();
                }
            });
//                }
//            });
        }
        stopWatch.lap("againEmployeeMapping");
        stopWatch.log();
        log.info("again mapping list end");
        return null;
    }

    @Override
    public Result<Void> independenceAgainMapping(Integer ei, String appId) {
        //先处理dingDept为0的部门，在crm创建
        DeptVo dataDept = dingDeptMananger.queryByDingId(ei, 347706037L, appId);
        dataDept.setCrmDeptId(8574);
        dingDeptMananger.updateDept(dataDept, appId);
        StopWatch stopWatch = StopWatch.create("againEmployeeMapping");
        Result<DingEnterpriseResult> enterpriseResult = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        String clientIp = enterpriseResult.getData().getClientIp();
        //获取crm的人员信息
//      //获取全部dingDeptMap
        List<DeptVo> deptList = dingDeptMananger.getDeptByEI(ei, appId);
        Map<Long, DeptVo> deptMaps = deptList.stream().collect(Collectors.toMap(DeptVo::getDingDeptId, Function.identity(), (key1, key2) -> key2));
        QueryEmployeeVo vo = new QueryEmployeeVo();
        vo.setBindStatus(2);
        Result<ConditionEmployeeResult> result = this.conditionEmployee(vo, ei, appId);
        log.info("condition employee size:{}", result.getData().getDataList().size());
        //获取到list数据
        List<DingMappingEmployeeResult> emps = result.getData().getDataList();
        Map<String, DingMappingEmployeeResult> employeeDtoMap = emps.stream().collect(Collectors.toMap(DingMappingEmployeeResult::getDingEmployeeId, Function.identity(), (key1, key2) -> key2));
        for (int i = 0; i < emps.size(); i++) {
            DingMappingEmployeeResult employeeResult = emps.get(i);
            executorService.submit(new againMappingTask(employeeAdapterService, deptMaps, ei, employeeResult.getDingEmployeeId(), employeeResult.getEmployeeId(), employeeResult.getDingEmployeePhone(), employeeResult.getDingDeptId(), clientIp, tokenManager.getToken(ei, appId), enterpriseResult.getData()));
        }
        stopWatch.lap("againEmployeeMapping");
        stopWatch.log();
        log.info("again mapping list end");
        return null;
    }

    //统一处理特殊字符或者空格的情况
    private String validName(String name) {
        String match = "[^\\-\\\\/\\[\\]【】()（）_a-zA-Z0-9\\u4E00-\\u9fAF\\u3400-\\u4dBF\\u3300-\\u33FF\\uF900-\\uFAFF]";
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(match);
        // 现在创建 matcher 对象
        Matcher m = r.matcher(name);
        String result = m.replaceAll("-");
        return result;
    }
//    [^\\-·\\[\\]【】()（）_a-zA-Z0-9\u4E00-\u9fAF\u3400-\u4dBF\u3300-\u33FF\uF900-\uFAFF]

    private String employeeValidName(String name) {
        String match = "[^\\-·\\[\\]【】()（）_a-zA-Z0-9\\u4E00-\\u9fAF\\u3400-\\u4dBF\\u3300-\\u33FF\\uF900-\\uFAFF]";
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(match);
        // 现在创建 matcher 对象
        Matcher m = r.matcher(name.trim());
        String result = m.replaceAll("-");
        return result;
    }

    @Override
    public Result<Void> autoSaveEmployeeAccount(CreateUserEventResult createUserEventResult, String appId) {
        List<Integer> users = new LinkedList<>();
        users.add(createUserEventResult.getUser());
        //查询纷享员工
        Result<List<DingMappingEmployeeResult>> fxResult = getEmployeeFs(createUserEventResult.getEi(), users);
        log.info("ObjectMappingServiceImpl.autoSaveEmployeeAccount,fxResult:{}", fxResult);
        if (ObjectUtils.isEmpty(fxResult) || CollectionUtils.isEmpty(fxResult.getData())) {
            return null;
        }
        List<DingMappingEmployeeResult> list = new LinkedList<>();
        for(DingMappingEmployeeResult employeeResult : fxResult.getData()) {
            if(StringUtils.isEmpty(employeeResult.getEmployeePhone())) {
                continue;
            }
            Result<List<DingMappingEmployeeResult>> employeesResult = dingMappingEmployeeManager.findUnbindByDingEmployeePhone(createUserEventResult.getEi(), employeeResult.getEmployeePhone(), appId);
            if(CollectionUtils.isEmpty(employeesResult.getData())) {
                continue;
            }
            for(DingMappingEmployeeResult dingMappingEmployeeResult : employeesResult.getData()) {
                dingMappingEmployeeResult.setEi(createUserEventResult.getEi());
                dingMappingEmployeeResult.setUpdateBy(1000);
                dingMappingEmployeeResult.setCreateBy(1000);
                dingMappingEmployeeResult.setEmployeeStatus(1);
                dingMappingEmployeeResult.setEmployeeId(employeeResult.getEmployeeId());
                dingMappingEmployeeResult.setEmployeeName(employeeResult.getEmployeeName());
                dingMappingEmployeeResult.setEmployeePhone(employeeResult.getEmployeePhone());
                list.add(dingMappingEmployeeResult);
            }
        }
        //将数据保存到员工绑定表
        log.info("ObjectMappingServiceImpl.autoSaveEmployeeAccount,employees:{}", list);
        Integer count = dingMappingEmployeeManager.saveAutoMappingEmployee(list, appId);
        log.info("ObjectMappingServiceImpl.autoSaveEmployeeAccount,ei:{}自动绑定成功count:{}条.", createUserEventResult.getEi(), count);
        return null;
    }

    @Override
    public Result<Void> autoSaveDingEmployeeAccount(Integer ei, List<String> userIds, String appId) {
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        if (enterprise.getData().getAutBind() == 0 || enterprise.getData().getDevModel() == 2) {
            return null;
        }
        Result<List<DingMappingEmployeeResult>> employeesResult;
        if (ObjectUtils.isNotEmpty(userIds)) {
            employeesResult = dingMappingEmployeeManager.queryEmployeesByDingUserIds(ei, userIds, appId);
        } else {
            employeesResult = dingMappingEmployeeManager.queryEmployeesBatch(ei, appId);
        }
        if (ObjectUtils.isEmpty(employeesResult.getData())) {
            log.info("ObjectMappingServiceImpl.autoSaveDingEmployeeAccount,ei:{} userIds:{} appId:{} 自动绑定失败,未查询到员工数据.", ei, userIds, appId);
            return null;
        }
        Result<Map<String, Integer>> mapResult = bindEmployee(employeesResult.getData(), ei, 1000, true, appId);
        if (mapResult.isSuccess()) {
            log.info("ObjectMappingServiceImpl.autoSaveDingEmployeeAccount,ei:{}自动绑定成功count:{}条.", ei, mapResult.getData().get("newEmpCount"));
        }
        return null;
    }

    @Override
    public Result<Boolean> isGrayCorp(String ea) {
        boolean flag = false;
        if (ConfigCenter.DATA_GRAY_TENANTS.contains(ea)) {
            flag = true;
        }
        return Result.newSuccess(flag);
    }

    @Override
    public Result<Boolean> initAgainDept(Integer ei, String appId) throws InterruptedException {
        log.info("pullEmployee dingding emp start ei={}.", ei);
        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseByEi(ei, appId);
        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }

        String appKey = mappingEnterprise.getData().getAppKey();
        String appSecret = mappingEnterprise.getData().getAppSecret();
        //查询部门列表
        ScopeVo scopeVo = DingRequestUtil.queryScoreDeptEmployee(ei, mappingEnterprise.getData().getClientIp(),
                appKey, appSecret, mappingEnterprise.getData().getToken(), tokenManager.getToken(ei, appId));
        if (scopeVo == null) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return Result.newError(ResultCode.DING_CONNECT_PARAM_ERROR);
        }
        if (CollectionUtils.isEmpty(scopeVo.getAuthedDept())) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return Result.newError(ResultCode.DEPT_LIST_ERROR);
        }
        //解析出部门，查询部门信息
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedDept())) {

            //如果scopeVo的部门为1.说明权限是全部门需要遍历全部部门
            List<Dept> depts = new ArrayList<>();
            List<Dept> detailList = Lists.newArrayList();
            if (scopeVo.getAuthedDept().get(0) == 1) {
                detailList = DingRequestUtil.queryDeptList(ei, mappingEnterprise.getData().getClientIp(),
                        appKey, appSecret, mappingEnterprise.getData().getToken(), "1");
                if (CollectionUtils.isEmpty(detailList)) {
                    log.warn("query dept list failed, ei={}, deptResponse={}.", ei, detailList);
                    return Result.newError(ResultCode.DEPT_LIST_ERROR);
                }
            } else {
                for (int i = 0; i < scopeVo.getAuthedDept().size(); i++) {
                    List<Long> deptIdList = new ArrayList<>();
                    //先把父级部门查询出来
                    List<Dept> deptItem = DingRequestUtil.queryDeptList(ei, mappingEnterprise.getData().getClientIp(),
                            appKey, appSecret, mappingEnterprise.getData().getToken(), scopeVo.getAuthedDept().get(i).toString());
                    List<Long> collect = deptItem.stream().map(Dept::getId)
                            .collect(Collectors.toList());
                    deptIdList.add(scopeVo.getAuthedDept().get(i));
                    deptIdList.addAll(collect);
                    depts.addAll(deptItem);
                    for (int j = 0; j < deptIdList.size(); j++) {
                        Dept deptDetail = DingRequestUtil.queryDeptDetail(ei, mappingEnterprise.getData().getClientIp(), tokenManager.getToken(ei, appId), mappingEnterprise.getData().getToken(), deptIdList.get(j));
                        detailList.add(deptDetail);
                    }
                }
                List<Long> scopeCollect = detailList.stream().map(Dept::getId)
                        .collect(Collectors.toList());
                scopeVo.setAuthedDept(scopeCollect);
            }
            List<Dept> orderDepts = ReorderDeptUtils.reorderDingdingDepartments(detailList);
            //创建部门
            for (Dept dept : orderDepts) {
                this.modifyFxDept(ei, Lists.newArrayList(dept.getId()), appId);
                log.info("sync dept:{}", dept.getId());
                Thread.sleep(1000);
            }


        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<DingMappingEmployeeResult>> batchGetDingEmployeesByFsIds(Integer ei, List<Integer> fsEmpIds, String appId) {
        return dingMappingEmployeeManager.batchGetDingEmployeesByFsIds(ei, fsEmpIds, appId);
    }

    @Override
    public Result<List<DingMappingEmployeeResult>> batchGetDingEmployeesByDingIds(Integer ei, List<String> dingEmpIds, String appId) {
        return dingMappingEmployeeManager.batchGetDingEmployeesByDingIds(ei, dingEmpIds, appId);
    }

    @Override
    public Result<Integer> updateEmployeeAccountBindInfo(DingMappingEmployeeResult employeeResult, String appId) {
        //没有绑定不用更新或者绑定操作
        Result<List<DingMappingEmployeeResult>> result = dingMappingEmployeeManager.batchGetDingEmployeesByFsIds(employeeResult.getEi(), Lists.newArrayList(employeeResult.getEmployeeId()), appId);
        if(!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            return Result.newSuccess(0);
        }
        return Result.newSuccess(dingMappingEmployeeManager.updateDingEmpByFsId(employeeResult, appId));
    }

    @Override
    public Result<List<FsUserInfoModel>> getFsUserInfo(String outEa, String outUserId, String appId) {
        List<FsUserInfoModel> fsUserInfoModel = dingMappingEmployeeManager.findByOutUserId(outEa, outUserId, appId);
        return Result.newSuccess(fsUserInfoModel);
    }
}
