package com.facishare.open.ding.provider.mq;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.vo.DingTaskVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.arg.*;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.constants.OAMessageTag;
import com.facishare.open.ding.provider.enums.AlertStatusEnum;
import com.facishare.open.ding.provider.enums.DingTodoTypeEnum;
import com.facishare.open.ding.provider.enums.TodoTypeEnum;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.manager.DingTodoManager;
import com.facishare.open.ding.template.message.*;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/14 16:23 针对钉钉的审批流，代办流同步消息消费
 * @Version 1.0
 */
@Service("dingToDoMessageListener")
@Slf4j
public class DingToDoMessageListener implements MessageListenerOrderly {

    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Autowired
    private DingTodoManager dingTodoManager;
    @Autowired
    private DingSelfDeleteTodoTemplate dingSelfDeleteTodoTemplate;
    @Autowired
    private DingSelfCreateTodoTemplate dingSelfCreateTodoTemplate;
    @Autowired
    private DingSelfDealTodoTemplate dingSelfDealTodoTemplate;
    @Autowired
    private DingSelfSendTextMessageTemplate dingSelfSendTextMessageTemplate;
    @Autowired
    private DingSelfSendCardMessageTemplate dingSelfSendCardMessageTemplate;
    @Autowired
    private EIEAConverter eieaConverter;

    @Override
    public ConsumeOrderlyStatus consumeMessage(List<MessageExt> list, ConsumeOrderlyContext consumeOrderlyContext) {
        for (MessageExt msg : list) {
            try {
                TraceUtils.initTraceId(msg.getMsgId());
                String receiveTags = msg.getTags();
                String ei = msg.getProperty("ei");
                log.info("DingToDoMessageListener.consumeMessage,ei={}", ei);
                final String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(ei));
                Result<List<DingEnterpriseResult>> enterpriseBindResult = dingEnterpriseManager.queryAllEnterpriseResult(ea);
                log.info("DingToDoMessageListener.consumeMessage,enterpriseResult={}", enterpriseBindResult);
                if (!enterpriseBindResult.isSuccess() || CollectionUtils.isEmpty(enterpriseBindResult.getData())) {
                    continue;
                }
                for (DingEnterpriseResult entity : enterpriseBindResult.getData()) {
                    List<DingTodoTypeEnum> supportTypes = Lists.newArrayList();
                    switch (receiveTags) {
                        case OAMessageTag.CREATE_TO_DO_TAG:
                            handleCreateTodoTag(msg, supportTypes, entity);
                            break;
                        case OAMessageTag.DEAL_TO_DO_TAG:
                            handleDealTodoTag(msg, supportTypes, entity);
                            break;
                        case OAMessageTag.DELETE_TO_DO:
                            handlerDeleteTodoTag(msg, supportTypes, entity);
                            break;
                        case OAMessageTag.TEXT_MSG_TAG:
                            SendTextMessageArg sendTextMessageArg
                                    = JSONObject.parseObject(msg.getBody(), SendTextMessageArg.class);
                            log.info("consumeMessage,TEXT_MSG_TAG={}", sendTextMessageArg);
                            if (sendTextMessageArg != null) {
                                log.info("consumeMessage,sendTextMessageArg={}", sendTextMessageArg);
                            }
//                        externalMessageService.sendTextMessage(sendTextMessageArg);
                            SendTextMessageContextArg sendTextMessageContextArg = SendTextMessageContextArg.builder().sendTextMessageArg(sendTextMessageArg).dingEnterpriseResult(entity).build();

                            dingSelfSendTextMessageTemplate.execute(sendTextMessageContextArg);
                            break;
                        case OAMessageTag.CARD_MSG_TAG:
                            SendTextCardMessageArg sendTextCardMessageArg
                                    = JSONObject.parseObject(msg.getBody(), SendTextCardMessageArg.class);
                            log.info("consumeMessage,CARD_MSG_TAG={}", sendTextCardMessageArg);
                            if (sendTextCardMessageArg != null) {
                                log.info("consumeMessage,sendTextCardMessageArg={}", sendTextCardMessageArg);
                            }
//                        externalMessageService.sendTextCardMessage(sendTextCardMessageArg);
                            SendTextCardContextArg sendTextCardContextArg = SendTextCardContextArg.builder().sendTextCardMessageArg(sendTextCardMessageArg).dingEnterpriseResult(entity).build();

                            dingSelfSendCardMessageTemplate.execute(sendTextCardContextArg);

                            break;
                        default:
                            break;

                    }
                }
            } catch (Exception e) {
                //失败不直接返回，因为这是批量消费的线程
                log.info("consumeMessage.consumeMessage,msgId={}", msg.getMsgId());
                log.error("QiXinMsgListener consume  failed.", e);
//                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
        }
        return ConsumeOrderlyStatus.SUCCESS;
    }

    // 处理 CREATE_TO_DO_TAG 类型的消息
    private void handleCreateTodoTag(MessageExt msg, List<DingTodoTypeEnum> supportTypes, DingEnterpriseResult entity) {
        CreateTodoArg createTodoArg = JSONObject.parseObject(msg.getBody(), CreateTodoArg.class);
        if (createTodoArg == null) {
            log.warn("Invalid CreateTodoArg: {}", msg.getMsgId());
            return;
        }
        log.info("consumeMessage,createTodoArg={}", createTodoArg);

        if (!isAllowBizType(createTodoArg.getBizType())) {
            return;
        }

        // 推送待办
        if (isPushTodo(entity, DingTodoTypeEnum.DING_TODO, OAMessageTag.CREATE_TO_DO_TAG, createTodoArg.getSourceId())) {
            supportTypes.add(DingTodoTypeEnum.DING_TODO);
        }
        // 推送工作通知
        if (isPushTodo(entity, DingTodoTypeEnum.DING_WORK, OAMessageTag.CREATE_TO_DO_TAG, createTodoArg.getSourceId())) {
            supportTypes.add(DingTodoTypeEnum.DING_WORK);
        }

        // 执行待办模板
        CreateTodoContextArg createTodoContextArg = CreateTodoContextArg.builder()
                .createTodoArg(createTodoArg)
                .dingEnterpriseResult(entity)
                .needSupportTypes(supportTypes)
                .build();
        dingSelfCreateTodoTemplate.execute(createTodoContextArg);
    }

    // 处理 DEAL_TO_DO_TAG 类型的消息
    private void handleDealTodoTag(MessageExt msg, List<DingTodoTypeEnum> supportTypes, DingEnterpriseResult entity) {
        DealTodoArg dealTodoArg = JSONObject.parseObject(msg.getBody(), DealTodoArg.class);
        if (dealTodoArg == null) {
            log.warn("Invalid DealTodoArg: {}", msg.getMsgId());
            return;
        }
        log.info("consumeMessage,dealTodoArg={}", dealTodoArg);

        if (!isAllowBizType(dealTodoArg.getBizType())) {
            return;
        }

        // 推送待办
        if (isPushTodo(entity, DingTodoTypeEnum.DING_TODO, OAMessageTag.DEAL_TO_DO_TAG, dealTodoArg.getSourceId())) {
            supportTypes.add(DingTodoTypeEnum.DING_TODO);
        }
        // 推送工作通知
        if (isPushTodo(entity, DingTodoTypeEnum.DING_WORK, OAMessageTag.DEAL_TO_DO_TAG, dealTodoArg.getSourceId())) {
            supportTypes.add(DingTodoTypeEnum.DING_WORK);
        }

        // 执行待办模板
        DealTodoContextArg dealTodoContextArg = DealTodoContextArg.builder()
                .dealTodoArg(dealTodoArg)
                .dingEnterpriseResult(entity)
                .needSupportTypes(supportTypes)
                .build();
        dingSelfDealTodoTemplate.execute(dealTodoContextArg);
    }


    private void handlerDeleteTodoTag(MessageExt msg, List<DingTodoTypeEnum> supportTypes, DingEnterpriseResult entity) {
        DeleteTodoArg deleteTodoArg
                = JSONObject.parseObject(msg.getBody(), DeleteTodoArg.class);

        if (deleteTodoArg != null) {
            log.info("consumeMessage,deleteTodoArg={}", deleteTodoArg);
        }

        //待办类型
        if (!isAllowBizType(deleteTodoArg.getBizType())) {
            return;
        }

        //待办推送
        if (isPushTodo(entity, DingTodoTypeEnum.DING_TODO, OAMessageTag.DELETE_TO_DO, deleteTodoArg.getSourceId())) {
            supportTypes.add(DingTodoTypeEnum.DING_TODO);

        }
        // 推送工作通知
        if (isPushTodo(entity, DingTodoTypeEnum.DING_WORK, OAMessageTag.DEAL_TO_DO_TAG, deleteTodoArg.getSourceId())) {
            supportTypes.add(DingTodoTypeEnum.DING_WORK);
        }
        // 执行待办模板
        DeleteTodoContextArg deleteTodoContextArg = DeleteTodoContextArg.builder()
                .deleteTodoArg(deleteTodoArg)
                .dingEnterpriseResult(entity)
                .needSupportTypes(supportTypes)
                .build();
        dingSelfDeleteTodoTemplate.execute(deleteTodoContextArg);
    }


    //拦截待办类型
    private Boolean isAllowBizType(String bizType) {
        boolean flag = false;
        if (ConfigCenter.CRM_TO_BIZ_TYPES.contains(bizType)) {
            flag = true;
        }
        log.info("DingToDoMessageListener.isAllowBizType,bizType={}", bizType);
        return flag;
    }

    //拦截待办推送位置
    private Boolean isPushTodo(DingEnterpriseResult enterpriseResult, DingTodoTypeEnum dingTodoTypeEnum, String messageTag, String sourceId) {
        boolean flag = Boolean.FALSE;
        final String appId = enterpriseResult.getAppKey();
        if (dingTodoTypeEnum.equals(DingTodoTypeEnum.DING_TODO)) {
            //钉钉待办
            if (!(enterpriseResult.getAlertStatus().equals(AlertStatusEnum.TODO_STATUS.getStatus()) || enterpriseResult.getAlertStatus().equals(AlertStatusEnum.TODO_AND_REMIND_STATUS.getStatus()))) {
                //为防止关闭时仍有待办未处理的
                if (messageTag.equals(OAMessageTag.DEAL_TO_DO_TAG) || messageTag.equals(OAMessageTag.DELETE_TO_DO)) {
                    List<DingTaskVo> dingTaskVo = dingTodoManager.getDingTaskVo(enterpriseResult.getEi(), sourceId, appId);
                    if (CollectionUtils.isNotEmpty(dingTaskVo)) {
                        flag = Boolean.TRUE;
                    }
                }
            } else {
                //是否需要推送到钉钉待办
                if (!(TodoTypeEnum.DING_TODO.getStatus().equals(enterpriseResult.getTodoType()) || TodoTypeEnum.DING_TODO_AND_WORK.getStatus().equals(enterpriseResult.getTodoType()))) {
                    //为防止关闭时仍有待办未处理的
                    if (messageTag.equals(OAMessageTag.DEAL_TO_DO_TAG) || messageTag.equals(OAMessageTag.DELETE_TO_DO)) {
                        List<DingTaskVo> dingTaskVo = dingTodoManager.getDingTaskVo(enterpriseResult.getEi(), sourceId, appId);
                        if (CollectionUtils.isNotEmpty(dingTaskVo)) {
                            flag = Boolean.TRUE;
                        }
                    }
                } else {
                    flag = Boolean.TRUE;
                }
            }
        } else if (dingTodoTypeEnum.equals(DingTodoTypeEnum.DING_WORK)) {
            //工作通知
            if (AlertStatusEnum.TODO_STATUS.getStatus().equals(enterpriseResult.getAlertStatus()) || AlertStatusEnum.TODO_AND_REMIND_STATUS.getStatus().equals(enterpriseResult.getAlertStatus())) {
                if (TodoTypeEnum.DING_WORK.getStatus().equals(enterpriseResult.getTodoType()) || TodoTypeEnum.DING_TODO_AND_WORK.getStatus().equals(enterpriseResult.getTodoType())) {
                    flag = Boolean.TRUE;
                }
            }
        }
        log.info("DingToDoMessageListener.isAllowBizType,ea={},alertStatus={},todoType={},dingTodoTypeEnum={},MessageTag={},sourceId={}", enterpriseResult.getEa(), enterpriseResult.getAlertStatus(), enterpriseResult.getTodoType(), dingTodoTypeEnum, messageTag, sourceId);
        return flag;
    }
}
