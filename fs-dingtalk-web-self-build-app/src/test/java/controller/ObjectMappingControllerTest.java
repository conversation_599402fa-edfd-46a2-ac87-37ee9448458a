package controller;

import base.BaseAbstractTest;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.vo.BindEmpVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.web.base.UserContextHolder;
import com.facishare.open.ding.web.base.UserVo;
import com.facishare.open.ding.web.controller.ObjectMappingController;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

@Slf4j
public class ObjectMappingControllerTest extends BaseAbstractTest {
    @Autowired
    private ObjectMappingController objectMappingController;

    @Autowired
    private ObjectMappingService objectMappingService;
    @Test
    public void testQueryEmpBind(){
        BindEmpVo vo = new BindEmpVo();
        vo.setBindStatus(0);
        vo.setPageNumber(1);


        UserVo userVo = UserContextHolder.get().get();
        if (userVo == null) {
            log.warn("纷享用户为空，queryNewEmployee userVo=[{}].", userVo);
            System.out.println("空的");
        }


        String fsUserAccount = "E." + userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();

        Result<Map<String, Object>> result = objectMappingService.queryNewEmployee(vo, userVo.getEnterpriseId(), userVo.getEnterpriseAccount(), userVo.getEmployeeId(), null);
    }

    @Test
    public void testV(){
        Result<Map<String,Object>> result = objectMappingController.queryEmployeeBind(new BindEmpVo());

//        log.info("result = " + result);
    }

}
