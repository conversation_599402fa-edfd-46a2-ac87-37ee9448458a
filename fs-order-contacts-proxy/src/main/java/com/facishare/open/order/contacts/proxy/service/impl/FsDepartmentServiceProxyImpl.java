package com.facishare.open.order.contacts.proxy.service.impl;

import com.facishare.open.order.contacts.proxy.api.arg.FsDeptArg;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.result.ResultCodeEnum;
import com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.ModifyNameUtils;
import com.facishare.open.order.contacts.proxy.config.ConfigCenter;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.GetAllDepartmentDtoArg;
import com.facishare.organization.api.model.department.arg.GetChildrenDepartmentDtoArg;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetAllDepartmentDtoResult;
import com.facishare.organization.api.model.department.result.GetChildrenDepartmentDtoResult;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("fsDepartmentServiceProxy")
public class FsDepartmentServiceProxyImpl implements FsDepartmentServiceProxy {
    @Resource
    private FsEmployeeAndDepartmentProxy fsEmployeeAndDepartmentProxy;
    @Resource
    private DepartmentProviderService departmentProviderService;

    public static final String dep_prefix="D-FSQYWX-";

    @Override
    public Result<ObjectData> create(FsDeptArg arg) {
        LogUtils.info("FsDepartmentServiceProxyImpl.create,arg={}",arg);
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/DepartmentObj/action/Add";

        Map<String,Object> objectData= Maps.newHashMap();
        objectData.put("object_describe_api_name","DepartmentObj");
        String name = ModifyNameUtils.departmentValidName(arg.getName());
        objectData.put("name", name);
        objectData.put("dept_code",arg.getCode());
        objectData.put("record_type","default__c");
        objectData.put("status", StringUtils.isNotEmpty(arg.getStatus()) ? arg.getStatus() : "0");
        objectData.put("manager_id",arg.getManagerId());
        objectData.put("parent_id",arg.getParentId());
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);

        LogUtils.info("FsDepartmentServiceProxyImpl.create,objectData={}",objectData);
        Result<ObjectData> result = fsEmployeeAndDepartmentProxy.postUrl(url,paramMap,arg.getEi());
        LogUtils.info("FsDepartmentServiceProxyImpl.create,result={}",result);
        if(!result.isSuccess() && result.getCode() == ResultCodeEnum.DEPT_NAME_IS_EXIST.getCode()) {
            if(StringUtils.isNotEmpty(name)) {
                if(StringUtils.startsWith(name,dep_prefix)) {
                    LogUtils.info("FsDepartmentServiceProxyImpl.create,qywx dep name is exist",result);
                } else {
                    String nameExtend = ModifyNameUtils.nameExtend(null);
                    objectData.put("name", name.concat(nameExtend));
                }
            }
            paramMap.put("object_data", objectData);
            LogUtils.info("FsDepartmentServiceProxyImpl.create2,objectData={}",objectData);
            result = fsEmployeeAndDepartmentProxy.postUrl(url,paramMap,arg.getEi());
            LogUtils.info("FsDepartmentServiceProxyImpl.create2,result={}",result);
        }
        return result;
    }

    @Override
    public Result<Void> update(FsDeptArg arg) {
        LogUtils.info("FsDepartmentServiceProxyImpl.update,arg={}",arg);
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/DepartmentObj/action/IncrementUpdate";

        Map<String,Object> objectData= new HashMap<>();
        objectData.put("object_describe_api_name","DepartmentObj");
        objectData.put("_id",arg.getId());
        String name = ModifyNameUtils.departmentValidName(arg.getName());
        if(StringUtils.isNotEmpty(arg.getName())) {
            objectData.put("name", name);
        }

        objectData.put("record_type","default__c");
        if(StringUtils.isNotEmpty(arg.getStatus())) {
            objectData.put("status", arg.getStatus());
        }

        if(CollectionUtils.isNotEmpty(arg.getManagerId())) {
            objectData.put("manager_id",arg.getManagerId());
        }
        if(CollectionUtils.isNotEmpty(arg.getParentId())) {
            objectData.put("parent_id",arg.getParentId());
        }

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("data", objectData);

        LogUtils.info("FsDepartmentServiceProxyImpl.update,paramMap={}",paramMap);
        Result<Void> result = fsEmployeeAndDepartmentProxy.postUrl2(url,paramMap,arg.getEi());
        LogUtils.info("FsDepartmentServiceProxyImpl.update,result={}",result);
        if(!result.isSuccess() && result.getCode() == ResultCodeEnum.DEPT_NAME_IS_EXIST.getCode()) {
            objectData.remove("name");
            paramMap.put("data", objectData);
            LogUtils.info("FsDepartmentServiceProxyImpl.update2,objectData={}",objectData);
            result = fsEmployeeAndDepartmentProxy.postUrl2(url,paramMap,arg.getEi());
            LogUtils.info("FsDepartmentServiceProxyImpl.update2,result={}",result);
        }
        return result;
    }

    @Override
    public Result<Void> bulkStop(String ei,List<String> depIdList) {
        LogUtils.info("FsDepartmentServiceProxyImpl.bulkStop,depIdList={}",depIdList);
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/DepartmentObj/action/BulkStop";

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("dataIds", depIdList);

        LogUtils.info("FsDepartmentServiceProxyImpl.bulkStop,paramMap={}",paramMap);
        Result<Void> result = fsEmployeeAndDepartmentProxy.postUrl2(url,paramMap,ei);
        LogUtils.info("FsDepartmentServiceProxyImpl.bulkStop,result={}",result);
        return result;
    }

    @Override
    public Result<Void> bulkResume(String ei,List<String> depIdList) {
        LogUtils.info("FsDepartmentServiceProxyImpl.bulkResume,ei={},depIdList={}",ei,depIdList);
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/DepartmentObj/action/BulkResume";

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("dataIds", depIdList);

        LogUtils.info("FsDepartmentServiceProxyImpl.bulkResume,paramMap={}",paramMap);
        Result<Void> result = fsEmployeeAndDepartmentProxy.postUrl2(url,paramMap,ei);
        LogUtils.info("FsDepartmentServiceProxyImpl.bulkResume,result={}",result);
        return result;
    }

    @Override
    public Result<Void> toggle(String ei,String id,boolean enable) {
        FsDeptArg arg = FsDeptArg.builder()
                .ei(ei)
                .id(id)
                .status(enable ? "0" : "1")
                .build();
        LogUtils.info("FsDepartmentServiceProxyImpl.toggle,arg={}",arg);
        Result<Void> result = update(arg);
        LogUtils.info("FsDepartmentServiceProxyImpl.toggle,result={}",result);
        return result;
    }

    @Override
    public Result<List<ObjectData>> list(Integer ei,String depId) {
        LogUtils.info("FsDepartmentServiceProxyImpl.list,ei={},depId={}",ei,depId);
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/DepartmentObj/controller/List";

        String param = "{\n" +
                "    \"serializeEmpty\":false,\n" +
                "    \"extractExtendInfo\":true,\n" +
                "    \"object_describe_api_name\":\"DepartmentObj\",\n" +
                "    \"search_template_id\":\"5fe311e310a3c8000102d360\",\n" +
                "    \"include_describe\":false,\n" +
                "    \"include_layout\":false,\n" +
                "    \"search_template_type\":\"default\",\n" +
                "    \"ignore_scene_record_type\":false,\n" +
                "    \"search_query_info\":\"{\\\"limit\\\":10000,\\\"offset\\\":0,\\\"filters\\\":[],\\\"orders\\\":[{\\\"fieldName\\\":\\\"last_modified_time\\\",\\\"isAsc\\\":false}]}\",\n" +
                "    \"pageSizeOption\":[\n" +
                "        20\n" +
                "    ],\n" +
                "    \"management\":true\n" +
                "}";

        Result<List<ObjectData>> result = fsEmployeeAndDepartmentProxy.postUrl3(url, param, ei + "");
        LogUtils.info("FsDepartmentServiceProxyImpl.list,result={}",result);
        List<ObjectData> dataList = new ArrayList<>();
        for(ObjectData objectData : result.getData()) {
            if(StringUtils.equalsIgnoreCase(objectData.getId(),depId) || StringUtils.contains(objectData.getString("dept_parent_path"),depId)) {
                dataList.add(objectData);
            }
        }
        //根据 dept_parent_path 字段进行正序排列
        dataList = dataList.stream()
                .sorted((d1,d2)->StringUtils.compareIgnoreCase(d1.getString("dept_parent_path"),d2.getString("dept_parent_path")))
                .collect(Collectors.toList());
        LogUtils.info("FsDepartmentServiceProxyImpl.list,dataList={}",dataList);
        return Result.newSuccess(dataList);
    }

    @Override
    public Result<ObjectData> detail(int ei, String depId) {
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/DepartmentObj/controller/Detail";
        String param = "{\"management\":true,\"objectDataId\":\"{depId}\",\"objectDescribeApiName\":\"DepartmentObj\",\"keepAllMultiLangValue\":true}";
        param = param.replace("{depId}",depId);
        LogUtils.info("FsDepartmentServiceProxyImpl.detail,param={}",param);
        Result<ObjectData> result = fsEmployeeAndDepartmentProxy.postUrl4(url,param,ei+"");
        LogUtils.info("FsDepartmentServiceProxyImpl.detail,ei={},depId={},result={}",ei,depId,result);
        return result;
    }

    @Override
    public Result<List<DepartmentDto>> getAllDepartmentDto(Integer ei) {
        GetAllDepartmentDtoArg arg = new GetAllDepartmentDtoArg();
        arg.setEnterpriseId(ei);
        arg.setRunStatus(RunStatus.ALL);
        GetAllDepartmentDtoResult result = departmentProviderService.getAllDepartmentDto(arg);
        return Result.newSuccess(result.getDepartments());
    }

    @Override
    public Result<List<DepartmentDto>> getChildrenDepartment(Integer ei, Integer departmentId) {
        GetChildrenDepartmentDtoArg arg = new GetChildrenDepartmentDtoArg();
        arg.setEnterpriseId(ei);
        arg.setRunStatus(RunStatus.ALL);
        arg.setDepartmentId(departmentId);
        GetChildrenDepartmentDtoResult result = departmentProviderService.getChildrenDepartment(arg);
        return Result.newSuccess(result.getDepartmentDtos());
    }

    @Override
    public Result<DepartmentDto> getDepartmentDto(Integer ei, Integer departmentId) {
        GetDepartmentDtoArg arg = new GetDepartmentDtoArg();
        arg.setEnterpriseId(ei);
        arg.setDepartmentId(departmentId);
        GetDepartmentDtoResult departmentDtoResult = departmentProviderService.getDepartmentDto(arg);
        return Result.newSuccess(departmentDtoResult.getDepartment());
    }
}
