package com.facishare.open.order.contacts.proxy.api.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DealCrmTodoArg implements Serializable {
    private String taskId;
    private String actionType;
    private String opinion;
    private String entityId;
    private String objectId;
    private Boolean blockWithLayout;
}
