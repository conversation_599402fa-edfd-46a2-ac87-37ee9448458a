package com.facishare.open.order.contacts.proxy.api.aop;

import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.google.common.base.Stopwatch;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 服务方法拦截器
 * <AUTHOR>
 */
@Component
public class ServiceAspect {
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        Object object = null;
        String methodFullName = proceedingJoinPoint.getTarget().getClass().getName()+"."+proceedingJoinPoint.getSignature().getName();
        String args = arrayToString(proceedingJoinPoint.getArgs());
        LogUtils.info("ServiceAspect.around,{},args={}",methodFullName,args);
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            object = proceedingJoinPoint.proceed();
            long ms=stopwatch.elapsed(TimeUnit.MILLISECONDS);
            LogUtils.info("ServiceAspect.around,{},args={},return value={},ms={}",methodFullName,
                    args,objectToString(object),ms);
        } catch (Exception e) {
            long ms=stopwatch.elapsed(TimeUnit.MILLISECONDS);
            LogUtils.error("ServiceAspect.around,{},args={},ms={}",methodFullName,
                    args,ms,e);
            throw e;
        }
        return object;
    }

    private static String arrayToString(Object[] a) {
        if (a == null)
            return "null";

        int iMax = a.length - 1;
        if (iMax == -1)
            return "[]";

        StringBuilder b = new StringBuilder();
        b.append('[');
        for (int i = 0; ; i++) {
            String tem = String.valueOf(a[i]);
            if(StringUtils.isNotEmpty(tem) && tem.length() > 1000) {
                tem = tem.substring(0, 1000) + "...";
            }
            b.append(tem);
            if (i == iMax)
                return b.append(']').toString();
            b.append(", ");
        }
    }

    private static String objectToString(Object o) {
        if (o == null)
            return "null";

        String tem = String.valueOf(o);
        if(tem.length() > 1000) {
            tem = tem.substring(0, 1000) + "...";
        }
        return tem;
    }
}