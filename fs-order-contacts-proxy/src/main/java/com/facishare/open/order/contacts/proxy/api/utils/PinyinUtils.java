package com.facishare.open.order.contacts.proxy.api.utils;

import lombok.experimental.UtilityClass;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @IgnoreI18n
 */
@UtilityClass
// IgnoreI18nFile
public class PinyinUtils {

    /**
     * 汉字转换位汉语拼音首字母，英文字符不变
     *
     * @param chinese 汉字
     * @return 拼音
     */
    public String converterToFirstSpell(String chinese) {
        chinese = cleanChar(chinese);
        StringBuilder pinyinName = new StringBuilder();
        char[] nameChar = chinese.toCharArray();
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        for (char c : nameChar) {
            if (c > 128) {
                try {
                    pinyinName.append(PinyinHelper.toHanyuPinyinStringArray(c, defaultFormat)[0].charAt(0));
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    e.printStackTrace();
                }
            }
        }
        return pinyinName.toString();
    }

    /**
     * 将字符串中的中文转化为拼音,英文字符不变
     *
     * @param chinese 汉字
     * @return 拼音
     */
    public String getPingYin(String chinese) {
        chinese = cleanChar(chinese);
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        format.setVCharType(HanyuPinyinVCharType.WITH_V);
        StringBuilder output = new StringBuilder();
        if (chinese.length() > 0 && !"null".equals(chinese)) {
            char[] input = chinese.trim().toCharArray();
            try {
                for (char c : input) {
                    if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                        String[] temp = PinyinHelper.toHanyuPinyinStringArray(c, format);
                        output.append(temp[0]);
                    } else {
                        output.append(c);
                    }
                }
            } catch (BadHanyuPinyinOutputFormatCombination e) {
                e.printStackTrace();
            }
        } else {
            return "*";
        }
        return output.toString();
    }

    /**
     * 清理特殊字符以便得到
     */
    public static String cleanChar(String chinese) {
        // 正则去掉所有字符操作
        chinese = chinese.replaceAll("[\\p{Punct}\\p{Space}]+", "");
        // 正则表达式去掉所有中文的特殊符号
        String regEx = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}<>《》【】‘；：”“’。，、？–]";
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(chinese);
        chinese = matcher.replaceAll("").trim();
        return chinese;
    }
    public static String saveSpecialSymbol(String chinese) {
        // 正则去掉所有字符操作
        // 正则表达式去掉所有中文的特殊符号
        String regEx = "[`~!@#$%^&*+=|{}':;',\\[\\].<>/?~！@#￥%……&*——+|{}<>《》【】‘；：”“’。，、？–]";
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(chinese);
        chinese = matcher.replaceAll("").trim();
        return chinese;
    }


    public static void main(String[] args) {
        String str = "奶牛?(测试)";
        String s = PinyinUtils.cleanChar(str);
        String message = PinyinUtils.converterToFirstSpell(s);
        System.out.println(PinyinUtils.converterToFirstSpell(str));
        System.out.println(str);
        System.out.println(PinyinUtils.getPingYin(str));
    }
}