<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:aop="http://www.springframework.org/schema/aop" 
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd 
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!-- 引入应用配置 -->
    <import resource="classpath:spring/applicationContext.xml"/>
    
    <!-- 启用注解配置 -->
    <context:annotation-config/>
    
    <!-- 组件扫描 -->
    <context:component-scan base-package="com.facishare.open.outer.oa.connector"/>
    
    <!-- 配置测试环境的特殊bean -->
    <bean id="licenseClient" class="org.mockito.Mockito" factory-method="mock">
        <constructor-arg value="com.facishare.paas.license.http.LicenseClient"/>
    </bean>
</beans> 