package com.facishare.open.outer.oa.connector.web.service;

import com.facishare.open.outer.oa.connector.web.model.FeishuJsapiSignature;
import com.facishare.open.outer.oa.connector.web.thirdparty.auth.FeishuAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/1/3 16:29:42
 */
@Service("feishu")
public class FeishuService implements OuterChannelAuthService<FeishuJsapiSignature> {
    @Autowired
    private FeishuAuthService feishuAuthService;

    @Override
    public FeishuJsapiSignature getJsapiSignature(String appId, String fsEa, String outEa, String url) {
        return feishuAuthService.getJsApiSignature(appId, url, fsEa, outEa).getData();
    }
}
