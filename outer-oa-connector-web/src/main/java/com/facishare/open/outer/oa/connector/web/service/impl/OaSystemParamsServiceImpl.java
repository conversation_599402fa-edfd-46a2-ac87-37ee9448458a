package com.facishare.open.outer.oa.connector.web.service.impl;

import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.web.model.admin.CommonExecuteArg;
import com.facishare.open.outer.oa.connector.web.service.CommonExecuteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("getOaSystemParams")
public class OaSystemParamsServiceImpl implements CommonExecuteService {
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Override
    public Result<?> executeLogic(CommonExecuteArg arg) {
        String tenantId = arg.getTenantId();
        String dcId = arg.getParams();
        OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getEntityById(dcId);
        if (entity == null || entity.getConnectParams() == null || entity.getConnectParams().getStandard_oa() == null
                || entity.getConnectParams().getStandard_oa().getSystemParams() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return Result.newSuccess(entity.getConnectParams().getStandard_oa().getSystemParams());
    }
}
