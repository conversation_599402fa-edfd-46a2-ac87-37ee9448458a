package com.facishare.open.outer.oa.connector.web.openconnetor.model;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import lombok.extern.jackson.Jacksonized;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@Jacksonized
@Builder
@AllArgsConstructor
@FieldNameConstants
public class HubInfo {

    private final String name;

    private final String baseUrl;

    private final List<OuterConnector> outerConnectors;

    /**
     * 路由的企业Id，为空则支持所有企业
     */
    private final Set<String> tenantIds;

    private Set<String> supportConnectorApiNames;

    /**
     * 影响检查的顺序
     */
    @Builder.Default
    private int order = 65536;

    public boolean isDefault() {
        return Objects.equals(name, "default");
    }

    synchronized
    private void initSupportConnectorApiNames() {
        if (outerConnectors == null) {
            supportConnectorApiNames = new HashSet<>();
        } else {
            supportConnectorApiNames = outerConnectors.stream().map(v -> v.getApiName()).filter(Objects::nonNull).collect(Collectors.toSet());
        }
    }

    public boolean supportInvoke(String tenantId, String apiName) {//企业为空也过，因为外部还没有tenantId
        return getSupportConnectorApiNames().contains(apiName)
                && (tenantId==null||CollUtil.isEmpty(tenantIds) || tenantIds.contains(tenantId));
    }

    public Set<String> getSupportConnectorApiNames() {
        if (supportConnectorApiNames == null) {
            initSupportConnectorApiNames();
        }
        return supportConnectorApiNames;
    }
}
