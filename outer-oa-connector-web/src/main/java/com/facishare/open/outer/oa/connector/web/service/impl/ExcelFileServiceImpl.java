package com.facishare.open.outer.oa.connector.web.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.configVo.ConstantDb;
import com.facishare.open.oa.base.dbproxy.manager.DescManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDeptDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDeptDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaDeptDataParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.CRMEmployeeFiledEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.open.outer.oa.connector.web.excel.listener.EmployeeBindMappingListener;
import com.facishare.open.outer.oa.connector.web.excel.vo.EmployeeBindMappingVo;
import com.facishare.open.outer.oa.connector.web.excel.vo.EmployeeBindTemplateVo;
import com.facishare.open.outer.oa.connector.web.manager.FileManager;
import com.facishare.open.outer.oa.connector.web.model.excel.DownloadTemplateArg;
import com.facishare.open.outer.oa.connector.web.model.excel.ExportBindDataArg;
import com.facishare.open.outer.oa.connector.web.model.excel.ImportBindDataArg;
import com.facishare.open.outer.oa.connector.web.result.excel.ExcelFileResult;
import com.facishare.open.outer.oa.connector.web.result.excel.ImportResult;
import com.facishare.open.outer.oa.connector.web.service.ExcelFileService;
import com.facishare.open.outer.oa.connector.web.util.ExcelStyleUtil;
import com.facishare.open.outer.oa.connector.web.util.SearchQueryUtils;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataQueryListResult;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel文件处理服务实现
 */
@Service
@Slf4j
public class ExcelFileServiceImpl implements ExcelFileService {

        @Autowired
        private FileManager fileManager;

        @Autowired
        private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

        @Autowired
        private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

        @Autowired
        private OuterOaEmployeeDataManager outerOaEmployeeDataManager;

        @Autowired
        private ObjectDataServiceV3 objectDataServiceV3;

        @Autowired
        private EIEAConverter eieaConverter;

        @Autowired
        private DescManager descManager;

        @Autowired
        private I18NStringManager i18NStringManager;

        @Autowired
        private OuterOaDeptDataManager outerOaDeptDataManager;

        @Autowired
        private OuterOaEmployeeBindMapper outerOaEmployeeBindMapper;

        /**
         * 导出绑定数据为Excel
         */
        @Override
        public Result<ExcelFileResult> exportBindData(String dataCenterId) {
                try {
                        // 获取数据中心信息
                        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager
                                        .getEntityById(dataCenterId);
                        if (enterpriseBindEntity == null) {
                                return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
                        }

                        // 使用分页查询获取所有绑定数据
                        List<Map<String, Object>> allBindData = new ArrayList<>();
                        int pageSize = 100; // 每页大小
                        int offset = 0;
                        boolean hasMore = true;

                        while (hasMore) {
                                List<Map<String, Object>> pageData = outerOaEmployeeBindMapper
                                                .queryEmployeeBindByDcId(dataCenterId, pageSize, offset);

                                if (CollectionUtils.isEmpty(pageData)) {
                                        hasMore = false;
                                } else {
                                        allBindData.addAll(pageData);
                                        offset += pageSize;
                                }
                        }
                        List<OuterOaEmployeeBindEntity> bindEntities = Lists.newArrayList();
                        List<OuterOaEmployeeDataEntity> employeeDataEntities = Lists.newArrayList();
                        for (Map<String, Object> allBindDatum : allBindData) {
                                OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = JSONObject.parseObject(
                                                JSONObject.toJSONString(allBindDatum), OuterOaEmployeeBindEntity.class);
                                OuterOaEmployeeDataEntity outerOaEmployeeDataEntity = JSONObject.parseObject(
                                                JSONObject.toJSONString(allBindDatum), OuterOaEmployeeDataEntity.class);
                                bindEntities.add(outerOaEmployeeBindEntity);
                                employeeDataEntities.add(outerOaEmployeeDataEntity);
                        }

                        Map<String, OuterOaEmployeeDataEntity> outEmpIdToDataMap = employeeDataEntities.stream()
                                        .collect(Collectors.toMap(OuterOaEmployeeDataEntity::getOutUserId,
                                                        entity -> entity, (existing, replacement) -> existing));

                        // 获取纷享员工数据
                        List<String> fsEmpIds = bindEntities.stream().map(OuterOaEmployeeBindEntity::getFsEmpId)
                                        .collect(Collectors.toList());

                        int tenantId = eieaConverter.enterpriseAccountToId(enterpriseBindEntity.getFsEa());
                        HeaderObj headerObj = HeaderObj.newInstance(tenantId, ConstantDb.CRM_OPERATOR_USER_ID);

                        FindV3Arg findV3Arg = new FindV3Arg();
                        findV3Arg.setDescribeApiName("PersonnelObj");
                        List<String> selectFields = Arrays.asList(CRMEmployeeFiledEnum.NAME.getCode(),
                                        CRMEmployeeFiledEnum.PHONE.getCode(),
                                        CRMEmployeeFiledEnum.USER_ID.getCode(),
                                        CRMEmployeeFiledEnum.OWNER_DEPARTMENT.getCode());
                        findV3Arg.setSelectFields(selectFields);

                        // 分批查询，避免一次查询过多
                        List<ObjectData> fsEmployees = new ArrayList<>();
                        List<List<String>> batches = splitList(fsEmpIds, 100);
                        for (List<String> crmUserIds : batches) {
                                // 使用查询条件中的ID列表
                                List<Object> convertUserIds = new ArrayList<>(crmUserIds);
                                String searchQuery = SearchQueryUtils.buildSearchQueryInfo("user_id", convertUserIds, "IN");
                                findV3Arg.setSearchQueryInfo(searchQuery);
                                com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> queryResult = objectDataServiceV3
                                                .queryList(headerObj, findV3Arg);

                                if (queryResult != null && queryResult.isSuccess() && queryResult.getData() != null) {
                                        fsEmployees.addAll(queryResult.getData().getQueryResult().getDataList());
                                }
                        }

                        Map<String, ObjectData> fsEmpIdToDataMap = fsEmployees.stream().collect(Collectors
                                        .toMap(item ->item.getString("user_id"), data -> data, (existing, replacement) -> existing));

                        // 构建导出数据
                        List<EmployeeBindMappingVo> exportData = new ArrayList<>();
                        // 批量获取外部部门
                        OuterOaDeptDataParams outerOaDeptDataParams = OuterOaDeptDataParams.builder()
                                        .outEa(enterpriseBindEntity.getOutEa())
                                        .channel(enterpriseBindEntity.getChannel())
                                        .appId(enterpriseBindEntity.getAppId()).build();
                        List<OuterOaDeptDataEntity> outerOaDeptDataEntities = outerOaDeptDataManager
                                        .getEntities(outerOaDeptDataParams);
                        Map<String, String> outDeptIdName = outerOaDeptDataEntities.stream().collect(Collectors.toMap(
                                        OuterOaDeptDataEntity::getOutDeptId, OuterOaDeptDataEntity::getDeptName));

                        for (OuterOaEmployeeBindEntity bindEntity : bindEntities) {
                                EmployeeBindMappingVo vo = new EmployeeBindMappingVo();
                                vo.setFsEmpId(bindEntity.getFsEmpId());
                                vo.setOutEmpId(bindEntity.getOutEmpId());

                                // 设置纷享员工信息
                                ObjectData fsEmpData = fsEmpIdToDataMap.get(bindEntity.getFsEmpId());
                                if (fsEmpData != null) {
                                        vo.setFsEmpName(fsEmpData.getString(CRMEmployeeFiledEnum.NAME.getCode()));
                                        vo.setFsEmpPhone(fsEmpData.getString(CRMEmployeeFiledEnum.PHONE.getCode()));
                                        vo.setFsEmpDept(fsEmpData
                                                        .getString(CRMEmployeeFiledEnum.OWNER_DEPARTMENT.getCode()));
                                }

                                // 设置外部员工信息
                                OuterOaEmployeeDataEntity outEmpData = outEmpIdToDataMap.get(bindEntity.getOutEmpId());
                                if (outEmpData != null) {
                                        vo.setOutEmpName(outEmpData.getText1());
                                        vo.setOutEmpDept(outDeptIdName.get(outEmpData.getOutDeptId()));
                                }

                                exportData.add(vo);
                        }
                        List<OuterOaEmployeeDataEntity> outerOaEmployeeDataEntities = queryUnboundEmployees(
                                        enterpriseBindEntity.getOutEa(), enterpriseBindEntity.getChannel(),
                                        enterpriseBindEntity.getAppId(), enterpriseBindEntity.getFsEa());

                        // 构建未绑定数据导出列表
                        List<EmployeeBindMappingVo> unboundExportData = new ArrayList<>();
                        for (OuterOaEmployeeDataEntity unboundEntity : outerOaEmployeeDataEntities) {
                                EmployeeBindMappingVo vo = new EmployeeBindMappingVo();
                                // 未绑定数据，纷享员工ID为空
                                vo.setOutEmpId(unboundEntity.getOutUserId());
                                // 设置外部员工信息
                                vo.setOutEmpName(unboundEntity.getText1());
                                vo.setOutEmpDept(outDeptIdName.get(unboundEntity.getOutDeptId()));
                                unboundExportData.add(vo);
                        }

                        // 生成Excel文件
                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                        HorizontalCellStyleStrategy styleStrategy = ExcelStyleUtil.getDefaultStyle();

                        ExcelWriter excelWriter = EasyExcel.write(outputStream).registerWriteHandler(styleStrategy)
                                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
                        String lang = TraceUtils.getLocale();
                        String sheetBindName = i18NStringManager.get(I18NStringEnum.s176, lang,
                                        String.valueOf(tenantId));
                        WriteSheet writeSheet = EasyExcel.writerSheet(sheetBindName).head(EmployeeBindMappingVo.class)
                                        .build();
                        excelWriter.write(exportData, writeSheet);

                        // 添加未绑定数据的sheet
                        String sheetUnboundName = i18NStringManager.get(I18NStringEnum.s177, lang,
                                        String.valueOf(tenantId));
                        WriteSheet unboundWriteSheet = EasyExcel.writerSheet(sheetUnboundName)
                                        .head(EmployeeBindMappingVo.class).build();
                        excelWriter.write(unboundExportData, unboundWriteSheet);

                        excelWriter.finish();
                        // 上传文件
                        String fileName = sheetBindName + System.currentTimeMillis() + ".xlsx";
                        String filePath = fileManager.uploadTempFile(enterpriseBindEntity.getFsEa(), -10000, fileName,
                                        outputStream.toByteArray());
                        // 返回结果
                        ExcelFileResult result = new ExcelFileResult();
                        result.setFileName(fileName);
                        result.setFilePath(filePath);

                        return Result.newSuccess(result);
                } catch (Exception e) {
                        log.error("导出账号绑定数据失败", e);
                        return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
                }
        }

        /**
         * 下载账号绑定模板Excel
         */
        @Override
        public Result<ExcelFileResult> downloadTemplate(DownloadTemplateArg arg) {
                try {
                        // 获取数据中心信息
                        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager
                                        .getEntityById(arg.getCurrentDcId());
                        if (enterpriseBindEntity == null) {
                                return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
                        }

                        // 获取当前用户的语言
                        String lang = TraceUtils.getLocale();
                        String tenantId = eieaConverter.enterpriseAccountToId(enterpriseBindEntity.getFsEa()) + "";

                        // 构建模板数据，只保留表头
                        List<EmployeeBindTemplateVo> templateData = new ArrayList<>();
                        EmployeeBindTemplateVo exampleVo = new EmployeeBindTemplateVo();

                        // 使用国际化支持
                        exampleVo.setFsEmpId(i18NStringManager.get(I18NStringEnum.s110, lang, tenantId));

                        // 生成Excel文件
                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                        HorizontalCellStyleStrategy styleStrategy = ExcelStyleUtil.getDefaultStyle();

                        ExcelWriter excelWriter = EasyExcel.write(outputStream).registerWriteHandler(styleStrategy)
                                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();

                        // 使用国际化的Sheet名称
                        String sheetName = i18NStringManager.get(I18NStringEnum.s181, lang,
                                        tenantId);
                        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).head(EmployeeBindTemplateVo.class)
                                        .build();

                        excelWriter.write(templateData, writeSheet);
                        excelWriter.finish();

                        // 上传文件 - 使用国际化的文件名
                        String fileName = i18NStringManager.get(I18NStringEnum.s181, lang,
                                tenantId)+ ".xlsx";
                        String filePath = fileManager.uploadTempFile(enterpriseBindEntity.getFsEa(), -10000, fileName,
                                        outputStream.toByteArray());

                        // 返回结果
                        ExcelFileResult result = new ExcelFileResult();
                        result.setFileName(fileName);
                        result.setFilePath(filePath);

                        return Result.newSuccess(result);
                } catch (Exception e) {
                        log.error("下载账号绑定模板失败", e);
                        return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
                }
        }

        /**
         * 导入账号绑定数据
         */
        @Override
        public Result<ImportResult> importBindData(ImportBindDataArg arg) throws IOException {
                try {
                        // 获取数据中心信息
                        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager
                                        .getEntityById(arg.getCurrentDcId());
                        if (enterpriseBindEntity == null) {
                                return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
                        }
                        Integer tenantId=eieaConverter.enterpriseAccountToId(enterpriseBindEntity.getFsEa());
                        // 下载文件
                        byte[] fileBytes = fileManager.downloadFile(enterpriseBindEntity.getFsEa(), -10000,
                                        arg.getFilePath());
                        if (fileBytes == null || fileBytes.length == 0) {
                                return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
                        }

                        // 创建监听器
                        EmployeeBindMappingListener listener = new EmployeeBindMappingListener(enterpriseBindEntity,
                                        outerOaEmployeeBindManager,objectDataServiceV3,tenantId);

                        // 读取Excel
                        InputStream inputStream = new ByteArrayInputStream(fileBytes);
                        fileManager.readExcel(inputStream, EmployeeBindMappingVo.class, listener);

                        // 返回导入结果
                        return Result.newSuccess(listener.getImportResult());
                } catch (Exception e) {
                        log.error("导入账号绑定数据失败", e);
                        return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
                }
        }

        /**
         * 将列表分成多个批次
         *
         * @param list      原始列表
         * @param batchSize 批次大小
         * @param <T>       列表元素类型
         * @return 批次列表
         */
        private <T> List<List<T>> splitList(List<T> list, int batchSize) {
                List<List<T>> result = new ArrayList<>();
                if (CollectionUtils.isEmpty(list)) {
                        return result;
                }

                int size = list.size();
                int batchCount = (size + batchSize - 1) / batchSize;

                for (int i = 0; i < batchCount; i++) {
                        int fromIndex = i * batchSize;
                        int toIndex = Math.min(fromIndex + batchSize, size);
                        result.add(list.subList(fromIndex, toIndex));
                }

                return result;
        }

        /**
         * 导出未绑定的数据
         */
        public List<OuterOaEmployeeDataEntity> queryUnboundEmployees(String outEa, ChannelEnum channel, String appId,
                        String fsEa) {
                // 使用分页查询获取所有绑定数据
                List<OuterOaEmployeeDataEntity> outerOaEmployeeDataEntities = Lists.newArrayList();
                int pageSize = 100; // 每页大小
                int offset = 0;
                boolean hasMore = true;
                while (hasMore) {

                        try {
                                List<Map<String, Object>> outerDataMaps = outerOaEmployeeBindManager
                                                .queryUnboundEmployees(channel, outEa, fsEa, appId, pageSize, offset);
                                for (Map<String, Object> outerDataMap : outerDataMaps) {
                                        outerDataMap.remove("out_user_info");
                                        OuterOaEmployeeDataEntity outerOaEmployeeDataEntity = JSONObject.parseObject(
                                                        JSONObject.toJSONString(outerDataMap),
                                                        OuterOaEmployeeDataEntity.class);
                                        outerOaEmployeeDataEntities.add(outerOaEmployeeDataEntity);
                                }
                                if (CollectionUtils.isEmpty(outerDataMaps)) {
                                        hasMore = false;
                                }
                                offset += pageSize;
                        } catch (Exception e) {
                                log.warn("query unbound employees error:{}", e);
                                hasMore = false;
                        }
                }

                return outerOaEmployeeDataEntities;
        }
}