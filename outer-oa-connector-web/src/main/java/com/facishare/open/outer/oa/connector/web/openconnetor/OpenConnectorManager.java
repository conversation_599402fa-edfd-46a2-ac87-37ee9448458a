package com.facishare.open.outer.oa.connector.web.openconnetor;


import com.alibaba.fastjson.TypeReference;
import com.facishare.open.outer.oa.connector.common.api.info.SystemParams;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.web.model.GetConnectorIntroArg;
import com.facishare.open.outer.oa.connector.web.model.OaConnectorAuthType;
import com.facishare.open.outer.oa.connector.web.model.admin.*;

import com.facishare.open.outer.oa.connector.web.openconnetor.model.*;
import com.facishare.open.outer.oa.connector.web.openconnetor.service.*;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/14
 */
@Component
@Slf4j
public class OpenConnectorManager implements ConnectorOaInterfaceService, OpenConnectorCreateTodoService, OpenConnectorDealTodoService, OpenConnectorDeleteTodoService, OpenConnectorSendTextCarMsgService,OpenConnectorSendTextMsgService {
    @Autowired
    private OpenConnectorHubHandler openConnectorHubHandler;

    private <T> Result<T> execute(String tenantId, String dataCenterId, String connectorApiName, InterfaceUrlEnum interEnum, Object requestBody,
                                  TypeReference<T> resType) {
        return openConnectorHubHandler.execute(tenantId,
                dataCenterId,
                interEnum,
                connectorApiName,
                requestBody,
                resType
        );
    }
    @Override
    public Result<List<OaConnectorAuthType>> getConnectorAuthTypeList(String tenantId, String dataCenterId, String connectorApiName) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.getConnectorAuthTypeList;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, null, new TypeReference<List<OaConnectorAuthType>>() {
        });
    }

    @Override
    public Result<String> getOAuth2AuthUrl(String tenantId, String dataCenterId, String connectorApiName, SystemParams arg) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.getOAuth2AuthUrl;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, arg, new TypeReference<String>() {
        });
    }

    @Override
    public Result<SystemParams> processUserInputSystemParams(String tenantId, String dataCenterId, String connectorApiName, SystemParams arg) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.processUserInputSystemParams;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, arg, new TypeReference<SystemParams>() {
        });
    }

    @Override
    public Result<ConnectorIntro> getOaConnectorIntro(String tenantId, String dataCenterId, String connectorApiName, GetConnectorIntroArg arg) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.getOaConnectorIntro;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, arg, new TypeReference<ConnectorIntro>() {
        });
    }

    @Override
    public Result<OuterRedirectMsg> getDoAuthUrlMsg(String tenantId, String dataCenterId, String connectorApiName, DoAuthArg arg) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.getDoAuthUrlMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, arg, new TypeReference<OuterRedirectMsg>() {
        });
    }

    @Override
    public Result<OuterRedirectMsg> getLoginAuthUrlMsg(String tenantId, String dataCenterId, String connectorApiName, OriginRequestArg arg) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.getLoginAuthUrlMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, arg, new TypeReference<OuterRedirectMsg>() {
        });
    }

    @Override
    public Result<OuterCallBackEventMsg> dealWithCallBackEvent(String tenantId, String dataCenterId, String connectorApiName, OriginRequestArg arg) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.dealWithCallBackEvent;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, arg, new TypeReference<OuterCallBackEventMsg>() {
        });
    }


    @Override
    public Result<StdData> queryOaMasterById(String tenantId, String dataCenterId, String connectorApiName, IdArg arg) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.queryOaMasterById;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, arg, new TypeReference<StdData>() {
        });
    }

    @Override
    public Result<StdListData> queryOaMasterBatch(String tenantId, String dataCenterId, String connectorApiName, TimeFilterArg arg) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.queryOaMasterBatch;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, arg, new TypeReference<StdListData>() {
        });
    }

    @Override
    public Result<Void> filterCreateTodoMsg(String tenantId, String dataCenterId, String connectorApiName, CreateTodoArg context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.filterCreateTodoMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, null);
    }

    @Override
    public Result<OuterMsgModel> buildCreateTodoMsg(String tenantId, String dataCenterId, String connectorApiName, CreateTodoArg context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.buildCreateTodoMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, new TypeReference<OuterMsgModel>() {
        });
    }

    @Override
    public Result<Void> sendCreateTodoMsg(String tenantId, String dataCenterId, String connectorApiName, OuterMsgModel context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.sendCreateTodoMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, null);
    }

    @Override
    public Result<Void> filterDealTodoMsg(String tenantId, String dataCenterId, String connectorApiName, DealTodoArg context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.filterDealTodoMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, null);
    }

    @Override
    public Result<OuterMsgModel> buildDealTodoMsg(String tenantId, String dataCenterId, String connectorApiName, DealTodoArg context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.buildDealTodoMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, new TypeReference<OuterMsgModel>() {
        });
    }

    @Override
    public Result<Void> sendDealTodoMsg(String tenantId, String dataCenterId, String connectorApiName, OuterMsgModel context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.sendDealTodoMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, null);
    }

    @Override
    public Result<Void> filterDeleteTodoMsg(String tenantId, String dataCenterId, String connectorApiName, DeleteTodoArg context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.filterDeleteTodoMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, null);
    }

    @Override
    public Result<OuterMsgModel> buildDeleteTodoMsg(String tenantId, String dataCenterId, String connectorApiName, DeleteTodoArg context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.buildDeleteTodoMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, new TypeReference<OuterMsgModel>() {
        });
    }

    @Override
    public Result<Void> sendDeleteTodoMsg(String tenantId, String dataCenterId, String connectorApiName, OuterMsgModel context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.sendDeleteTodoMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, null);
    }

    @Override
    public Result<Void> filterTextCarMsg(String tenantId, String dataCenterId, String connectorApiName, SendTextCardMessageArg context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.filterTextCardMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, null);
    }

    @Override
    public Result<OuterMsgModel> buildTextCarMsg(String tenantId, String dataCenterId, String connectorApiName, SendTextCardMessageArg context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.buildTextCardMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, new TypeReference<OuterMsgModel>() {
        });
    }

    @Override
    public Result<Void> sendTextCarMsg(String tenantId, String dataCenterId, String connectorApiName, OuterMsgModel context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.sendTextCardMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, null);
    }

    @Override
    public Result<Void> filterTextMsg(String tenantId, String dataCenterId, String connectorApiName, SendTextMessageArg context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.filterTextMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, null);
    }

    @Override
    public Result<OuterMsgModel> buildTextMsg(String tenantId, String dataCenterId, String connectorApiName, SendTextMessageArg context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.buildTextMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, new TypeReference<OuterMsgModel>() {
        });
    }

    @Override
    public Result<Void> sendTextMsg(String tenantId, String dataCenterId, String connectorApiName, OuterMsgModel context) {
        InterfaceUrlEnum interEnum = InterfaceUrlEnum.sendTextMsg;
        return execute(tenantId, dataCenterId, connectorApiName, interEnum, context, null);
    }
}
