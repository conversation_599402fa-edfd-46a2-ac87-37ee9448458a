package com.facishare.open.outer.oa.connector.web.openconnetor.model;

import com.facishare.open.outer.oa.connector.common.api.object.StandardDepartmentObject;
import com.facishare.open.outer.oa.connector.common.api.object.StandardEmployeeObject;
import com.facishare.rest.core.util.JacksonUtil;
import com.fxiaoke.crmrestapi.common.data.TypeHashMap;

/**
 *
 * <AUTHOR>
 * @date 2025/5/14
 */
public class OuterObjectData extends TypeHashMap<String, Object> {

    public String getId() {
        return this.getString("_id");
    }
    public void putId(String id) {
        this.put("_id", id);
    }
    public String getName() {
        return this.getString("name");
    }
    public void putName(String name) {
        this.put("name", name);
    }

    public static StandardEmployeeObject change2EmployeeObject(OuterObjectData objectData){
        if(objectData==null){
            return null;
        }
        StandardEmployeeObject object=new StandardEmployeeObject();
        object.setId(objectData.getString("id"));
        object.setUserid(objectData.getString("userid"));
        object.setName(objectData.getString("name"));
        object.setPhone(objectData.getString("phone"));
        object.setDeptId(objectData.getString("deptId"));
        object.setJobNumber(objectData.getString("jobNumber"));
        object.setEmail(objectData.getString("email"));
        object.setExtendValue(JacksonUtil.toJson(objectData));
        return object;
    }
    public static StandardDepartmentObject change2DepartmentObject(OuterObjectData objectData){
        if(objectData==null){
            return null;
        }
        StandardDepartmentObject object=new StandardDepartmentObject();
        object.putAll(objectData);
        object.setDeptId(objectData.getString("deptId"));
        object.setParentId(objectData.getString("parentId"));
        object.setName(objectData.getString("name"));
        object.setSeq(objectData.get("seq")==null?null:Integer.valueOf(objectData.getString("seq")));
        return object;
    }

}
